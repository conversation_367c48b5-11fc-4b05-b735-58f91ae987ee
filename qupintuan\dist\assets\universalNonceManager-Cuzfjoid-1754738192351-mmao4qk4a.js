import{c as m,h as w,b as N}from"./web3-DJUNf-KT-*************-mmao4qk4a.js";import"./vendor-Caz4khA--*************-mmao4qk4a.js";const f={CONFIRMED:"confirmed"};function M(){if(typeof window>"u")return"unknown";const d=navigator.userAgent.toLowerCase(),n=window.ethereum;return d.includes("tokenpocket")||n?.isTokenPocket?"tokenpocket":d.includes("trustwallet")||n?.isTrust?"trust":d.includes("imtoken")||n?.isImToken?"imtoken":n?.isMetaMask?"metamask":d.includes("mobile")?"mobile_unknown":"desktop"}class C{constructor(){this.nonceCache=new Map,this.transactionQueues=new Map,this.pendingCounts=new Map,this.accountLocks=new Map,this.publicClient=null,this.walletType=M(),console.log("🔧 [UniversalNonceManager] 初始化完成，钱包类型:",this.walletType)}getPublicClient(){return this.publicClient||(this.publicClient=m({chain:N,transport:w()})),this.publicClient}async getNextNonce(n,e={}){const t=n.toLowerCase(),{forceRefresh:o=!1,highPriority:s=!1}=e;try{console.log("🔍 [UniversalNonceManager] 获取Nonce:",{address:t,forceRefresh:o,highPriority:s,walletType:this.walletType});const r=await this.getPublicClient().getTransactionCount({address:t,blockTag:"pending"}),g=this.nonceCache.get(t)||0,u=this.pendingCounts.get(t)||0;let l;return o?l=r:l=Math.max(r,g)+u,this.nonceCache.set(t,l+1),this.pendingCounts.set(t,u+1),console.log("✅ [UniversalNonceManager] Nonce计算完成:",{address:t,chainNonce:r,cachedNonce:g,pendingCount:u,nextNonce:l,walletType:this.walletType}),l}catch(i){console.error("❌ [UniversalNonceManager] 获取Nonce失败:",i);const r=Date.now()%1e6;return console.warn("⚠️ [UniversalNonceManager] 使用降级Nonce:",r),r}}async lockAccount(n){const e=n.toLowerCase();for(;this.accountLocks.get(e);)await new Promise(t=>setTimeout(t,100));return this.accountLocks.set(e,!0),console.log("🔒 [UniversalNonceManager] 账户已锁定:",e),()=>{this.accountLocks.delete(e),console.log("🔓 [UniversalNonceManager] 账户已解锁:",e)}}markTransactionComplete(n,e,t=f.CONFIRMED){const o=n.toLowerCase(),s=this.pendingCounts.get(o)||0;s>0&&this.pendingCounts.set(o,s-1),console.log("✅ [UniversalNonceManager] 交易完成:",{address:o,txHash:e,status:t,remainingPending:this.pendingCounts.get(o)||0})}clearAccountCache(n){const e=n.toLowerCase();this.nonceCache.delete(e),this.pendingCounts.delete(e),this.accountLocks.delete(e),console.log("🧹 [UniversalNonceManager] 账户缓存已清理:",e)}getAccountStatus(n){const e=n.toLowerCase();return{address:e,cachedNonce:this.nonceCache.get(e)||0,pendingCount:this.pendingCounts.get(e)||0,isLocked:this.accountLocks.get(e)||!1,walletType:this.walletType}}async executeTransaction(n,e,t={}){const o=n.toLowerCase(),{highPriority:s=!1,maxRetries:i=3,retryDelay:r=3e3,onProgress:g,onNonceConflict:u}=t,l=await this.lockAccount(o);try{let p;for(let a=1;a<=i;a++)try{console.log(`🔄 [UniversalNonceManager] 交易尝试 ${a}/${i}:`,o);const c=await this.getNextNonce(o,{forceRefresh:a>1,highPriority:s});g&&g({step:"executing",attempt:a,nonce:c});const h=await e({nonce:c});return console.log("✅ [UniversalNonceManager] 交易成功:",{address:o,attempt:a,nonce:c,txHash:h.hash||h.txHash}),h}catch(c){p=c,console.error(`❌ [UniversalNonceManager] 交易失败 ${a}/${i}:`,c),this.isNonceError(c)&&(console.warn("⚠️ [UniversalNonceManager] 检测到Nonce冲突，清理缓存"),this.clearAccountCache(o),u&&u({attempt:a,error:c})),a<i&&(console.log(`⏳ [UniversalNonceManager] 等待 ${r}ms 后重试...`),await new Promise(h=>setTimeout(h,r)))}throw new Error(`交易失败，已重试 ${i} 次。最后错误: ${p.message}`)}finally{l()}}isNonceError(n){const e=n.message?.toLowerCase()||"";return e.includes("nonce too low")||e.includes("nonce too high")||e.includes("nonce has already been used")||e.includes("replacement transaction underpriced")||e.includes("already known")}getOptimizedGasConfig(n={},e=!1){const t=this.getWalletGasMultiplier(),o=e?1.5:1.2,s={...n};return n.gasPrice&&(s.gasPrice=BigInt(Math.floor(Number(n.gasPrice)*t*o))),n.maxFeePerGas&&(s.maxFeePerGas=BigInt(Math.floor(Number(n.maxFeePerGas)*t*o))),n.maxPriorityFeePerGas&&(s.maxPriorityFeePerGas=BigInt(Math.floor(Number(n.maxPriorityFeePerGas)*t*o))),n.gas&&(s.gas=BigInt(Math.floor(Number(n.gas)*1.2))),console.log("⛽ [UniversalNonceManager] Gas配置优化:",{walletType:this.walletType,isFirstTransaction:e,walletMultiplier:t,priorityMultiplier:o,original:n,optimized:s}),s}getWalletGasMultiplier(){switch(this.walletType){case"tokenpocket":return 1.3;case"trust":return 1.25;case"imtoken":return 1.25;case"mobile_unknown":return 1.3;case"metamask":return 1.1;default:return 1.2}}}const T=new C;export{f as TRANSACTION_STATUS,T as default,M as detectWalletType,T as universalNonceManager};
