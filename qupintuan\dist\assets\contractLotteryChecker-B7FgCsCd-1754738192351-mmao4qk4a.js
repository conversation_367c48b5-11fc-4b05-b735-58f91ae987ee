const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/basicOperations-CmXjWg1V-1754738192351-mmao4qk4a.js","assets/vendor-Caz4khA--1754738192351-mmao4qk4a.js","assets/index-DmsQauDE-1754738192351-rjh9jemju.js","assets/web3-DJUNf-KT-1754738192351-mmao4qk4a.js","assets/index-Dcug_PHz-1754738192351-gaytee33w.css","assets/groupBuyService-Cr4Xc-AR-1754738192351-mmao4qk4a.js","assets/core-sFjn417F-1754738192351-mmao4qk4a.js","assets/roomManagement-CRZ0InUC-1754738192351-mmao4qk4a.js","assets/rewardOperations-2IHLUMdh-1754738192351-mmao4qk4a.js","assets/transaction-D--126Ts-1754738192351-mmao4qk4a.js"])))=>i.map(i=>d[i]);
import{_ as u}from"./vendor-Caz4khA--1754738192351-mmao4qk4a.js";async function d(t){try{console.log(`🔍 直接查询合约中房间 #${t} 的开奖信息...`);const{fetchRoom:s}=await u(async()=>{const{fetchRoom:n}=await import("./basicOperations-CmXjWg1V-1754738192351-mmao4qk4a.js");return{fetchRoom:n}},__vite__mapDeps([0,1])),e=await s({chainId:97,roomId:t});console.log(`📊 房间 #${t} 完整信息:`,{id:e.id,creator:e.creator,participantsCount:e.participants?.length||0,isClosed:e.isClosed,isSuccessful:e.isSuccessful,readyForWinner:e.readyForWinner,winner:e.winner,winnerAddress:e.winnerAddress,lotteryTxHash:e.lotteryTxHash,lotteryTimestamp:e.lotteryTimestamp,timeLeft:e.timeLeft,isExpired:e.isExpired,status:e.status});let i=null;try{const n=localStorage.getItem(`lottery_${t}`);n?(i=JSON.parse(n),console.log(`💾 房间 #${t} localStorage开奖信息:`,i)):console.log(`💾 房间 #${t} localStorage中无开奖数据`)}catch(n){console.warn(`💾 房间 #${t} localStorage检查失败:`,n)}const r={roomId:t,creator:e.creator,participantsCount:e.participants?.length||0,isClosed:e.isClosed,isSuccessful:e.isSuccessful,timeLeft:e.timeLeft,step1_readyForWinner:!!e.readyForWinner,step1_hasLocalLotteryInfo:!!i,step1_isHistoricalMalicious:!!e._isMaliciousLottery,step2_hasWinner:!!(e.winner&&e.winner!=="0x0000000000000000000000000000000000000000"),step2_isSuccessful:!!(e.isSuccessful&&e.isSuccessful!=="0x0000000000000000000000000000000000000000"),isViolation:!1},l=r.step1_readyForWinner||r.step1_hasLocalLotteryInfo||r.step1_isHistoricalMalicious,o=r.step2_hasWinner||r.step2_isSuccessful,a=e.timeLeft<=0;let c=!1;if(e.createTime&&!a){const n=Date.now(),p=e.createTime instanceof Date?e.createTime.getTime():new Date(e.createTime).getTime(),y=n-p,w=1440*60*1e3;c=y>w}const m=a||c,f=l&&!o;return r.isViolation=m&&f,r}catch(s){throw console.error(`查询房间 #${t} 合约信息失败:`,s),s}}async function x(t){const s=[];for(const e of t)try{const i=await d(e);s.push(i)}catch(i){s.push({roomId:e,error:i.message,isViolation:!1})}return s.filter(e=>e.isViolation),s}function _(){console.log("🧪 开始测试违规检测逻辑..."),u(async()=>{const{isMaliciousLotteryRoom:t}=await import("./index-DmsQauDE-1754738192351-rjh9jemju.js").then(s=>s.m);return{isMaliciousLotteryRoom:t}},__vite__mapDeps([2,1,3,4])).then(({isMaliciousLotteryRoom:t})=>{const s={id:"test1",isClosed:!0,isSuccessful:"0x0000000000000000000000000000000000000000",participants:new Array(8).fill("0x123"),readyForWinner:!1,timeLeft:3600},e={id:"test2",isClosed:!0,isSuccessful:"0x0000000000000000000000000000000000000000",participants:new Array(8).fill("0x123"),readyForWinner:!0,timeLeft:3600},i={id:"test3",isClosed:!0,isSuccessful:"0x456789abcdef",participants:new Array(8).fill("0x123"),readyForWinner:!0,winner:"0x456789abcdef",timeLeft:0};localStorage.setItem("lottery_test4",JSON.stringify({txHash:"0xabc123",winner:{address:"0x789"},timestamp:Date.now()}));const r={id:"test4",isClosed:!0,isSuccessful:"0x0000000000000000000000000000000000000000",participants:new Array(8).fill("0x123"),readyForWinner:!1,timeLeft:3600};console.log("📊 测试结果:"),console.log("1. 正常过期房间:",t(s),"(应该是 false)"),console.log("2. 故意不开奖房间:",t(e),"(应该是 true)"),console.log("3. 正常完成房间:",t(i),"(应该是 false)"),console.log("4. 有本地开奖信息但未完成:",t(r),"(应该是 true)"),localStorage.removeItem("lottery_test4"),console.log("✅ 违规检测逻辑测试完成")})}async function g(){console.log("🚀 开始查询所有过期房间的违规状态...");try{const e=await(await u(()=>import("./groupBuyService-Cr4Xc-AR-1754738192351-mmao4qk4a.js"),__vite__mapDeps([5,2,1,3,4,6,0,7,8,9]))).default.fetchRooms({chainId:97,currentUserAddress:null,isMyRoomsPage:!1});if(!e||!e.rooms)return console.log("❌ 无法获取房间数据"),[];const i=e.rooms.filter(o=>{const a=o.timeLeft<=0,c=Date.now(),m=o.createTime instanceof Date?o.createTime.getTime():new Date(o.createTime).getTime(),f=c-m,n=1440*60*1e3,p=f>n;return a||p}),{isMaliciousLotteryRoom:r}=await u(async()=>{const{isMaliciousLotteryRoom:o}=await import("./index-DmsQauDE-1754738192351-rjh9jemju.js").then(a=>a.m);return{isMaliciousLotteryRoom:o}},__vite__mapDeps([2,1,3,4])),l=[];for(const o of i){try{if(JSON.parse(localStorage.getItem("exempt_rooms")||"{}").hasOwnProperty(o.id))continue}catch(c){console.warn(`检查房间 #${o.id} 豁免状态失败:`,c)}r(o)&&l.push({roomId:o.id,creator:o.creator,participants:o.participants.length,tier:o.tier,createTime:o.createTime,status:o.status,readyForWinner:o.readyForWinner,isViolation:!0})}return l}catch(t){return console.error("查询过期房间违规状态失败:",t),[]}}typeof window<"u"&&setTimeout(()=>{window.queryExpiredRoomsViolations=g,window.queryContractLotteryInfo=d,window.testViolationDetection=_},2e3);export{x as batchQueryContractLotteryInfo,d as queryContractLotteryInfo,g as queryExpiredRoomsViolations,_ as testViolationDetection};
