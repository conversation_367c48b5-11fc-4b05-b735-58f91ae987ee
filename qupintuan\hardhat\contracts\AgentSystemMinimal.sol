// SPDX-License-Identifier: MIT
pragma solidity ^0.8.22;

import "@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/security/ReentrancyGuardUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/security/PausableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/utils/AddressUpgradeable.sol";
import "@openzeppelin/contracts/governance/TimelockController.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "./StorageValidator.sol";

contract AgentSystem is
    Initializable,
    StorageValidator,
    AccessControlUpgradeable,
    PausableUpgradeable,
    ReentrancyGuardUpgradeable,
    UUPSUpgradeable
{
    using AddressUpgradeable for address;
    bytes32 public constant ADMIN_ROLE = keccak256("ADMIN_ROLE");
    bytes32 public constant PERFORMANCE_UPLOADER = keccak256("PERFORMANCE_UPLOADER");
    bytes32 public constant PAUSER_ROLE = keccak256("PAUSER_ROLE");

    address public systemAdmin;
    TimelockController public timelock;

    // 升级类型枚举
    enum UpgradeType {
        NONE,           // 未升级
        AUTO_UPGRADE,   // 基于业绩自动升级
        ADMIN_SET       // 管理员手动设置
    }

    struct User {
        address inviter;
        uint8 level;
        uint256 totalPerformance;
        address[] referrals;
        uint256 personalPerformance;
        UpgradeType upgradeType;  // 升级类型
        uint256 upgradeTimestamp; // 升级时间戳
    }

    mapping(address => User) public users;
    mapping(address => mapping(address => uint256)) public teamPerformance;
    mapping(address => bool) public blacklist;
    mapping(address => bool) public frozenUsers;

    event Registered(address indexed user, address indexed inviter);
    event PerformanceAdded(address indexed user, uint256 amount);
    event PersonalPerformanceAdded(address indexed user, uint256 amount);
    event LevelUpdated(address indexed user, uint8 newLevel, uint256 totalSmallTeamsPerformance, uint256 validTeams);
    event LevelUpgradeTypeChanged(address indexed user, uint8 level, UpgradeType upgradeType, uint256 timestamp);
    event AdminUpdated(address indexed newAdmin);
    event BlacklistUpdated(address indexed user, bool isBlacklisted);
    event UserFrozen(address indexed user, bool isFrozen);
    event TeamPerformanceAdded(address indexed user, address indexed inviter, uint256 amount);
    event ReferralAdded(address indexed user, address indexed referral);

    modifier notBlacklisted(address user) {
        require(!blacklist[user], "User is blacklisted");
        _;
    }

    modifier notFrozen(address user) {
        require(!frozenUsers[user], "User is frozen");
        _;
    }

    modifier onlyRegistered(address user) {
        require(users[user].inviter != address(0), "User not registered in agent system");
        _;
    }

    modifier onlyUploader() {
        require(hasRole(PERFORMANCE_UPLOADER, msg.sender), "Not uploader");
        _;
    }

    function initialize(address _systemAdmin, TimelockController _timelock) public initializer {
        __AccessControl_init();
        __Pausable_init();
        __ReentrancyGuard_init();
        __UUPSUpgradeable_init();

        systemAdmin = _systemAdmin;
        timelock = _timelock;
        _grantRole(DEFAULT_ADMIN_ROLE, msg.sender);
        _grantRole(ADMIN_ROLE, _systemAdmin);
        _grantRole(PAUSER_ROLE, _systemAdmin);
        _grantRole(ADMIN_ROLE, address(_timelock));
        _grantRole(PAUSER_ROLE, address(_timelock));
    }

    // —— 存储验证实现 —— //

    function validateStorageLayout() public view override returns (bool) {
        // 验证系统管理员地址
        if (systemAdmin == address(0)) return false;
        if (address(timelock) == address(0)) return false;

        return true;
    }

    function calculateStorageChecksum() public view override returns (bytes32) {
        return keccak256(abi.encodePacked(
            systemAdmin,
            address(timelock),
            STORAGE_LAYOUT_VERSION
        ));
    }

    function emergencyStorageFix() external override onlyRole(ADMIN_ROLE) whenPaused {
        // AgentSystem的存储修复逻辑
        emit StorageFixed(address(this), "AgentSystem storage checked");
    }

    function savePreUpgradeState() external override onlyRole(ADMIN_ROLE) {
        // 保存升级前状态
        // 可以在这里添加特定的状态保存逻辑
    }

    function setEmergencyFixMode(bool enabled) external override onlyRole(ADMIN_ROLE) {
        // 设置紧急修复模式
        _emergencyFixMode = enabled;
    }

    function postUpgradeValidation() external override onlyRole(ADMIN_ROLE) {
        // 升级后验证
        require(validateStorageLayout(), "Storage validation failed");
        require(validateBasicState(), "Basic state validation failed");
    }

    function _authorizeUpgrade(address newImplementation) internal override onlyRole(ADMIN_ROLE) {
        _authorizeUpgradeWithValidation(newImplementation);
    }

    function register(address inviter) external nonReentrant whenNotPaused notBlacklisted(msg.sender) notFrozen(msg.sender) {
        require(users[msg.sender].inviter == address(0), "Operation not allowed");
        require(msg.sender != inviter, "Operation not allowed");
        require(inviter.code.length == 0, "Operation not allowed");

        if (inviter == address(0)) {
            inviter = systemAdmin;
        }

        require(users[inviter].inviter != address(0) || inviter == systemAdmin, "Operation not allowed");

        users[msg.sender].inviter = inviter;
        users[inviter].referrals.push(msg.sender);

        emit Registered(msg.sender, inviter);
        emit ReferralAdded(inviter, msg.sender);
    }

    function _upgradeIfEligible(address user) internal {
        uint8 lvl = users[user].level;
        if (lvl >= 4) return;

        address[] storage refs = users[user].referrals;
        uint256 n = refs.length;
        if (n == 0) return;

        uint256 maxIdx = 0;
        uint256[] memory perf = new uint256[](n);
        // 计算每个直推用户的真实总业绩（个人业绩 + 团队业绩）
        for (uint i = 0; i < n; i++) {
            perf[i] = users[refs[i]].personalPerformance + users[refs[i]].totalPerformance;
        }
        for (uint i = 1; i < n; i++) {
            if (perf[i] > perf[maxIdx]) maxIdx = i;
        }
        uint256 smallSum = 0; uint256 smallCount = 0;
        for (uint i = 0; i < n; i++) {
            if (i == maxIdx) continue;
            if (perf[i] > 0) { smallSum += perf[i]; smallCount++; }
        }

        uint256[5] memory REQ = [uint256(0), 5000e6, 30000e6, 100000e6, 500000e6];
        // 修复关键bug: 检查下一级的要求，而不是当前级的要求
        // lvl=0 升级到 lvl=1 需要 REQ[1]=5000 USDT
        // lvl=1 升级到 lvl=2 需要 REQ[2]=30000 USDT
        uint8 nextLevel = lvl + 1;
        if (nextLevel < REQ.length && smallCount >= 1 && smallSum >= REQ[nextLevel]) {
            users[user].level = nextLevel;
            users[user].upgradeType = UpgradeType.AUTO_UPGRADE;
            users[user].upgradeTimestamp = block.timestamp;
            emit LevelUpdated(user, nextLevel, smallSum, smallCount);
            emit LevelUpgradeTypeChanged(user, nextLevel, UpgradeType.AUTO_UPGRADE, block.timestamp);
        }
    }

    function addPerformance(address winner, uint256 amount)
        external
        nonReentrant
        onlyUploader
        notBlacklisted(winner)
        notFrozen(winner)
    {
        // 设置个人业绩
        users[winner].personalPerformance += amount;
        emit PersonalPerformanceAdded(winner, amount);

        // 向上递归设置团队业绩，并收集需要检查升级的地址
        address[] memory upgradeCheckList = new address[](21); // 最多20层 + 获胜者
        uint8 upgradeCheckCount = 0;

        // 添加获胜者到升级检查列表
        upgradeCheckList[upgradeCheckCount++] = winner;

        address current = winner;
        for (uint8 i = 0; i < 20; i++) {
            address inviter = users[current].inviter;
            if (inviter == address(0) || inviter == systemAdmin) break;

            users[inviter].totalPerformance += amount;
            teamPerformance[current][inviter] += amount;

            // 添加推荐人到升级检查列表
            upgradeCheckList[upgradeCheckCount++] = inviter;

            emit TeamPerformanceAdded(current, inviter, amount);
            current = inviter;
        }

        emit PerformanceAdded(winner, amount);

        // 检查所有相关用户的升级条件
        for (uint8 j = 0; j < upgradeCheckCount; j++) {
            _upgradeIfEligible(upgradeCheckList[j]);
        }
    }

    function setUserLevel(address user, uint8 level) external nonReentrant onlyRole(ADMIN_ROLE) {
        require(level <= 4, "Invalid level");
        users[user].level = level;
        users[user].upgradeType = UpgradeType.ADMIN_SET;
        users[user].upgradeTimestamp = block.timestamp;
        emit LevelUpdated(user, level, 0, 0);
        emit LevelUpgradeTypeChanged(user, level, UpgradeType.ADMIN_SET, block.timestamp);
    }

    function addToBlacklist(address user) external nonReentrant onlyRole(ADMIN_ROLE) {
        blacklist[user] = true;
        emit BlacklistUpdated(user, true);
    }

    function removeFromBlacklist(address user) external nonReentrant onlyRole(ADMIN_ROLE) {
        blacklist[user] = false;
        emit BlacklistUpdated(user, false);
    }

    function freezeUser(address user) external nonReentrant onlyRole(ADMIN_ROLE) {
        frozenUsers[user] = true;
        emit UserFrozen(user, true);
    }

    function unfreezeUser(address user) external nonReentrant onlyRole(ADMIN_ROLE) {
        frozenUsers[user] = false;
        emit UserFrozen(user, false);
    }

    function updateSystemAdmin(address newAdmin) external nonReentrant onlyRole(ADMIN_ROLE) {
        require(newAdmin != address(0), "Zero address");
        _revokeRole(ADMIN_ROLE, systemAdmin);
        _revokeRole(DEFAULT_ADMIN_ROLE, systemAdmin);
        _grantRole(ADMIN_ROLE, newAdmin);
        _grantRole(DEFAULT_ADMIN_ROLE, newAdmin);
        systemAdmin = newAdmin;
        emit AdminUpdated(newAdmin);
    }

    function grantUploaderRole(address account) external onlyRole(DEFAULT_ADMIN_ROLE) {
        grantRole(PERFORMANCE_UPLOADER, account);
    }

    function tryUpgrade(address user) external nonReentrant whenNotPaused {
        require(user != address(0), "Zero address");
        _upgradeIfEligible(user);
    }

    /**
     * @dev 验证用户等级是否合理
     * @param user 用户地址
     * @return isValid 等级是否有效
     * @return upgradeType 升级类型
     * @return expectedLevel 基于业绩应有的等级
     * @return smallTeamsPerformance 小团队业绩总和
     * @return validTeamsCount 有效团队数量
     */
    function validateUserLevel(address user) external view returns (
        bool isValid,
        UpgradeType upgradeType,
        uint8 expectedLevel,
        uint256 smallTeamsPerformance,
        uint256 validTeamsCount
    ) {
        User storage userInfo = users[user];
        uint8 currentLevel = userInfo.level;
        upgradeType = userInfo.upgradeType;

        // 如果是管理员设置的等级，直接认为有效
        if (upgradeType == UpgradeType.ADMIN_SET) {
            return (true, upgradeType, currentLevel, 0, 0);
        }

        // 计算基于业绩应有的等级
        (expectedLevel, smallTeamsPerformance, validTeamsCount) = _calculateExpectedLevel(user);

        // 检查等级是否匹配
        isValid = (currentLevel == expectedLevel);

        return (isValid, upgradeType, expectedLevel, smallTeamsPerformance, validTeamsCount);
    }

    /**
     * @dev 计算用户基于业绩应有的等级
     * @param user 用户地址
     * @return expectedLevel 应有等级
     * @return smallTeamsPerformance 小团队业绩总和
     * @return validTeamsCount 有效团队数量
     */
    function _calculateExpectedLevel(address user) internal view returns (
        uint8 expectedLevel,
        uint256 smallTeamsPerformance,
        uint256 validTeamsCount
    ) {
        address[] storage refs = users[user].referrals;
        uint256 n = refs.length;

        if (n == 0) {
            return (0, 0, 0);
        }

        // 计算每个直推用户的真实总业绩
        uint256 maxIdx = 0;
        uint256[] memory perf = new uint256[](n);
        for (uint i = 0; i < n; i++) {
            perf[i] = users[refs[i]].personalPerformance + users[refs[i]].totalPerformance;
        }

        // 找出最大业绩团队
        for (uint i = 1; i < n; i++) {
            if (perf[i] > perf[maxIdx]) maxIdx = i;
        }

        // 计算小团队业绩总和
        smallTeamsPerformance = 0;
        validTeamsCount = 0;
        for (uint i = 0; i < n; i++) {
            if (i == maxIdx) continue;
            if (perf[i] > 0) {
                smallTeamsPerformance += perf[i];
                validTeamsCount++;
            }
        }

        // 根据业绩计算应有等级
        uint256[5] memory REQ = [uint256(0), 5000e6, 30000e6, 100000e6, 500000e6];
        expectedLevel = 0;

        if (validTeamsCount >= 1) {
            for (uint8 i = 1; i < 5; i++) {
                if (smallTeamsPerformance >= REQ[i]) {
                    expectedLevel = i;
                } else {
                    break;
                }
            }
        }

        return (expectedLevel, smallTeamsPerformance, validTeamsCount);
    }

    /**
     * @dev 批量验证用户等级
     * @param userAddresses 用户地址数组
     * @return isValid 验证结果数组
     * @return upgradeTypes 升级类型数组
     * @return expectedLevels 期望等级数组
     */
    function batchValidateUserLevels(address[] calldata userAddresses) external view returns (
        bool[] memory isValid,
        uint8[] memory upgradeTypes,
        uint8[] memory expectedLevels
    ) {
        uint256 length = userAddresses.length;
        isValid = new bool[](length);
        upgradeTypes = new uint8[](length);
        expectedLevels = new uint8[](length);

        for (uint256 i = 0; i < length; i++) {
            (bool valid, UpgradeType upgradeType, uint8 expectedLevel,,) = this.validateUserLevel(userAddresses[i]);
            isValid[i] = valid;
            upgradeTypes[i] = uint8(upgradeType);
            expectedLevels[i] = expectedLevel;
        }

        return (isValid, upgradeTypes, expectedLevels);
    }

    /**
     * @dev 获取用户升级历史信息
     * @param user 用户地址
     * @return level 当前等级
     * @return upgradeType 升级类型
     * @return upgradeTimestamp 升级时间戳
     * @return isLevelValid 等级是否有效
     */
    function getUserUpgradeInfo(address user) external view returns (
        uint8 level,
        UpgradeType upgradeType,
        uint256 upgradeTimestamp,
        bool isLevelValid
    ) {
        User storage userInfo = users[user];
        level = userInfo.level;
        upgradeType = userInfo.upgradeType;
        upgradeTimestamp = userInfo.upgradeTimestamp;

        // 验证等级是否有效
        (isLevelValid,,,,) = this.validateUserLevel(user);

        return (level, upgradeType, upgradeTimestamp, isLevelValid);
    }

    function getUserInfo(address user) external view returns (
        address inviter,
        uint8 level,
        uint256 totalPerformance,
        uint256 referralsCount,
        bool isRegistered,
        uint256 personalPerformance,
        UpgradeType upgradeType,
        uint256 upgradeTimestamp
    ) {
        User storage userInfo = users[user];
        return (
            userInfo.inviter,
            userInfo.level,
            userInfo.totalPerformance,
            userInfo.referrals.length,
            userInfo.inviter != address(0),
            userInfo.personalPerformance,
            userInfo.upgradeType,
            userInfo.upgradeTimestamp
        );
    }

    function getUserReferrals(address user) external view returns (address[] memory) {
        return users[user].referrals;
    }

    function getLevel(address user) external view returns (uint8) {
        return users[user].level;
    }

    function getInviter(address user) external view returns (address) {
        return users[user].inviter;
    }

    function getPerformance(address user) external view returns (uint256) {
        return users[user].totalPerformance;
    }

    function getPersonalPerformance(address user) external view returns (uint256) {
        return users[user].personalPerformance;
    }

    // 递归获取团队成员总数
    function getTeamMemberCount(address user, uint8 maxDepth) public view returns (uint256) {
        if (maxDepth == 0) return 0;
        
        address[] storage directRefs = users[user].referrals;
        uint256 total = directRefs.length;
        
        for (uint i = 0; i < directRefs.length; i++) {
            total += getTeamMemberCount(directRefs[i], maxDepth - 1);
        }
        
        return total;
    }

    // 获取团队统计信息
    function getTeamStats(address user) external view returns (
        uint256 directCount,
        uint256 totalCount,
        uint256 teamPerf,
        uint256 personalPerf
    ) {
        directCount = users[user].referrals.length;
        totalCount = getTeamMemberCount(user, 20); // 统计20层以内的所有成员
        teamPerf = users[user].totalPerformance;
        personalPerf = users[user].personalPerformance;
    }

    function isBlacklisted(address account) external view returns (bool) {
        return blacklist[account];
    }

    function isFrozen(address account) external view returns (bool) {
        return frozenUsers[account];
    }

    function pause() external onlyRole(PAUSER_ROLE) {
        _pause();
    }

    function unpause() external onlyRole(PAUSER_ROLE) {
        _unpause();
    }

    // ==================== 业务操作注册检查函数 ====================

    /**
     * @notice 检查用户是否可以创建拼团房间
     * @param user 用户地址
     * @return 是否可以创建
     */
    function canCreateGroupBuyRoom(address user) external view returns (bool) {
        return users[user].inviter != address(0) && !blacklist[user] && !frozenUsers[user];
    }

    /**
     * @notice 检查用户是否可以参与拼团房间
     * @param user 用户地址
     * @return 是否可以参与
     */
    function canJoinGroupBuyRoom(address user) external view returns (bool) {
        return users[user].inviter != address(0) && !blacklist[user] && !frozenUsers[user];
    }

    /**
     * @notice 检查用户是否可以转账积分
     * @param user 用户地址
     * @return 是否可以转账
     */
    function canTransferPoints(address user) external view returns (bool) {
        return users[user].inviter != address(0) && !blacklist[user] && !frozenUsers[user];
    }

    /**
     * @notice 检查用户是否可以接收积分
     * @param user 用户地址
     * @return 是否可以接收
     */
    function canReceivePoints(address user) external view returns (bool) {
        return users[user].inviter != address(0) && !blacklist[user] && !frozenUsers[user];
    }

    /**
     * @notice 检查用户是否可以质押节点
     * @param user 用户地址
     * @return 是否可以质押
     */
    function canStakeNode(address user) external view returns (bool) {
        return users[user].inviter != address(0) && !blacklist[user] && !frozenUsers[user];
    }

    /**
     * @notice 检查用户是否可以参与QPT回购
     * @param user 用户地址
     * @return 是否可以参与
     */
    function canJoinQPTBuyback(address user) external view returns (bool) {
        return users[user].inviter != address(0) && !blacklist[user] && !frozenUsers[user];
    }

    /**
     * @notice 检查用户是否可以注册商家账户
     * @param user 用户地址
     * @return 是否可以注册
     */
    function canRegisterMerchant(address user) external view returns (bool) {
        return users[user].inviter != address(0) && !blacklist[user] && !frozenUsers[user];
    }

    /**
     * @notice 批量检查用户权限（用于前端一次性获取所有权限）
     * @param user 用户地址
     * @return canCreateRoom 是否可以创建房间
     * @return canJoinRoom 是否可以参与房间
     * @return canTransfer 是否可以转账
     * @return canReceive 是否可以接收
     * @return canStake 是否可以质押
     * @return canBuyback 是否可以回购
     * @return canMerchant 是否可以注册商家
     * @return isRegistered 是否已注册
     * @return userBlacklisted 是否被拉黑
     * @return userFrozen 是否被冻结
     */
    function getUserPermissions(address user) external view returns (
        bool canCreateRoom,
        bool canJoinRoom,
        bool canTransfer,
        bool canReceive,
        bool canStake,
        bool canBuyback,
        bool canMerchant,
        bool isRegistered,
        bool userBlacklisted,
        bool userFrozen
    ) {
        bool registered = users[user].inviter != address(0);
        bool blocked = blacklist[user];
        bool frozen = frozenUsers[user];
        bool allowed = registered && !blocked && !frozen;

        return (
            allowed, // canCreateRoom
            allowed, // canJoinRoom
            allowed, // canTransfer
            allowed, // canReceive
            allowed, // canStake
            allowed, // canBuyback
            allowed, // canMerchant
            registered, // isRegistered
            blocked, // userBlacklisted
            frozen // userFrozen
        );
    }

    uint256[50] private __gap;
}