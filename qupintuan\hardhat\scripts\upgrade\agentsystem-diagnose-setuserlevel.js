// qupintuan/hardhat/scripts/upgrade/agentsystem-diagnose-setuserlevel.js
// 诊断setUserLevel函数权限问题

const { ethers } = require("hardhat");

async function main() {
  console.log("🔍 诊断setUserLevel函数权限问题...\n");

  const MULTISIG_ADDRESS = "******************************************";
  const AGENT_SYSTEM_PROXY = "******************************************";
  const TEST_USER = "******************************************";

  try {
    console.log("📋 诊断信息:");
    console.log(`   多签钱包: ${MULTISIG_ADDRESS}`);
    console.log(`   代理合约: ${AGENT_SYSTEM_PROXY}`);
    console.log(`   测试用户: ${TEST_USER}`);
    console.log("");

    // 连接合约
    const agentSystem = await ethers.getContractAt("AgentSystem", AGENT_SYSTEM_PROXY);

    // 1. 检查setUserLevel函数的权限要求
    console.log("1️⃣ 检查setUserLevel函数的权限要求...");
    
    try {
      // 查看合约源码中setUserLevel函数的修饰符
      console.log("   检查函数修饰符...");
      
      // 尝试从不同地址调用setUserLevel来测试权限
      const [deployer] = await ethers.getSigners();
      
      // 测试当前账户
      try {
        await agentSystem.connect(deployer).callStatic.setUserLevel(TEST_USER, 0);
        console.log("   ✅ 当前账户可以调用setUserLevel");
      } catch (error) {
        console.log("   ❌ 当前账户不能调用setUserLevel:", error.message);
      }
      
      // 测试多签钱包
      try {
        await agentSystem.callStatic.setUserLevel(TEST_USER, 0, { from: MULTISIG_ADDRESS });
        console.log("   ✅ 多签钱包可以调用setUserLevel");
      } catch (error) {
        console.log("   ❌ 多签钱包不能调用setUserLevel:", error.message);
        
        if (error.message.includes("AccessControl")) {
          console.log("   原因: AccessControl权限不足");
        } else if (error.message.includes("Ownable")) {
          console.log("   原因: 不是合约所有者");
        }
      }
      
    } catch (error) {
      console.log(`   ❌ 权限测试失败: ${error.message}`);
    }

    // 2. 检查多签钱包的具体权限
    console.log("\n2️⃣ 检查多签钱包的具体权限...");
    
    try {
      const ADMIN_ROLE = await agentSystem.ADMIN_ROLE();
      const DEFAULT_ADMIN_ROLE = "0x0000000000000000000000000000000000000000000000000000000000000000";
      
      const multisigHasAdmin = await agentSystem.hasRole(ADMIN_ROLE, MULTISIG_ADDRESS);
      const multisigHasDefaultAdmin = await agentSystem.hasRole(DEFAULT_ADMIN_ROLE, MULTISIG_ADDRESS);
      
      console.log(`   管理员角色: ${ADMIN_ROLE}`);
      console.log(`   多签有管理员权限: ${multisigHasAdmin ? '✅' : '❌'}`);
      console.log(`   多签有默认管理员权限: ${multisigHasDefaultAdmin ? '✅' : '❌'}`);
      
      if (!multisigHasAdmin && !multisigHasDefaultAdmin) {
        console.log("   🚨 问题: 多签钱包没有任何管理员权限！");
      }
      
    } catch (error) {
      console.log(`   ❌ 权限检查失败: ${error.message}`);
    }

    // 3. 检查谁有setUserLevel权限
    console.log("\n3️⃣ 检查谁有setUserLevel权限...");
    
    const possibleAdmins = [
      "0x012F049B3947C45C400AF5909c5b629BEac0C0b3", // 部署者
      "0x69bf4d498BBd3580f126F3442FFAE9aAaC4c3400", // Timelock
      "******************************************", // 多签钱包
      "0xc05aaF414836c7AFdb0d60D51b44DA34cb9BbF7F"  // 系统管理员
    ];
    
    for (const addr of possibleAdmins) {
      try {
        const ADMIN_ROLE = await agentSystem.ADMIN_ROLE();
        const hasAdmin = await agentSystem.hasRole(ADMIN_ROLE, addr);
        
        if (hasAdmin) {
          console.log(`   ✅ ${addr}: 有ADMIN_ROLE权限`);
          
          // 测试是否能调用setUserLevel
          try {
            await agentSystem.callStatic.setUserLevel(TEST_USER, 0, { from: addr });
            console.log(`      ✅ 可以调用setUserLevel`);
          } catch (error) {
            console.log(`      ❌ 不能调用setUserLevel: ${error.message}`);
          }
        } else {
          console.log(`   ❌ ${addr}: 没有ADMIN_ROLE权限`);
        }
      } catch (error) {
        console.log(`   ❌ ${addr}: 检查失败 - ${error.message}`);
      }
    }

    // 4. 尝试给多签钱包授权
    console.log("\n4️⃣ 尝试给多签钱包授权...");
    
    try {
      const [deployer] = await ethers.getSigners();
      const ADMIN_ROLE = await agentSystem.ADMIN_ROLE();
      const deployerHasAdmin = await agentSystem.hasRole(ADMIN_ROLE, deployer.address);
      const deployerHasDefaultAdmin = await agentSystem.hasRole("0x0000000000000000000000000000000000000000000000000000000000000000", deployer.address);
      
      console.log(`   当前账户: ${deployer.address}`);
      console.log(`   有ADMIN权限: ${deployerHasAdmin ? '✅' : '❌'}`);
      console.log(`   有DEFAULT_ADMIN权限: ${deployerHasDefaultAdmin ? '✅' : '❌'}`);
      
      if (deployerHasAdmin || deployerHasDefaultAdmin) {
        console.log("   ✅ 当前账户有权限，尝试给多签钱包授权...");
        
        const multisigHasAdmin = await agentSystem.hasRole(ADMIN_ROLE, MULTISIG_ADDRESS);
        if (!multisigHasAdmin) {
          try {
            const grantTx = await agentSystem.grantRole(ADMIN_ROLE, MULTISIG_ADDRESS);
            console.log(`   ⏳ 授权交易已提交: ${grantTx.hash}`);
            
            const receipt = await grantTx.wait();
            console.log(`   ✅ 授权成功! Gas: ${receipt.gasUsed.toString()}`);
            
            // 验证授权
            const newHasAdmin = await agentSystem.hasRole(ADMIN_ROLE, MULTISIG_ADDRESS);
            console.log(`   验证结果: ${newHasAdmin ? '✅ 授权成功' : '❌ 授权失败'}`);
            
          } catch (error) {
            console.log(`   ❌ 授权失败: ${error.message}`);
          }
        } else {
          console.log("   ✅ 多签钱包已经有ADMIN权限");
        }
      } else {
        console.log("   ❌ 当前账户没有授权权限");
      }
      
    } catch (error) {
      console.log(`   ❌ 授权尝试失败: ${error.message}`);
    }

    // 5. 尝试直接修复用户（如果有权限）
    console.log("\n5️⃣ 尝试直接修复用户...");
    
    try {
      const [deployer] = await ethers.getSigners();
      const ADMIN_ROLE = await agentSystem.ADMIN_ROLE();
      const hasAdmin = await agentSystem.hasRole(ADMIN_ROLE, deployer.address);
      
      if (hasAdmin) {
        console.log("   ✅ 当前账户有权限，尝试直接修复用户...");
        
        const problemUsers = [
          "******************************************",
          "******************************************"
        ];
        
        for (const userAddress of problemUsers) {
          try {
            console.log(`\n   修复用户: ${userAddress}`);
            
            // 检查当前状态
            const userInfo = await agentSystem.getUserInfo(userAddress);
            const currentLevel = Number(userInfo[1]);
            const validation = await agentSystem.validateUserLevel(userAddress);
            const expectedLevel = Number(validation[2]);
            
            console.log(`     当前等级: Level ${currentLevel}`);
            console.log(`     应有等级: Level ${expectedLevel}`);
            
            if (currentLevel !== expectedLevel) {
              console.log(`     正在修复: Level ${currentLevel} → Level ${expectedLevel}`);
              
              const fixTx = await agentSystem.setUserLevel(userAddress, expectedLevel);
              console.log(`     ⏳ 修复交易已提交: ${fixTx.hash}`);
              
              const receipt = await fixTx.wait();
              console.log(`     ✅ 修复成功! Gas: ${receipt.gasUsed.toString()}`);
              
              // 验证修复结果
              const newUserInfo = await agentSystem.getUserInfo(userAddress);
              const newLevel = Number(newUserInfo[1]);
              const newValidation = await agentSystem.validateUserLevel(userAddress);
              const newIsValid = newValidation[0];
              
              console.log(`     验证结果: Level ${newLevel}, 有效: ${newIsValid ? '✅' : '❌'}`);
              
            } else {
              console.log(`     ✅ 用户等级已正确，无需修复`);
            }
            
          } catch (error) {
            console.log(`     ❌ 修复用户失败: ${error.message}`);
          }
        }
        
      } else {
        console.log("   ❌ 当前账户没有权限直接修复");
      }
      
    } catch (error) {
      console.log(`   ❌ 直接修复失败: ${error.message}`);
    }

    // 6. 解决方案建议
    console.log("\n6️⃣ 解决方案建议...");
    console.log("=".repeat(50));
    
    console.log("\n🔧 解决方案:");
    console.log("1. 如果上面的直接修复成功了，问题已解决");
    console.log("2. 如果需要通过多签，先确保多签有ADMIN_ROLE权限");
    console.log("3. 或者通过Timelock来修复用户（Timelock有权限）");
    
    console.log("\n📋 下一步:");
    console.log("1. 检查上面的直接修复是否成功");
    console.log("2. 如果成功，运行验证脚本:");
    console.log("   npx hardhat run scripts/verify-agentsystem-fix.js --network bscTestnet");
    console.log("3. 如果失败，考虑通过Timelock修复");

  } catch (error) {
    console.error("❌ 诊断失败:", error.message);
    console.error("详细错误:", error);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
