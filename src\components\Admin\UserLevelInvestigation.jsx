// src/components/Admin/UserLevelInvestigation.jsx
// 用户等级问题调查工具

import React, { useState } from 'react';
import { createPublicClient, http } from 'viem';
import { bscTestnet } from 'viem/chains';
import { CONTRACT_ADDRESSES, ABIS } from '@/contracts';
import './UserLevelInvestigation.css';

const UserLevelInvestigation = () => {
  const [userAddress, setUserAddress] = useState('0xDf98905098CB4e5D261578f600337eeeFd4082b3');
  const [investigation, setInvestigation] = useState(null);
  const [isInvestigating, setIsInvestigating] = useState(false);

  // 升级类型名称映射
  const upgradeTypeNames = {
    0: '未升级',
    1: '自动升级',
    2: '管理员设置'
  };

  // 等级要求（USDT）
  const levelRequirements = [0, 5000, 30000, 100000, 500000];

  const investigateUser = async () => {
    if (!userAddress) return;

    setIsInvestigating(true);
    try {
      const publicClient = createPublicClient({
        chain: bscTestnet,
        transport: http()
      });

      const agentSystemAddress = CONTRACT_ADDRESSES[97].AgentSystem;

      // 1. 获取用户基本信息
      const userInfo = await publicClient.readContract({
        address: agentSystemAddress,
        abi: ABIS.AgentSystem,
        functionName: 'getUserInfo',
        args: [userAddress]
      });

      // 2. 获取升级信息
      let upgradeInfo;
      try {
        upgradeInfo = await publicClient.readContract({
          address: agentSystemAddress,
          abi: ABIS.AgentSystem,
          functionName: 'getUserUpgradeInfo',
          args: [userAddress]
        });
      } catch (error) {
        console.warn('获取升级信息失败，可能是旧版本合约:', error);
        upgradeInfo = [userInfo[1], 0, 0, false]; // 默认值
      }

      // 3. 验证等级
      let validation;
      try {
        validation = await publicClient.readContract({
          address: agentSystemAddress,
          abi: ABIS.AgentSystem,
          functionName: 'validateUserLevel',
          args: [userAddress]
        });
      } catch (error) {
        console.warn('验证等级失败，可能是旧版本合约:', error);
        validation = [false, 0, 0, 0, 0]; // 默认值
      }

      // 4. 获取直推用户信息
      const referralsCount = Number(userInfo[3]);
      const referralsList = [];
      
      if (referralsCount > 0) {
        for (let i = 0; i < referralsCount; i++) {
          try {
            const referralAddress = await publicClient.readContract({
              address: agentSystemAddress,
              abi: ABIS.AgentSystem,
              functionName: 'getReferral',
              args: [userAddress, i]
            });

            const referralInfo = await publicClient.readContract({
              address: agentSystemAddress,
              abi: ABIS.AgentSystem,
              functionName: 'getUserInfo',
              args: [referralAddress]
            });

            referralsList.push({
              address: referralAddress,
              level: Number(referralInfo[1]),
              totalPerformance: Number(referralInfo[2]) / 1000000, // 转换为USDT
              personalPerformance: Number(referralInfo[5]) / 1000000,
              realTotalPerformance: (Number(referralInfo[5]) + Number(referralInfo[2])) / 1000000
            });
          } catch (error) {
            console.warn(`获取第${i}个直推用户信息失败:`, error);
          }
        }
      }

      // 5. 计算小团队业绩
      const performances = referralsList.map(r => r.realTotalPerformance);
      performances.sort((a, b) => b - a);
      
      const maxPerformance = performances[0] || 0;
      const smallTeamsPerformance = performances.slice(1).reduce((sum, perf) => sum + perf, 0);
      const validTeamsCount = performances.filter(p => p > 0).length;

      // 6. 分析结果
      const currentLevel = Number(userInfo[1]);
      const totalPerformance = Number(userInfo[2]) / 1000000;
      const personalPerformance = Number(userInfo[5]) / 1000000;
      
      const [level, upgradeType, upgradeTimestamp, isLevelValid] = upgradeInfo;
      const [validationIsValid, validationUpgradeType, expectedLevel, validationSmallTeams, validationValidTeams] = validation;

      const shouldBeLevel = performances.length >= 1 && smallTeamsPerformance >= levelRequirements[1] ? 1 : 0;
      
      setInvestigation({
        userAddress,
        currentLevel,
        upgradeType: Number(upgradeType),
        upgradeTimestamp: Number(upgradeTimestamp),
        isLevelValid,
        totalPerformance,
        personalPerformance,
        referralsCount,
        referralsList,
        maxPerformance,
        smallTeamsPerformance,
        validTeamsCount,
        shouldBeLevel,
        levelRequirement: levelRequirements[currentLevel + 1] || 0,
        validation: {
          isValid: validationIsValid,
          upgradeType: Number(validationUpgradeType),
          expectedLevel: Number(expectedLevel),
          smallTeamsPerformance: Number(validationSmallTeams) / 1000000,
          validTeamsCount: Number(validationValidTeams)
        }
      });

    } catch (error) {
      console.error('调查用户失败:', error);
      setInvestigation({
        error: error.message,
        userAddress
      });
    } finally {
      setIsInvestigating(false);
    }
  };

  return (
    <div className="user-level-investigation">
      <div className="investigation-header">
        <h3>🔍 用户等级问题调查工具</h3>
        <p>调查用户等级是否符合合约规则，分析升级类型和业绩计算</p>
      </div>

      <div className="investigation-input">
        <div className="input-group">
          <label>用户地址:</label>
          <input
            type="text"
            value={userAddress}
            onChange={(e) => setUserAddress(e.target.value)}
            placeholder="输入用户地址"
          />
          <button 
            onClick={investigateUser}
            disabled={isInvestigating || !userAddress}
            className="investigate-btn"
          >
            {isInvestigating ? '调查中...' : '开始调查'}
          </button>
        </div>
      </div>

      {investigation && (
        <div className="investigation-results">
          {investigation.error ? (
            <div className="error-result">
              <h4>❌ 调查失败</h4>
              <p>{investigation.error}</p>
            </div>
          ) : (
            <div className="success-result">
              <h4>📊 调查结果</h4>
              
              {/* 基本信息 */}
              <div className="result-section">
                <h5>👤 用户基本信息</h5>
                <div className="info-grid">
                  <div className="info-item">
                    <span className="label">用户地址:</span>
                    <span className="value">{investigation.userAddress}</span>
                  </div>
                  <div className="info-item">
                    <span className="label">当前等级:</span>
                    <span className="value">Level {investigation.currentLevel}</span>
                  </div>
                  <div className="info-item">
                    <span className="label">个人业绩:</span>
                    <span className="value">{investigation.personalPerformance.toFixed(2)} USDT</span>
                  </div>
                  <div className="info-item">
                    <span className="label">团队业绩:</span>
                    <span className="value">{investigation.totalPerformance.toFixed(2)} USDT</span>
                  </div>
                  <div className="info-item">
                    <span className="label">直推人数:</span>
                    <span className="value">{investigation.referralsCount} 人</span>
                  </div>
                </div>
              </div>

              {/* 升级信息 */}
              <div className="result-section">
                <h5>🔄 升级信息</h5>
                <div className="info-grid">
                  <div className="info-item">
                    <span className="label">升级类型:</span>
                    <span className={`value upgrade-type-${investigation.upgradeType}`}>
                      {upgradeTypeNames[investigation.upgradeType] || '未知'}
                    </span>
                  </div>
                  <div className="info-item">
                    <span className="label">升级时间:</span>
                    <span className="value">
                      {investigation.upgradeTimestamp > 0 
                        ? new Date(investigation.upgradeTimestamp * 1000).toLocaleString()
                        : '未升级'
                      }
                    </span>
                  </div>
                  <div className="info-item">
                    <span className="label">等级有效性:</span>
                    <span className={`value ${investigation.isLevelValid ? 'valid' : 'invalid'}`}>
                      {investigation.isLevelValid ? '✅ 有效' : '❌ 无效'}
                    </span>
                  </div>
                </div>
              </div>

              {/* 业绩分析 */}
              <div className="result-section">
                <h5>📈 业绩分析</h5>
                <div className="info-grid">
                  <div className="info-item">
                    <span className="label">最大团队业绩:</span>
                    <span className="value">{investigation.maxPerformance.toFixed(2)} USDT</span>
                  </div>
                  <div className="info-item">
                    <span className="label">小团队业绩总和:</span>
                    <span className="value">{investigation.smallTeamsPerformance.toFixed(2)} USDT</span>
                  </div>
                  <div className="info-item">
                    <span className="label">有效团队数:</span>
                    <span className="value">{investigation.validTeamsCount} 个</span>
                  </div>
                  <div className="info-item">
                    <span className="label">应有等级:</span>
                    <span className="value">Level {investigation.shouldBeLevel}</span>
                  </div>
                  <div className="info-item">
                    <span className="label">下级要求:</span>
                    <span className="value">
                      {investigation.levelRequirement > 0 
                        ? `${investigation.levelRequirement} USDT`
                        : '已达最高等级'
                      }
                    </span>
                  </div>
                </div>
              </div>

              {/* 直推用户列表 */}
              {investigation.referralsList.length > 0 && (
                <div className="result-section">
                  <h5>👥 直推用户业绩排行</h5>
                  <div className="referrals-table">
                    <table>
                      <thead>
                        <tr>
                          <th>排名</th>
                          <th>用户地址</th>
                          <th>等级</th>
                          <th>个人业绩</th>
                          <th>团队业绩</th>
                          <th>总业绩</th>
                          <th>是否计入小团队</th>
                        </tr>
                      </thead>
                      <tbody>
                        {investigation.referralsList
                          .sort((a, b) => b.realTotalPerformance - a.realTotalPerformance)
                          .map((referral, index) => (
                            <tr key={referral.address}>
                              <td>{index + 1}</td>
                              <td>{referral.address.slice(0, 6)}...{referral.address.slice(-4)}</td>
                              <td>Level {referral.level}</td>
                              <td>{referral.personalPerformance.toFixed(2)} USDT</td>
                              <td>{referral.totalPerformance.toFixed(2)} USDT</td>
                              <td>{referral.realTotalPerformance.toFixed(2)} USDT</td>
                              <td>
                                {index === 0 ? (
                                  <span className="excluded">❌ 排除（最大团队）</span>
                                ) : (
                                  <span className="included">✅ 计入</span>
                                )}
                              </td>
                            </tr>
                          ))
                        }
                      </tbody>
                    </table>
                  </div>
                </div>
              )}

              {/* 问题诊断 */}
              <div className="result-section">
                <h5>🩺 问题诊断</h5>
                <div className="diagnosis">
                  {investigation.currentLevel > investigation.shouldBeLevel ? (
                    <div className="diagnosis-item warning">
                      <span className="icon">⚠️</span>
                      <div className="content">
                        <strong>等级异常</strong>
                        <p>
                          用户当前等级 Level {investigation.currentLevel}，但根据业绩计算应为 Level {investigation.shouldBeLevel}。
                          {investigation.upgradeType === 2 
                            ? ' 这是管理员手动设置的等级。'
                            : investigation.upgradeType === 1
                            ? ' 这可能是由于合约升级前的历史数据问题。'
                            : ' 升级类型未知，需要进一步调查。'
                          }
                        </p>
                      </div>
                    </div>
                  ) : investigation.currentLevel < investigation.shouldBeLevel ? (
                    <div className="diagnosis-item info">
                      <span className="icon">📈</span>
                      <div className="content">
                        <strong>可以升级</strong>
                        <p>
                          用户满足升级条件，可以从 Level {investigation.currentLevel} 升级到 Level {investigation.shouldBeLevel}。
                          小团队业绩 {investigation.smallTeamsPerformance.toFixed(2)} USDT 已达到要求的 {levelRequirements[1]} USDT。
                        </p>
                      </div>
                    </div>
                  ) : (
                    <div className="diagnosis-item success">
                      <span className="icon">✅</span>
                      <div className="content">
                        <strong>等级正常</strong>
                        <p>用户等级与业绩匹配，无异常。</p>
                      </div>
                    </div>
                  )}

                  {investigation.upgradeType === 0 && investigation.currentLevel > 0 && (
                    <div className="diagnosis-item error">
                      <span className="icon">🚨</span>
                      <div className="content">
                        <strong>数据异常</strong>
                        <p>
                          用户等级为 Level {investigation.currentLevel}，但升级类型显示为"未升级"。
                          这表明可能存在数据不一致的问题，建议使用修复工具。
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default UserLevelInvestigation;
