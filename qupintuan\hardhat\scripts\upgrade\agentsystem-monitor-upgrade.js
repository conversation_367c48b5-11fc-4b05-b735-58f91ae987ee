// qupintuan/hardhat/scripts/upgrade/agentsystem-monitor-upgrade.js
// 监控AgentSystem升级进度

const { ethers } = require("hardhat");

async function main() {
  console.log("👀 监控AgentSystem升级进度...\n");

  // 升级信息
  const MULTISIG_ADDRESS = "******************************************";
  const TIMELOCK_ADDRESS = "******************************************";
  const AGENT_SYSTEM_PROXY = "******************************************";
  const NEW_IMPLEMENTATION = "******************************************";
  const SALT = "0x835bfaef1f47108801e75329e3f4dcefd355c3a2a425b1d271f07d1c393dce62";
  
  // 提案ID
  const SCHEDULE_PROPOSAL_ID = 38;
  const EXECUTE_PROPOSAL_ID = 39;

  try {
    console.log("📋 升级监控信息:");
    console.log(`   多签钱包: ${MULTISIG_ADDRESS}`);
    console.log(`   Timelock: ${TIMELOCK_ADDRESS}`);
    console.log(`   代理合约: ${AGENT_SYSTEM_PROXY}`);
    console.log(`   新实现: ${NEW_IMPLEMENTATION}`);
    console.log(`   Schedule提案ID: ${SCHEDULE_PROPOSAL_ID}`);
    console.log(`   Execute提案ID: ${EXECUTE_PROPOSAL_ID}`);
    console.log("");

    // 连接合约
    const multisig = await ethers.getContractAt("MultiSigWallet", MULTISIG_ADDRESS);
    const timelock = await ethers.getContractAt("TimelockController", TIMELOCK_ADDRESS);
    const agentSystem = await ethers.getContractAt("AgentSystem", AGENT_SYSTEM_PROXY);

    // 1. 检查多签提案状态
    console.log("1️⃣ 检查多签提案状态...");
    
    try {
      const scheduleProposal = await multisig.getTransaction(SCHEDULE_PROPOSAL_ID);
      console.log(`   Schedule提案 ${SCHEDULE_PROPOSAL_ID}:`);
      console.log(`     目标: ${scheduleProposal.to}`);
      console.log(`     已执行: ${scheduleProposal.executed ? '✅' : '❌'}`);
      console.log(`     确认数: ${scheduleProposal.numConfirmations}`);
      
      const executeProposal = await multisig.getTransaction(EXECUTE_PROPOSAL_ID);
      console.log(`   Execute提案 ${EXECUTE_PROPOSAL_ID}:`);
      console.log(`     目标: ${executeProposal.to}`);
      console.log(`     已执行: ${executeProposal.executed ? '✅' : '❌'}`);
      console.log(`     确认数: ${executeProposal.numConfirmations}`);
      
    } catch (error) {
      console.log(`   ❌ 获取多签提案状态失败: ${error.message}`);
    }

    // 2. 检查Timelock状态
    console.log("\n2️⃣ 检查Timelock状态...");
    
    try {
      // 计算操作ID
      const target = AGENT_SYSTEM_PROXY;
      const value = 0;
      const data = "0x4f1ef2860000000000000000000000006143a4cc8bca739ed4d610f704fcf88974c2188300000000000000000000000000000000000000000000000000000000000000400000000000000000000000000000000000000000000000000000000000000000";
      const predecessor = ethers.ZeroHash;
      const salt = SALT;
      
      const operationId = await timelock.hashOperation(target, value, data, predecessor, salt);
      console.log(`   操作ID: ${operationId}`);
      
      // 检查操作状态
      const isScheduled = await timelock.isOperation(operationId);
      const isPending = await timelock.isOperationPending(operationId);
      const isReady = await timelock.isOperationReady(operationId);
      const isDone = await timelock.isOperationDone(operationId);
      
      console.log(`   已调度: ${isScheduled ? '✅' : '❌'}`);
      console.log(`   等待中: ${isPending ? '✅' : '❌'}`);
      console.log(`   准备就绪: ${isReady ? '✅' : '❌'}`);
      console.log(`   已完成: ${isDone ? '✅' : '❌'}`);
      
      if (isScheduled && !isDone) {
        try {
          const timestamp = await timelock.getTimestamp(operationId);
          const currentTime = Math.floor(Date.now() / 1000);
          const readyTime = Number(timestamp);
          
          console.log(`   调度时间: ${new Date(readyTime * 1000).toLocaleString()}`);
          console.log(`   当前时间: ${new Date(currentTime * 1000).toLocaleString()}`);
          
          if (currentTime >= readyTime) {
            console.log(`   ✅ 延迟期已结束，可以执行`);
          } else {
            const remainingTime = readyTime - currentTime;
            console.log(`   ⏳ 剩余等待时间: ${remainingTime} 秒 (${Math.ceil(remainingTime / 60)} 分钟)`);
          }
          
        } catch (error) {
          console.log(`   ❌ 获取时间戳失败: ${error.message}`);
        }
      }
      
    } catch (error) {
      console.log(`   ❌ 检查Timelock状态失败: ${error.message}`);
    }

    // 3. 检查当前实现地址
    console.log("\n3️⃣ 检查当前实现地址...");
    
    try {
      const implementationSlot = "0x360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc";
      const currentImpl = await ethers.provider.getStorageAt(AGENT_SYSTEM_PROXY, implementationSlot);
      const currentImplAddress = "0x" + currentImpl.slice(-40);
      
      console.log(`   当前实现: ${currentImplAddress}`);
      console.log(`   目标实现: ${NEW_IMPLEMENTATION}`);
      console.log(`   升级完成: ${currentImplAddress.toLowerCase() === NEW_IMPLEMENTATION.toLowerCase() ? '✅' : '❌'}`);
      
    } catch (error) {
      console.log(`   ❌ 检查实现地址失败: ${error.message}`);
    }

    // 4. 测试问题用户状态
    console.log("\n4️⃣ 测试问题用户状态...");
    
    const problemUsers = [
      "******************************************",
      "******************************************"
    ];
    
    for (const userAddress of problemUsers) {
      try {
        const userInfo = await agentSystem.getUserInfo(userAddress);
        const currentLevel = Number(userInfo[1]);
        
        console.log(`   用户 ${userAddress}:`);
        console.log(`     当前等级: Level ${currentLevel}`);
        
        // 尝试调用新函数（如果升级完成）
        try {
          const validation = await agentSystem.validateUserLevel(userAddress);
          const isValid = validation[0];
          const expectedLevel = Number(validation[2]);
          
          console.log(`     等级有效: ${isValid ? '✅' : '❌'}`);
          console.log(`     应有等级: Level ${expectedLevel}`);
          
          if (!isValid) {
            console.log(`     🚨 仍需修复: Level ${currentLevel} → Level ${expectedLevel}`);
          }
          
        } catch (error) {
          console.log(`     ⚠️ 新函数不可用（升级未完成）`);
        }
        
      } catch (error) {
        console.log(`   ❌ 检查用户 ${userAddress} 失败: ${error.message}`);
      }
    }

    // 5. 升级状态总结
    console.log("\n5️⃣ 升级状态总结...");
    console.log("=".repeat(50));
    
    // 重新检查关键状态
    try {
      const scheduleProposal = await multisig.getTransaction(SCHEDULE_PROPOSAL_ID);
      const executeProposal = await multisig.getTransaction(EXECUTE_PROPOSAL_ID);
      
      const implementationSlot = "0x360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc";
      const currentImpl = await ethers.provider.getStorageAt(AGENT_SYSTEM_PROXY, implementationSlot);
      const currentImplAddress = "0x" + currentImpl.slice(-40);
      const isUpgraded = currentImplAddress.toLowerCase() === NEW_IMPLEMENTATION.toLowerCase();
      
      console.log("\n📊 当前状态:");
      console.log(`   Schedule提案已执行: ${scheduleProposal.executed ? '✅' : '❌'}`);
      console.log(`   Execute提案已执行: ${executeProposal.executed ? '✅' : '❌'}`);
      console.log(`   合约已升级: ${isUpgraded ? '✅' : '❌'}`);
      
      if (!scheduleProposal.executed) {
        console.log("\n📋 下一步: 执行Schedule提案");
        console.log(`   1. 登录多签钱包`);
        console.log(`   2. 确认并执行提案ID ${SCHEDULE_PROPOSAL_ID}`);
      } else if (!executeProposal.executed) {
        console.log("\n📋 下一步: 等待延迟期后执行Execute提案");
        console.log(`   1. 等待10分钟延迟期结束`);
        console.log(`   2. 登录多签钱包`);
        console.log(`   3. 确认并执行提案ID ${EXECUTE_PROPOSAL_ID}`);
      } else if (!isUpgraded) {
        console.log("\n⚠️ 异常: 提案已执行但合约未升级");
        console.log(`   需要检查执行日志和错误信息`);
      } else {
        console.log("\n🎉 升级完成!");
        console.log(`   运行验证脚本: npx hardhat run scripts/upgrade/agentsystem-verify-upgrade.js --network bscTestnet`);
      }
      
    } catch (error) {
      console.log(`❌ 状态总结失败: ${error.message}`);
    }

  } catch (error) {
    console.error("❌ 监控失败:", error.message);
    console.error("详细错误:", error);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
