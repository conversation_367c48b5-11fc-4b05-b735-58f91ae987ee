// qupintuan/hardhat/scripts/upgrade/agentsystem-deep-diagnosis.js
// 深度诊断AgentSystem升级失败的根本原因

const { ethers } = require("hardhat");

async function main() {
  console.log("🔬 深度诊断AgentSystem升级失败的根本原因...\n");

  const MULTISIG_ADDRESS = "******************************************";
  const AGENT_SYSTEM_PROXY = "******************************************";
  const NEW_IMPLEMENTATION = "******************************************";

  try {
    console.log("📋 诊断信息:");
    console.log(`   多签钱包: ${MULTISIG_ADDRESS}`);
    console.log(`   代理合约: ${AGENT_SYSTEM_PROXY}`);
    console.log(`   新实现: ${NEW_IMPLEMENTATION}`);
    console.log("");

    // 连接合约
    const agentSystem = await ethers.getContractAt("AgentSystem", AGENT_SYSTEM_PROXY);
    const multisig = await ethers.getContractAt("MultiSigWallet", MULTISIG_ADDRESS);

    // 1. 检查多签钱包权限
    console.log("1️⃣ 检查多签钱包权限...");
    
    try {
      // 检查多签钱包是否有ADMIN_ROLE
      const ADMIN_ROLE = await agentSystem.ADMIN_ROLE();
      const DEFAULT_ADMIN_ROLE = "0x0000000000000000000000000000000000000000000000000000000000000000";
      
      const multisigHasAdmin = await agentSystem.hasRole(ADMIN_ROLE, MULTISIG_ADDRESS);
      const multisigHasDefaultAdmin = await agentSystem.hasRole(DEFAULT_ADMIN_ROLE, MULTISIG_ADDRESS);
      
      console.log(`   管理员角色: ${ADMIN_ROLE}`);
      console.log(`   多签有管理员权限: ${multisigHasAdmin ? '✅' : '❌'}`);
      console.log(`   多签有默认管理员权限: ${multisigHasDefaultAdmin ? '✅' : '❌'}`);
      
      if (!multisigHasAdmin && !multisigHasDefaultAdmin) {
        console.log(`   🚨 问题发现: 多签钱包没有升级权限！`);
      }
      
    } catch (error) {
      console.log(`   ❌ 权限检查失败: ${error.message}`);
    }

    // 2. 检查当前合约的_authorizeUpgrade函数
    console.log("\n2️⃣ 检查_authorizeUpgrade函数权限要求...");
    
    try {
      // 尝试模拟升级调用来检查权限
      const [deployer] = await ethers.getSigners();
      
      try {
        // 尝试从多签地址调用upgradeTo
        await agentSystem.connect(deployer).callStatic.upgradeTo(NEW_IMPLEMENTATION, {
          from: MULTISIG_ADDRESS
        });
        console.log(`   ✅ 多签钱包有升级权限`);
      } catch (error) {
        console.log(`   ❌ 多签钱包升级权限测试失败: ${error.message}`);
        
        if (error.message.includes("AccessControl")) {
          console.log(`   原因: AccessControl权限不足`);
        } else if (error.message.includes("Ownable")) {
          console.log(`   原因: 不是合约所有者`);
        }
      }
      
    } catch (error) {
      console.log(`   ❌ 权限测试失败: ${error.message}`);
    }

    // 3. 检查新实现合约的有效性
    console.log("\n3️⃣ 检查新实现合约的有效性...");
    
    try {
      const newImpl = await ethers.getContractAt("AgentSystem", NEW_IMPLEMENTATION);
      
      // 检查新实现的UUPS UUID
      const newProxiableUUID = await newImpl.proxiableUUID();
      const currentProxiableUUID = await agentSystem.proxiableUUID();
      
      console.log(`   当前UUPS UUID: ${currentProxiableUUID}`);
      console.log(`   新实现UUPS UUID: ${newProxiableUUID}`);
      console.log(`   UUID匹配: ${newProxiableUUID === currentProxiableUUID ? '✅' : '❌'}`);
      
      // 检查新实现是否已初始化
      try {
        await newImpl.systemAdmin();
        console.log(`   ⚠️ 新实现可能已初始化，这会导致升级失败`);
      } catch (error) {
        console.log(`   ✅ 新实现未初始化（正常）`);
      }
      
      // 检查新实现的代码
      const newImplCode = await ethers.provider.getCode(NEW_IMPLEMENTATION);
      console.log(`   新实现代码长度: ${newImplCode.length} 字符`);
      console.log(`   新实现存在: ${newImplCode !== '0x' ? '✅' : '❌'}`);
      
    } catch (error) {
      console.log(`   ❌ 检查新实现失败: ${error.message}`);
    }

    // 4. 检查当前合约的所有者/管理员
    console.log("\n4️⃣ 检查当前合约的所有者/管理员...");
    
    try {
      const ADMIN_ROLE = await agentSystem.ADMIN_ROLE();
      const DEFAULT_ADMIN_ROLE = "0x0000000000000000000000000000000000000000000000000000000000000000";
      
      // 获取所有管理员
      console.log(`   查找所有管理员...`);
      
      // 检查一些可能的管理员地址
      const possibleAdmins = [
        "******************************************", // 部署者
        "******************************************", // Timelock
        "******************************************", // 多签钱包
        "0xc05aaF414836c7AFdb0d60D51b44DA34cb9BbF7F"  // 系统管理员
      ];
      
      for (const addr of possibleAdmins) {
        const hasAdmin = await agentSystem.hasRole(ADMIN_ROLE, addr);
        const hasDefaultAdmin = await agentSystem.hasRole(DEFAULT_ADMIN_ROLE, addr);
        
        if (hasAdmin || hasDefaultAdmin) {
          console.log(`   ✅ ${addr}: ${hasAdmin ? 'ADMIN' : ''} ${hasDefaultAdmin ? 'DEFAULT_ADMIN' : ''}`);
        }
      }
      
    } catch (error) {
      console.log(`   ❌ 检查管理员失败: ${error.message}`);
    }

    // 5. 尝试直接升级（如果有权限）
    console.log("\n5️⃣ 尝试直接升级测试...");
    
    try {
      const [deployer] = await ethers.getSigners();
      console.log(`   测试账户: ${deployer.address}`);
      
      // 检查当前账户权限
      const ADMIN_ROLE = await agentSystem.ADMIN_ROLE();
      const hasAdmin = await agentSystem.hasRole(ADMIN_ROLE, deployer.address);
      
      if (hasAdmin) {
        console.log(`   ✅ 当前账户有管理员权限，尝试直接升级...`);
        
        try {
          // 估算gas
          const gasEstimate = await agentSystem.upgradeTo.estimateGas(NEW_IMPLEMENTATION);
          console.log(`   预估Gas: ${gasEstimate.toString()}`);
          
          // 执行升级
          const upgradeTx = await agentSystem.upgradeTo(NEW_IMPLEMENTATION);
          console.log(`   ⏳ 直接升级交易已提交: ${upgradeTx.hash}`);
          
          const receipt = await upgradeTx.wait();
          console.log(`   ✅ 直接升级成功! Gas使用: ${receipt.gasUsed.toString()}`);
          
          // 验证升级结果
          const provider = ethers.provider;
          const implementationSlot = "0x360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc";
          const newImplStorage = await provider.send("eth_getStorageAt", [AGENT_SYSTEM_PROXY, implementationSlot, "latest"]);
          const newCurrentImplAddress = "0x" + newImplStorage.slice(-40);
          
          if (newCurrentImplAddress.toLowerCase() === NEW_IMPLEMENTATION.toLowerCase()) {
            console.log(`   🎉 升级成功！新实现地址: ${newCurrentImplAddress}`);
            
            // 测试新功能
            try {
              const testUser = "******************************************";
              const validation = await agentSystem.validateUserLevel(testUser);
              console.log(`   ✅ 新功能测试成功，升级逻辑修复已生效`);
              console.log(`   测试用户等级有效: ${validation[0] ? '✅' : '❌'}`);
            } catch (error) {
              console.log(`   ❌ 新功能测试失败: ${error.message}`);
            }
            
          } else {
            console.log(`   ❌ 升级失败，实现地址未更新`);
          }
          
        } catch (error) {
          console.log(`   ❌ 直接升级失败: ${error.message}`);
        }
        
      } else {
        console.log(`   ❌ 当前账户没有管理员权限`);
      }
      
    } catch (error) {
      console.log(`   ❌ 直接升级测试失败: ${error.message}`);
    }

    // 6. 分析多签升级失败的具体原因
    console.log("\n6️⃣ 分析多签升级失败的具体原因...");
    
    try {
      // 模拟多签调用
      const upgradeCalldata = "0x3659cfe60000000000000000000000001895fe4d13dc43b9ffa9b2ee2e020023c16f42c2";
      
      try {
        // 尝试静态调用来检查会发生什么
        const result = await ethers.provider.call({
          to: AGENT_SYSTEM_PROXY,
          data: upgradeCalldata,
          from: MULTISIG_ADDRESS
        });
        console.log(`   ✅ 模拟多签升级调用成功`);
      } catch (error) {
        console.log(`   ❌ 模拟多签升级调用失败: ${error.message}`);
        
        if (error.message.includes("AccessControl")) {
          console.log(`   🚨 根本原因: 多签钱包没有ADMIN_ROLE权限`);
          console.log(`   解决方案: 需要给多签钱包授予ADMIN_ROLE权限`);
        } else if (error.message.includes("ERC1967Upgrade")) {
          console.log(`   🚨 根本原因: UUPS升级错误`);
        } else if (error.message.includes("Address: low-level delegate call failed")) {
          console.log(`   🚨 根本原因: 委托调用失败`);
        }
      }
      
    } catch (error) {
      console.log(`   ❌ 模拟调用失败: ${error.message}`);
    }

    // 7. 提供解决方案
    console.log("\n7️⃣ 解决方案建议...");
    console.log("=".repeat(50));
    
    console.log("\n🔧 可能的解决方案:");
    console.log("1. 权限问题解决:");
    console.log("   - 给多签钱包授予ADMIN_ROLE权限");
    console.log("   - 或者使用有权限的账户直接升级");
    console.log("");
    console.log("2. 如果当前账户有权限:");
    console.log("   - 直接执行升级（上面已尝试）");
    console.log("");
    console.log("3. 授权多签钱包:");
    console.log("   npx hardhat run scripts/upgrade/agentsystem-grant-multisig-admin.js --network bscTestnet");
    console.log("");
    console.log("4. 检查升级结果:");
    console.log("   npx hardhat run scripts/verify-agentsystem-fix.js --network bscTestnet");

  } catch (error) {
    console.error("❌ 深度诊断失败:", error.message);
    console.error("详细错误:", error);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
