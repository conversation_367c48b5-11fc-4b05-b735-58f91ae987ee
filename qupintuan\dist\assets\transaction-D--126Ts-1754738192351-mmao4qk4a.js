const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/web3-DJUNf-KT-1754738192351-mmao4qk4a.js","assets/vendor-Caz4khA--1754738192351-mmao4qk4a.js","assets/universalNonceManager-Cuzfjoid-1754738192351-mmao4qk4a.js"])))=>i.map(i=>d[i]);
import{E as m,l as _,i as N}from"./index-DmsQauDE-1754738192351-rjh9jemju.js";import{_ as g}from"./vendor-Caz4khA--1754738192351-mmao4qk4a.js";async function O(r,o){if(typeof r!="function")throw new Error("txFn必须是一个返回Promise的函数");const{onTxHash:t,onReceipt:a,onError:d,timeout:l=12e4,useOptimizedNonce:f=!1,userAddress:I,isFirstTransaction:T=!1,operationName:i="交易"}=o||{};if(f&&I)return y(r,o);if(typeof t!="function"||typeof a!="function"||typeof d!="function")throw new Error("sendTx: onTxHash, onReceipt和onOnError回调都是必需的");if(typeof l!="number"||l<=0)throw new Error("timeout必须是大于0的数字");let e;try{if(e=await r(),typeof e=="string"){t(e);const n=await g(()=>import("./web3-DJUNf-KT-1754738192351-mmao4qk4a.js").then(w=>w.F),__vite__mapDeps([0,1])),{BrowserProvider:u}=n,p=await new u(window.ethereum).waitForTransaction(e,1,l);return a(p),p}if(e&&e.receipt&&!e.wait)return console.log("🔍 [sendTx] 处理包含 receipt 的结果对象:",{tx:e,hasRoomId:e.roomId!==void 0,hasReceipt:e.receipt!==void 0,receiptHash:e.receipt?.hash}),t(e.receipt.hash),a(e.receipt),e;if(!e?.wait&&e?.hash){t(e.hash);const n=await g(()=>import("./web3-DJUNf-KT-1754738192351-mmao4qk4a.js").then(w=>w.F),__vite__mapDeps([0,1])),{BrowserProvider:u}=n,p=await new u(window.ethereum).waitForTransaction(e.hash,1,l);return a(p),p}if(typeof e?.wait!="function")throw new Error("交易对象缺少 .wait() 方法，且不是有效的交易哈希")}catch(n){throw console.error("交易发送失败",n),d({code:"TX_SEND_FAILED",message:"无法发送交易，请检查网络连接或钱包配置",originalError:n}),{code:"TX_SEND_FAILED",message:"无法发送交易，请检查网络连接或钱包配置",originalError:n}}t(e.hash);let h;try{const n=new AbortController,u=setTimeout(()=>n.abort(),l);try{h=await e.wait();const c={...h,logs:h.logs||[]};return clearTimeout(u),a(c),c}catch(c){if(clearTimeout(u),c.name==="AbortError"){const w={code:"TX_CONFIRM_TIMEOUT",message:`交易确认超时(${l}ms)，请稍后手动检查交易状态。交易哈希: ${e?.hash||"未知"}`,originalError:c};throw console.error(w.message,c),d(w),w}const p={code:"TX_EXECUTION_FAILURE",message:`交易执行失败，请检查链上状态或 gas 配置。交易哈希: ${e?.hash||"未知"}`,originalError:c};throw console.error(p.message,p.originalError),d(p),p}}catch(n){console.error("交易执行失败",n);const u={code:"TX_EXECUTION_FAILED",message:"交易执行失败，请检查交易是否被拒绝或gas不足",originalError:n};throw d(u),u}}async function y(r,o){const{onTxHash:s,onReceipt:t,onError:a,timeout:d=12e4,userAddress:l,isFirstTransaction:f=!1,operationName:I="交易"}=o,{universalNonceManager:T}=await g(async()=>{const{universalNonceManager:i}=await import("./universalNonceManager-Cuzfjoid-1754738192351-mmao4qk4a.js");return{universalNonceManager:i}},__vite__mapDeps([2,0,1]));console.log("🚀 [sendTxWithOptimizedNonce] 开始优化交易:",{userAddress:l,isFirstTransaction:f,operationName:I,walletType:T.walletType});try{const i=await T.executeTransaction(l,async({nonce:h})=>{console.log("📞 [sendTxWithOptimizedNonce] 执行交易，Nonce:",h);const n=T.getOptimizedGasConfig({},f);return console.log("⛽ [sendTxWithOptimizedNonce] 使用Gas配置:",n),await r({nonce:h,gasConfig:n})},{highPriority:f,maxRetries:3,retryDelay:3e3,onProgress:h=>{console.log("📊 [sendTxWithOptimizedNonce] 交易进度:",h)},onNonceConflict:h=>{console.warn("⚠️ [sendTxWithOptimizedNonce] Nonce冲突:",h)}}),e=i.hash||i.txHash||i;if(typeof e=="string"){console.log("✅ [sendTxWithOptimizedNonce] 交易发送成功:",e),s&&s(e);const h=await g(()=>import("./web3-DJUNf-KT-1754738192351-mmao4qk4a.js").then(p=>p.F),__vite__mapDeps([0,1])),{BrowserProvider:n}=h,c=await new n(window.ethereum).waitForTransaction(e,1,d);return t&&t(c),T.markTransactionComplete(l,e),console.log("🎉 [sendTxWithOptimizedNonce] 交易确认成功:",{txHash:e,blockNumber:c.blockNumber,gasUsed:c.gasUsed?.toString()}),c}else{if(i.receipt)return s&&s(i.receipt.hash),t&&t(i.receipt),T.markTransactionComplete(l,i.receipt.hash),i;throw new Error("无效的交易结果格式")}}catch(i){throw console.error("❌ [sendTxWithOptimizedNonce] 交易失败:",i),a&&a({code:"OPTIMIZED_TX_FAILED",message:i.message,originalError:i}),i}}function E(r,o,s=null){const t=new Error(o);throw t.code=r,s&&(t.original=s),t}function x(r){!r&&(typeof r!="number"||typeof r?.toNumber!="function")&&E(m.MISSING_CHAIN_ID,"未检测到有效的链 ID，请检查钱包连接状态")}function b(r){/^[+]?((\d+(\.\d*)?)|(\.\d+))$/.test(r)||E(m.INVALID_TIER_AMOUNT,"无效的 tierAmount 字符串，必须为非负数字符串");const o=BigInt(r);return o<=0n&&E(m.INVALID_TIER_AMOUNT,"tierAmount 必须大于 0"),o}function C(r){r||E(m.MISSING_SIGNER,"未检测到钱包签名者，请安装 MetaMask 或其他以太坊兼容钱包")}function A(r,o){return O(r,{onTxHash:s=>_(`${o} tx hash`),onReceipt:s=>_(`${o} receipt`),onError:s=>{if(N(`${o} error`),s.message?.includes("nonce too low"))throw new Error("Nonce 值过低，请稍后重试或重启钱包应用");if(s.message?.includes("Cannot read properties of undefined"))throw new Error("参数错误，请重新连接钱包后重试")}})}async function v(r,o=3,s=3e3){let t;for(let a=1;a<=o;a++)try{_(`交易尝试 ${a}/${o}`);const d=await r();return _(`交易成功，尝试次数: ${a}`),d}catch(d){t=d,N(`交易失败，尝试 ${a}/${o}:`),a<o&&await new Promise(l=>setTimeout(l,s))}E(m.NETWORK_ERROR,`交易失败，已重试 ${o} 次`,t)}const F=Object.freeze(Object.defineProperty({__proto__:null,executeTransaction:A,retryTransaction:v},Symbol.toStringTag,{value:"Module"}));export{x as a,F as b,A as e,b as p,E as t,C as v};
