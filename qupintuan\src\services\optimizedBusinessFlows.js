// src/services/optimizedBusinessFlows.js
/**
 * 优化后的业务流程 - 使用通用Nonce管理和交易队列
 * 解决所有钱包的Nonce冲突问题，确保交易按正确顺序执行
 */

import { transactionQueue, BUSINESS_FLOWS } from '@/utils/transactionQueue.js';
import { universalNonceManager } from '@/utils/universalNonceManager.js';

/**
 * 优化后的创建拼团房间流程（3次弹窗）
 * @param {Object} signer - 钱包签名者
 * @param {string} tierAmountStr - 拼团金额字符串
 * @returns {Promise<Object>} 执行结果
 */
export async function optimizedCreateGroupBuy(signer, tierAmountStr) {
  const userAddress = signer.account?.address;
  if (!userAddress) {
    throw new Error('无法获取用户地址');
  }



  // 转换金额格式
  const tierNum = Number(tierAmountStr) / 1000000; // USDT 有 6 位小数精度

  // 动态导入API函数
  const {
    createRoomWithQPTVerification,
    approveQPTForCreate,
    lockQPTForCreate
  } = await import('@/apis/groupBuy/roomManagement.js');

  let stepResults = [];

  // 定义三个步骤的执行函数
  const stepFunctions = [
    // 步骤1：创建拼团房间
    async ({ nonce }) => {
      const gasConfig = universalNonceManager.getOptimizedGasConfig({}, true);

      return await createRoomWithQPTVerification({
        chainId: 97,
        tier: tierNum,
        signer,
        gasConfig,
        nonce
      });
    },

    // 步骤2：授权QPT给QPTLocker合约
    async ({ nonce }) => {
      const gasConfig = universalNonceManager.getOptimizedGasConfig({}, false);

      return await approveQPTForCreate({
        chainId: 97,
        tier: tierNum,
        signer,
        gasConfig,
        nonce
      });
    },

    // 步骤3：锁定QPT
    async ({ nonce }) => {
      const gasConfig = universalNonceManager.getOptimizedGasConfig({}, false);

      // 需要从步骤1的结果中获取roomId
      const roomId = stepResults[0]?.roomId;
      if (!roomId) {
        throw new Error('无法获取房间ID，请重试');
      }

      return await lockQPTForCreate({
        chainId: 97,
        tier: tierNum,
        roomId,
        signer,
        gasConfig,
        nonce
      });
    }
  ];

  // 执行业务流程
  const result = await transactionQueue.executeBusinessFlow(
    'CREATE_GROUP_BUY',
    userAddress,
    stepFunctions,
    {
      onStepComplete: (step, stepResult) => {
        // 保存步骤结果供后续步骤使用
        stepResults.push(stepResult);

        // 标记交易完成
        universalNonceManager.markTransactionComplete(
          userAddress,
          stepResult.hash || stepResult.txHash
        );
      }
    }
  );

  return {
    success: true,
    roomId: stepResults[0]?.roomId,
    receipt: stepResults[0]?.receipt,
    queueId: result.queueId,
    results: stepResults,
    message: '发起人三步操作完成：创建房间 → 授权QPT → 锁仓QPT'
  };
}

/**
 * 优化后的参与拼团房间流程（2次弹窗）
 * @param {Object} signer - 钱包签名者
 * @param {number} roomId - 房间ID
 * @returns {Promise<Object>} 执行结果
 */
export async function optimizedJoinGroupBuy(signer, roomId) {
  const userAddress = signer.account?.address;
  if (!userAddress) {
    throw new Error('无法获取用户地址');
  }



  // 动态导入API函数
  const {
    approveUSDTForJoin,
    joinRoomWithPaymentVerification
  } = await import('@/apis/groupBuy/roomManagement.js');

  // 定义两个步骤的执行函数
  const stepFunctions = [
    // 步骤1：授权USDT
    async ({ nonce }) => {
      const gasConfig = universalNonceManager.getOptimizedGasConfig({}, true);

      return await approveUSDTForJoin({
        chainId: 97,
        roomId,
        signer,
        gasConfig,
        nonce
      });
    },

    // 步骤2：加入拼团房间
    async ({ nonce }) => {
      const gasConfig = universalNonceManager.getOptimizedGasConfig({}, false);

      return await joinRoomWithPaymentVerification({
        chainId: 97,
        roomId,
        signer,
        gasConfig,
        nonce
      });
    }
  ];

  let stepResults = [];

  // 执行业务流程
  const result = await transactionQueue.executeBusinessFlow(
    'JOIN_GROUP_BUY',
    userAddress,
    stepFunctions,
    {
      onStepComplete: (step, stepResult) => {
        stepResults.push(stepResult);
        universalNonceManager.markTransactionComplete(
          userAddress, 
          stepResult.hash || stepResult.txHash
        );
      }
    }
  );

  return {
    success: true,
    roomId,
    receipt: stepResults[1]?.receipt,
    queueId: result.queueId,
    results: stepResults,
    message: '参与者两步操作完成：授权USDT → 加入房间'
  };
}

/**
 * 优化后的单个交易执行（通用）
 * @param {Object} params - 参数
 * @returns {Promise<Object>} 执行结果
 */
export async function optimizedSingleTransaction(params) {
  const {
    userAddress,
    transactionFn,
    operationName = '交易',
    isHighPriority = false
  } = params;



  try {
    // 使用通用Nonce管理器执行交易
    const result = await universalNonceManager.executeTransaction(
      userAddress,
      async ({ nonce }) => {
        // 获取优化的Gas配置
        const gasConfig = universalNonceManager.getOptimizedGasConfig({}, isHighPriority);

        // 执行交易函数
        return await transactionFn({ nonce, gasConfig });
      },
      {
        highPriority: isHighPriority,
        maxRetries: 3,
        retryDelay: 3000
      }
    );

    // 标记交易完成
    universalNonceManager.markTransactionComplete(
      userAddress,
      result.hash || result.txHash
    );

    return {
      success: true,
      result,
      txHash: result.hash || result.txHash,
      message: `${operationName}完成`
    };

  } catch (error) {
    throw error;
  }
}
