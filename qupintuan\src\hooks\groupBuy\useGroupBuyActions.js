// src/hooks/groupBuy/useGroupBuyActions.js
import { useCallback, useState } from 'react';
import { useAccount, useConnect } from 'wagmi';
import { useCreateAndLockRoom } from '@/hooks/useCreateAndLockRoom';
import { useJoinRoom } from '@/hooks/useJoinRoom';
import { scheduleDataRefresh } from '@/utils/dataRefreshManager';
import { logDebug, logError } from '@/services/logger';
import toast from 'react-hot-toast';

const TIERS = ['30', '50', '100', '200', '500', '1000'];
const USDT_DECIMALS = 1e6;

// 全局锁，防止并发创建房间（解决React.StrictMode双重调用问题）
const createRoomLocks = new Map();

export function useGroupBuyActions() {
  const { address: account } = useAccount();
  const { connect, connectors } = useConnect();
  const { createAndLock } = useCreateAndLockRoom();
  const { mutate: joinRoom } = useJoinRoom();

  // 状态管理
  const [pendingTiers, setPendingTiers] = useState({});
  const [joiningRooms, setJoiningRooms] = useState({});
  const [claimingRooms, setClaimingRooms] = useState({});

  // 连接钱包
  const connectWallet = useCallback(() => {
    const injectedConnector = connectors.find(
      (connector) => connector.id === 'injected'
    );

    if (injectedConnector) {
      return connect({ connector: injectedConnector });
    }
    return Promise.reject(new Error('未找到 Injected Connector'));
  }, [connect, connectors]);

  // 创建房间
  const handleCreate = useCallback(
    async (tier) => {
      const lockKey = `${account}-${tier}`;

      // 防重复创建检查
      if (createRoomLocks.has(lockKey)) {
        return;
      }

      // 设置锁
      createRoomLocks.set(lockKey, true);

      const logContext = {
        component: 'useGroupBuyActions',
        function: 'handleCreate',
        tier,
        account,
        timestamp: new Date().toISOString()
      };

      try {
        // 检查钱包连接
        if (!account) {
          await connectWallet();
          return;
        }



        // 检查用户注册状态
        const { checkUserRegistered } = await import('@/apis/agentSystemApi');
        const isRegistered = await checkUserRegistered({ userAddress: account });

        if (!isRegistered) {
          toast.error('请先注册代理系统才能发起拼团！', {
            duration: 5000,
            position: 'top-center',
          });
          return;
        }

        // 验证档位
        if (!TIERS.includes(tier)) {
          const validTiers = TIERS.filter(t => t);
          throw {
            code: 'INVALID_TIER',
            message: `不支持的档位: ${tier}`,
            uiMessage: `请选择支持的档位：${validTiers.join('、')}。当前输入: ${tier}`,
            tier
          };
        }

        // 设置加载状态
        setPendingTiers(prev => ({ ...prev, [tier]: true }));

        // 格式化档位金额
        const formattedTier = (parseFloat(tier) * USDT_DECIMALS).toString();

        // 调用创建函数
        const result = await createAndLock(formattedTier);

        // 移除重复的成功提示 - createAndLock 函数内部已经显示了成功消息

        // 刷新数据
        if (typeof window !== 'undefined' && window.refreshTokenBalances) {
          window.refreshTokenBalances();
        }

        logDebug({
          ...logContext,
          roomId: result.roomId,
          message: 'createAndLock success'
        });

        return result;

      } catch (err) {
        console.error('❌ [handleCreate] 创建失败:', err);

        // 根据错误类型显示不同的提示信息
        let errorMessage = '创建失败';
        let errorDuration = 5000;

        if (err.userFriendly) {
          // 使用增强的错误信息
          const { title, message, suggestion, roomId } = err.userFriendly;
          errorMessage = `${title}: ${message}`;
          if (suggestion) {
            errorMessage += `\n${suggestion}`;
          }
          if (roomId) {
            errorMessage += `\n⚠️ 房间 #${roomId} 已创建但未完成，请勿参与！`;
            errorDuration = 8000; // 更长的显示时间
          }
        } else if (err.step && err.stepName) {
          // 显示步骤信息
          errorMessage = `创建失败：在第${err.step}步"${err.stepName}"时出错`;
          if (err.roomId) {
            errorMessage += `\n⚠️ 房间 #${err.roomId} 已创建但未完成，请勿参与！`;
            errorDuration = 8000;
          }
        } else {
          // 通用错误信息
          errorMessage = `创建失败: ${err.message || '未知错误'}`;
        }

        toast.error(errorMessage, {
          duration: errorDuration,
          position: 'top-center',
          style: {
            maxWidth: '400px',
            whiteSpace: 'pre-line' // 支持换行显示
          }
        });

        logError({
          ...logContext,
          error: err,
          message: 'createAndLock failed',
          step: err.step,
          stepName: err.stepName,
          roomId: err.roomId
        });
      } finally {
        // 清理锁和状态
        createRoomLocks.delete(lockKey);
        setPendingTiers(prev => ({ ...prev, [tier]: false }));
      }
    },
    [account, connectWallet, createAndLock]
  );

  // 参与房间
  const handleJoinRoom = useCallback(
    async (room) => {
      // 立即设置加载状态，防止重复点击
      setJoiningRooms(prev => ({ ...prev, [room.id]: true }));

      try {
        // 检查钱包连接
        if (!account) {
          setJoiningRooms(prev => ({ ...prev, [room.id]: false }));
          await connectWallet();
          return;
        }

        // 检查用户注册状态
        const { checkUserRegistered } = await import('@/apis/agentSystemApi');
        const isRegistered = await checkUserRegistered({ userAddress: account });

        if (!isRegistered) {
          setJoiningRooms(prev => ({ ...prev, [room.id]: false }));
          toast.error('请先注册代理系统才能参与拼团！', {
            duration: 5000,
            position: 'top-center',
          });
          return;
        }

        // 验证基本参与条件
        const { validateJoinRoomConditions, approveUSDTForJoin, joinRoomWithPaymentVerification } = await import('@/apis/groupBuy/roomManagement');
        const validation = await validateJoinRoomConditions({
          chainId: 97,
          roomId: room.id,
          userAddress: account
        });

        // 检查基本条件（房间状态、用户状态等，但不检查授权状态）
        const basicErrors = validation.errors.filter(error =>
          !error.includes('授权') && !error.includes('余额不足')
        );

        if (basicErrors.length > 0) {
          setJoiningRooms(prev => ({ ...prev, [room.id]: false }));
          const errorMessage = basicErrors.join('; ');
          toast.error(`无法参与拼团: ${errorMessage}`, {
            duration: 5000,
            position: 'top-center',
          });
          return;
        }

        // 检查USDT余额
        if (validation.usdtInfo && !validation.usdtInfo.hasEnoughBalance) {
          setJoiningRooms(prev => ({ ...prev, [room.id]: false }));
          toast.error(`USDT余额不足。需要: ${validation.usdtInfo.tierAmount} USDT，当前: ${validation.usdtInfo.balance} USDT`, {
            duration: 5000,
            position: 'top-center',
          });
          return;
        }

        // 获取钱包客户端
        const { getWalletClient } = await import('wagmi/actions');
        const { config } = await import('@/wagmi.config.js');
        const walletClient = await getWalletClient(config);

        if (!walletClient) {
          setJoiningRooms(prev => ({ ...prev, [room.id]: false }));
          toast.error('无法获取钱包客户端');
          return;
        }

        // 检测移动端环境并使用优化的交易处理
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
                         window.ethereum?.isTokenPocket ||
                         window.ethereum?.isTrust ||
                         window.ethereum?.isImToken ||
                         window.ethereum?.isMathWallet;



        // 固定的两步操作流程（使用优化的交易处理）
        try {
          if (isMobile && account) {
            // 移动端使用优化的交易队列处理
            // 动态导入优化的业务流程
            const { optimizedJoinGroupBuy } = await import('@/services/optimizedBusinessFlows.js');

            const result = await optimizedJoinGroupBuy(walletClient, room.id);

            if (result.success) {
              const joinResult = result.results[1]; // 第二步的结果

              if (joinResult.receipt && joinResult.receipt.status === 'success') {
                const paymentInfo = joinResult.paymentVerification;
                const paymentStatus = paymentInfo.paymentVerified ? '✅ 支付验证通过' : '⚠️ 支付验证异常';

                toast.success(`🎉 参与拼团成功！房间ID: ${room.id}\n${paymentStatus}\n实际支付: ${paymentInfo.actualPaid} USDT`, {
                  duration: 5000,
                  position: 'top-center',
                });
              }
            }
          } else {
            // 桌面端使用原来的处理方式

            // 第1步：强制重新授权USDT
            toast(`第1步：正在重新授权 ${validation.usdtInfo.tierAmount} USDT...`, {
              duration: 3000,
              position: 'top-center',
            });

            const approveResult = await approveUSDTForJoin({
              chainId: 97,
              roomId: room.id,
              signer: walletClient
            });

            if (approveResult.success) {
              toast.success(`✅ 第1步完成：${approveResult.message}`, {
                duration: 3000,
                position: 'top-center',
              });
            }

            // 第2步：加入拼团房间并验证支付
            toast('第2步：正在加入拼团房间并验证支付...', {
              duration: 3000,
              position: 'top-center',
            });

            const joinResult = await joinRoomWithPaymentVerification({
              chainId: 97,
              roomId: room.id,
              signer: walletClient
            });

            if (joinResult.receipt && joinResult.receipt.status === 'success') {
              const paymentInfo = joinResult.paymentVerification;
              const paymentStatus = paymentInfo.paymentVerified ? '✅ 支付验证通过' : '⚠️ 支付验证异常';

              toast.success(`🎉 参与拼团成功！房间ID: ${room.id}\n${paymentStatus}\n实际支付: ${paymentInfo.actualPaid} USDT`, {
                duration: 5000,
                position: 'top-center',
              });
            }
          }

          // 刷新数据（移动端和桌面端都需要）
          try {
            scheduleDataRefresh('all', 800);
          } catch (error) {
            console.error('❌ 数据刷新失败:', error);
          }

        } catch (stepError) {
          console.error('❌ [handleJoinRoom] 操作步骤失败:', stepError);
          toast.error(`操作失败: ${stepError.message}`, {
            duration: 5000,
            position: 'top-center',
          });
          // 注意：这里不需要手动清除状态，因为会在 finally 块中处理
          return;
        }

      } catch (err) {
        console.error('❌ [handleJoinRoom] 参与失败:', err);
        toast.error(err.message || '参与拼团失败，请重试');
      } finally {
        // 清除加载状态
        setJoiningRooms(prev => ({ ...prev, [room.id]: false }));
      }
    },
    [account, connectWallet, joinRoom]
  );

  // 领取奖励
  const handleClaimReward = useCallback(
    async (room, updateClaimedStatus) => {
      try {
        if (!account) {
          await connectWallet();
          return;
        }

        // 检查用户注册状态
        const { checkUserRegistered } = await import('@/apis/agentSystemApi');
        const isRegistered = await checkUserRegistered({ userAddress: account });

        if (!isRegistered) {
          toast.error('请先注册代理系统才能领取奖励！', {
            duration: 5000,
            position: 'top-center',
          });
          return;
        }

        // 检查房间状态
        if (!room.isClosed) {
          toast.error('房间尚未关闭，无法领取');
          return;
        }

        // 区分成功房间和失败房间的处理逻辑
        const isCreator = account.toLowerCase() === room.creator.toLowerCase();
        const isParticipant = room.participants.some(p => p.toLowerCase() === account.toLowerCase());

        if (room.isSuccessful) {
          // 成功房间：创建者和参与者都可以领取奖励
          if (!isCreator && !isParticipant) {
            toast.error('您不是此房间的参与者或创建者，无法领取奖励');
            return;
          }
        } else {
          // 失败房间：只有参与者可以申请退款
          if (!isParticipant) {
            toast.error('只有参与者可以申请退款');
            return;
          }
          // 参与者都可以退款（包括既是创建者又是参与者的情况）
        }

        setClaimingRooms(prev => ({ ...prev, [room.id]: true }));

        // 创建 signer - 使用 wagmi walletClient
        const { getWalletClient } = await import('wagmi/actions');
        const { config } = await import('@/wagmi.config');

        const signer = await getWalletClient(config);

        if (!signer) {
          throw new Error('钱包客户端未连接，请重新连接钱包');
        }

        // 验证钱包地址
        if (account.toLowerCase() !== signer.account.address.toLowerCase()) {
          throw new Error(`账户地址不匹配！当前账户: ${account}, Signer地址: ${signer.account.address}`);
        }



        try {
          const { checkClaimEligibility } = await import('@/apis/claimRewardStable');
          const eligibility = await checkClaimEligibility({
            chainId: 97,
            roomId: room.id,
            userAddress: account
          });

          if (!eligibility.canClaim) {
            toast.error(`无法领取: ${eligibility.reason}`);
            return;
          }


        } catch (preCheckError) {
          console.warn('⚠️ [handleClaimReward] 预检查失败，使用备用验证:', preCheckError.message);

          // 备用验证逻辑
          const isCreator = account.toLowerCase() === room.creator.toLowerCase();
          const isParticipant = room.participants.some(p => p.toLowerCase() === account.toLowerCase());

          if (!isCreator && !isParticipant) {
            toast.error('您不是此房间的参与者或创建者，无法领取奖励');
            return;
          }

          if (room.isSuccessful && !isCreator && !isParticipant) {
            toast.error('成功房间只有创建者和参与者可以领取');
            return;
          }

          if (!room.isSuccessful && !isParticipant) {
            toast.error('失败房间只有参与者可以退款');
            return;
          }

          // 对于失败房间，参与者都可以退款（包括既是创建者又是参与者的情况）
        }

        // 调用领取API
        const { claimReward: apiClaimReward } = await import('@/apis/groupBuyApi');
        const result = await apiClaimReward({
          chainId: 97,
          roomId: room.id,
          signer
        });

        toast.success(`🎉 趣拼团奖励领取成功！房间ID: ${room.id} - 愛拼才会赢！`, {
          duration: 4000,
          position: 'top-center',
        });

        // 立即更新本地状态
        if (updateClaimedStatus) {
          updateClaimedStatus(room.id, account, true);
        }

        // 刷新数据
        if (typeof window !== 'undefined' && window.refreshTokenBalances) {
          window.refreshTokenBalances();
        }

        // 触发管理后台数据同步
        try {
          const { triggerRewardSync } = await import('@/utils/adminDataSync');
          triggerRewardSync({
            type: 'reward_claimed',
            roomId: room.id,
            userAddress: account,
            timestamp: new Date()
          });
        } catch (syncError) {
          console.warn('⚠️ 数据同步失败:', syncError);
        }

        return result;

      } catch (err) {
        console.error('❌ [handleClaimReward] 领取失败:', err);
        toast.error(`领取失败: ${err.message}`);
      } finally {
        setClaimingRooms(prev => ({ ...prev, [room.id]: false }));
      }
    },
    [account, connectWallet]
  );

  // 分离的领取函数 - 创建者佣金
  const handleClaimCreatorCommission = useCallback(
    async (room, updateClaimedStatus) => {
      try {
        if (!account) {
          await connectWallet();
          return;
        }

        // 检查是否是创建者
        const isCreator = account.toLowerCase() === room.creator.toLowerCase();
        if (!isCreator) {
          toast.error('您不是此房间的创建者');
          return;
        }

        setClaimingRooms(prev => ({ ...prev, [room.id]: true }));

        // 创建 signer
        const { getWalletClient } = await import('wagmi/actions');
        const { config } = await import('@/wagmi.config');

        const signer = await getWalletClient(config);

        if (!signer) {
          throw new Error('钱包客户端未连接，请重新连接钱包');
        }



        // 调用专门的创建者佣金领取API
        const { claimCreatorCommission } = await import('@/apis/groupBuyApi');
        const result = await claimCreatorCommission({
          chainId: 97,
          roomId: room.id,
          signer
        });

        // 成功消息由RoomActions.jsx统一处理，避免重复弹出

        // 更新状态
        if (updateClaimedStatus) {
          updateClaimedStatus(room.id, account, true);
        }

        // 刷新数据
        if (typeof window !== 'undefined' && window.refreshTokenBalances) {
          window.refreshTokenBalances();
        }

        return result;

      } catch (err) {
        console.error('❌ [handleClaimCreatorCommission] 领取失败:', err);
        toast.error(`发起人佣金领取失败: ${err.message}`);
      } finally {
        setClaimingRooms(prev => ({ ...prev, [room.id]: false }));
      }
    },
    [account, connectWallet]
  );

  // 分离的领取函数 - 赢家奖励 (QPT + 积分)
  const handleClaimWinnerReward = useCallback(
    async (room, updateClaimedStatus) => {
      try {
        if (!account) {
          await connectWallet();
          return;
        }

        // 检查是否是赢家 - 使用与 RoomActions.jsx 一致的判断逻辑
        const isWinner = room.winner && room.winner.address &&
                         room.winner.address.toLowerCase() === account.toLowerCase();



        if (!isWinner) {
          toast.error('您不是此房间的赢家');
          return;
        }

        setClaimingRooms(prev => ({ ...prev, [room.id]: true }));

        // 创建 signer
        const { getWalletClient } = await import('wagmi/actions');
        const { config } = await import('@/wagmi.config');

        const signer = await getWalletClient(config);

        if (!signer) {
          throw new Error('钱包客户端未连接，请重新连接钱包');
        }

        // 调用专门的赢家奖励领取API
        const { claimWinnerReward } = await import('@/apis/groupBuyApi');
        const result = await claimWinnerReward({
          chainId: 97,
          roomId: room.id,
          signer
        });

        // 成功消息由RoomActions.jsx统一处理，避免重复弹出

        // 更新状态
        if (updateClaimedStatus) {
          updateClaimedStatus(room.id, account, true);
        }

        // 刷新数据
        if (typeof window !== 'undefined' && window.refreshTokenBalances) {
          window.refreshTokenBalances();
        }

        return result;

      } catch (err) {
        console.error('❌ [handleClaimWinnerReward] 领取失败:', err);
        toast.error(`赢家奖励领取失败: ${err.message}`);
      } finally {
        setClaimingRooms(prev => ({ ...prev, [room.id]: false }));
      }
    },
    [account, connectWallet]
  );

  // 分离的领取函数 - 非赢家参与者本金+补贴
  const handleClaimParticipantRefund = useCallback(
    async (room, updateClaimedStatus) => {
      try {
        if (!account) {
          await connectWallet();
          return;
        }

        // 检查是否是参与者且非赢家 - 使用与 RoomActions.jsx 一致的判断逻辑
        const isParticipant = room.participants?.some(p => p.toLowerCase() === account.toLowerCase());
        const isWinner = room.winner && room.winner.address &&
                         room.winner.address.toLowerCase() === account.toLowerCase();



        if (!isParticipant) {
          toast.error('您不是此房间的参与者');
          return;
        }

        if (isWinner) {
          toast.error('赢家不能领取本金退款，请领取赢家奖励');
          return;
        }

        setClaimingRooms(prev => ({ ...prev, [room.id]: true }));

        // 创建 signer
        const { getWalletClient } = await import('wagmi/actions');
        const { config } = await import('@/wagmi.config');

        const signer = await getWalletClient(config);

        if (!signer) {
          throw new Error('钱包客户端未连接，请重新连接钱包');
        }

        // 调用专门的参与者退款API
        const { claimParticipantRefund } = await import('@/apis/groupBuyApi');
        const result = await claimParticipantRefund({
          chainId: 97,
          roomId: room.id,
          signer
        });

        // 成功消息由RoomActions.jsx统一处理，避免重复弹出

        // 更新状态
        if (updateClaimedStatus) {
          updateClaimedStatus(room.id, account, true);
        }

        // 刷新数据
        if (typeof window !== 'undefined' && window.refreshTokenBalances) {
          window.refreshTokenBalances();
        }

        return result;

      } catch (err) {
        console.error('❌ [handleClaimParticipantRefund] 领取失败:', err);
        toast.error(`本金和补贴领取失败: ${err.message}`);
      } finally {
        setClaimingRooms(prev => ({ ...prev, [room.id]: false }));
      }
    },
    [account, connectWallet]
  );

  // 分离的领取函数 - 过期房间本金退款
  const handleClaimPrincipalRefund = useCallback(
    async (room, updateClaimedStatus) => {
      try {
        if (!account) {
          await connectWallet();
          return;
        }

        // 检查是否是参与者或创建者
        const isCreator = account.toLowerCase() === room.creator.toLowerCase();
        const isParticipant = room.participants?.some(p => p.toLowerCase() === account.toLowerCase());

        if (!isCreator && !isParticipant) {
          toast.error('您不是此房间的参与者或创建者');
          return;
        }

        // 检查房间是否失败/过期
        if (room.isSuccessful) {
          toast.error('成功房间不能使用此功能，请使用对应的领取按钮');
          return;
        }

        setClaimingRooms(prev => ({ ...prev, [room.id]: true }));

        // 创建 signer
        const { getWalletClient } = await import('wagmi/actions');
        const { config } = await import('@/wagmi.config');

        const signer = await getWalletClient(config);

        if (!signer) {
          throw new Error('钱包客户端未连接，请重新连接钱包');
        }

        // 调用通用的 claimReward 函数（过期房间会退本金，现在支持无 tierAmount）
        const { claimReward } = await import('@/apis/groupBuyApi');
        const result = await claimReward({
          chainId: 97,
          roomId: room.id,
          signer
        });

        toast.success(`💰 本金退款领取成功！房间ID: ${room.id}`, {
          duration: 4000,
          position: 'top-center',
        });

        // 更新状态
        if (updateClaimedStatus) {
          updateClaimedStatus(room.id, account, true);
        }

        // 刷新数据
        if (typeof window !== 'undefined' && window.refreshTokenBalances) {
          window.refreshTokenBalances();
        }

        return result;

      } catch (err) {
        console.error('❌ [handleClaimPrincipalRefund] 领取失败:', err);
        toast.error(`本金退款领取失败: ${err.message}`);
      } finally {
        setClaimingRooms(prev => ({ ...prev, [room.id]: false }));
      }
    },
    [account, connectWallet]
  );

  // 领取锁仓QPT
  const handleClaimLockedQPT = useCallback(
    async (room, updateLockInfo) => {
      try {
        if (!account) {
          await connectWallet();
          return;
        }

        // 检查用户注册状态
        const { checkUserRegistered } = await import('@/apis/agentSystemApi');
        const isRegistered = await checkUserRegistered({ userAddress: account });

        if (!isRegistered) {
          toast.error('请先注册代理系统才能领取锁仓QPT！', {
            duration: 5000,
            position: 'top-center',
          });
          return;
        }

        // 检查是否是发起人
        if (account.toLowerCase() !== room.creator.toLowerCase()) {
          toast.error('只有发起人可以领取锁仓QPT');
          return;
        }

        setClaimingRooms(prev => ({ ...prev, [`qpt-${room.id}`]: true }));

        // 调用领取API（不需要传递signer，使用wagmi内部的连接）
        const { claimLockedQPT: apiClaimLockedQPT } = await import('@/apis/qptLockerApi');
        const result = await apiClaimLockedQPT({
          chainId: 97,
          roomId: room.id,
          signer: { account: { address: account } } // 简化的signer对象
        });

        toast.success(`🎉 锁仓QPT领取成功！房间ID: ${room.id}`, {
          duration: 4000,
          position: 'top-center',
        });

        // 立即更新本地锁仓状态
        if (updateLockInfo) {
          updateLockInfo(room.id, { isClaimed: true });
        }

        // 刷新数据
        if (typeof window !== 'undefined' && window.refreshTokenBalances) {
          window.refreshTokenBalances();
        }

        // 触发管理后台数据同步
        try {
          const { triggerQPTSync } = await import('@/utils/adminDataSync');
          triggerQPTSync({
            type: 'qpt_unlocked',
            roomId: room.id,
            userAddress: account,
            timestamp: new Date()
          });
        } catch (syncError) {
          console.warn('⚠️ 数据同步失败:', syncError);
        }

        // 记录QPT解锁操作（手动跟踪）
        try {
          const { recordQPTUnlock } = await import('@/utils/qptOperationTracker');
          // 获取实际解锁的QPT数量，从合约查询结果中获取
          let actualAmount = '100'; // 默认值

          try {
            // 尝试从合约查询实际锁仓数量
            const { getRoomLockInfo } = await import('@/apis/qptLockerApi');
            const lockInfo = await getRoomLockInfo({
              chainId: 97,
              roomId: room.id
            });
            actualAmount = (Number(lockInfo.amount) / 1e18).toString();
          } catch (queryError) {
            console.warn('⚠️ 查询实际QPT数量失败，使用默认值:', queryError);
          }

          recordQPTUnlock(room.id.toString(), account, actualAmount);
        } catch (trackError) {
          console.warn('⚠️ QPT操作跟踪失败:', trackError);
        }

        return result;

      } catch (err) {
        console.error('❌ [handleClaimLockedQPT] 领取失败:', err);
        toast.error(`领取锁仓QPT失败: ${err.message}`);
      } finally {
        setClaimingRooms(prev => ({ ...prev, [`qpt-${room.id}`]: false }));
      }
    },
    [account, connectWallet]
  );

  // 处理房间过期
  const handleExpireRoom = useCallback(
    async (room) => {
      try {
        if (!account) {
          await connectWallet();
          return;
        }

        // 检查用户注册状态
        const { checkUserRegistered } = await import('@/apis/agentSystemApi');
        const isRegistered = await checkUserRegistered({ userAddress: account });

        if (!isRegistered) {
          toast.error('请先注册代理系统才能处理过期房间！', {
            duration: 5000,
            position: 'top-center',
          });
          return;
        }

        // 检查房间是否真的过期
        if (room.timeLeft > 0) {
          toast.error('房间尚未过期，无法处理');
          return;
        }

        if (room.isClosed) {
          toast.error('房间已关闭，无需处理');
          return;
        }

        setClaimingRooms(prev => ({ ...prev, [`expire-${room.id}`]: true }));

        // 创建 signer - 使用 wagmi walletClient
        const { getWalletClient } = await import('wagmi/actions');
        const { config } = await import('@/wagmi.config');

        const signer = await getWalletClient(config);

        if (!signer) {
          throw new Error('钱包客户端未连接，请重新连接钱包');
        }

        // 验证钱包地址
        if (account.toLowerCase() !== signer.account.address.toLowerCase()) {
          throw new Error(`账户地址不匹配！当前账户: ${account}, Signer地址: ${signer.account.address}`);
        }

        // 调用过期API
        const { expireRoom: apiExpireRoom } = await import('@/apis/groupBuyApi');
        const result = await apiExpireRoom({
          chainId: 97,
          roomId: room.id,
          signer
        });

        toast.success(`🎉 房间过期处理成功！房间ID: ${room.id}`, {
          duration: 4000,
          position: 'top-center',
        });

        // 刷新数据
        if (typeof window !== 'undefined' && window.refreshTokenBalances) {
          window.refreshTokenBalances();
        }

        // 立即更新房间状态
        try {
          const { handleExpiredRoomStatusUpdate } = await import('@/utils/roomStatusUpdater');
          handleExpiredRoomStatusUpdate(room.id, null); // 不传onRefreshRooms，避免重复刷新
        } catch (statusError) {
          console.warn('⚠️ 房间状态更新失败:', statusError);
        }

        // 刷新房间列表以更新状态
        if (typeof window !== 'undefined' && window.refreshRoomList) {
          window.refreshRoomList();
        }

        // 触发管理后台数据同步
        try {
          const { triggerRoomSync } = await import('@/utils/adminDataSync');
          triggerRoomSync({
            type: 'room_expired',
            roomId: room.id,
            userAddress: account,
            timestamp: new Date()
          });
        } catch (syncError) {
          console.warn('⚠️ 数据同步失败:', syncError);
        }

        return result;

      } catch (err) {
        console.error('❌ [handleExpireRoom] 处理失败:', err);
        toast.error(`处理过期房间失败: ${err.message}`);
      } finally {
        setClaimingRooms(prev => ({ ...prev, [`expire-${room.id}`]: false }));
      }
    },
    [account, connectWallet]
  );

  return {
    // 状态
    pendingTiers,
    joiningRooms,
    claimingRooms,

    // 操作函数
    handleCreate,
    handleJoinRoom,
    handleClaimReward,
    handleClaimCreatorCommission,
    handleClaimWinnerReward,
    handleClaimParticipantRefund,
    handleClaimPrincipalRefund,
    handleClaimLockedQPT,
    handleExpireRoom,
    connectWallet
  };
}
