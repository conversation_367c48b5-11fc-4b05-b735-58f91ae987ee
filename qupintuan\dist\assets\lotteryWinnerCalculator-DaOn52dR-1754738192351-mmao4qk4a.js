function i(r,n){if(!r||!n||n.length===0)throw new Error("交易哈希或参与者列表无效");if(n.length!==8)throw new Error(`参与者数量必须为8，当前为: ${n.length}`);const o=r.slice(-1).toLowerCase(),l=parseInt(o,16);let e;l<=1?e=0:l<=3?e=1:l<=5?e=2:l<=7?e=3:l<=9?e=4:l<=11?e=5:l<=13?e=6:e=7;const s=n[e];return{winnerAddress:s,winnerIndex:e,lastHexChar:o,lastHexDigit:l,calculationDetails:{txHash:r,lastHexChar:o,lastHexDigit:l,winnerIndex:e+1,winnerAddress:s}}}export{i as calculateWinnerFromTxHash};
