// qupintuan/hardhat/scripts/upgrade/agentsystem-direct-multisig-upgrade.js
// 直接通过多签钱包升级AgentSystem

const { ethers } = require("hardhat");

async function main() {
  console.log("🔐 直接通过多签钱包升级AgentSystem...\n");

  // 升级信息
  const MULTISIG_ADDRESS = "******************************************";
  const AGENT_SYSTEM_PROXY = "******************************************";
  const NEW_IMPLEMENTATION = "******************************************"; // 新部署的实现地址

  // 直接升级调用数据
  const UPGRADE_CALLDATA = "0x4f1ef2860000000000000000000000001895fe4d13dc43b9ffa9b2ee2e020023c16f42c200000000000000000000000000000000000000000000000000000000000000400000000000000000000000000000000000000000000000000000000000000000";

  try {
    const [deployer] = await ethers.getSigners();
    console.log(`🔑 提交账户: ${deployer.address}\n`);

    console.log("📋 升级信息:");
    console.log(`   多签钱包: ${MULTISIG_ADDRESS}`);
    console.log(`   代理合约: ${AGENT_SYSTEM_PROXY}`);
    console.log(`   新实现: ${NEW_IMPLEMENTATION}`);
    console.log(`   升级调用数据: ${UPGRADE_CALLDATA}`);
    console.log("");

    // 连接到多签钱包
    const multisig = await ethers.getContractAt("MultiSigWallet", MULTISIG_ADDRESS);

    // 1. 检查当前实现
    console.log("1️⃣ 检查当前实现...");
    
    const provider = ethers.provider;
    const implementationSlot = "0x360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc";
    const implStorage = await provider.send("eth_getStorageAt", [AGENT_SYSTEM_PROXY, implementationSlot, "latest"]);
    const currentImplAddress = "0x" + implStorage.slice(-40);
    
    console.log(`   当前实现: ${currentImplAddress}`);
    console.log(`   目标实现: ${NEW_IMPLEMENTATION}`);
    console.log(`   需要升级: ${currentImplAddress.toLowerCase() !== NEW_IMPLEMENTATION.toLowerCase() ? '✅' : '❌'}`);

    if (currentImplAddress.toLowerCase() === NEW_IMPLEMENTATION.toLowerCase()) {
      console.log("\n🎉 升级已完成，无需再次升级！");
      return;
    }

    // 2. 提交直接升级提案
    console.log("\n2️⃣ 提交直接升级提案...");
    
    try {
      const submitTx = await multisig.submitTransaction(
        AGENT_SYSTEM_PROXY,  // 直接调用代理合约
        0,                   // value
        UPGRADE_CALLDATA     // 升级调用数据
      );
      
      console.log(`   ⏳ 升级提案交易已提交: ${submitTx.hash}`);
      const receipt = await submitTx.wait();
      console.log(`   ✅ 升级提案交易确认成功!`);
      
      // 获取交易ID
      const submitEvents = receipt.logs.filter(log => {
        try {
          const parsed = multisig.interface.parseLog(log);
          return parsed.name === 'SubmitTransaction';
        } catch {
          return false;
        }
      });
      
      if (submitEvents.length > 0) {
        const parsed = multisig.interface.parseLog(submitEvents[0]);
        const transactionId = parsed.args.txIndex;
        console.log(`   📝 升级提案ID: ${transactionId}`);
        
        // 3. 检查提案状态
        console.log("\n3️⃣ 检查提案状态...");
        
        try {
          const txInfo = await multisig.getTransaction(transactionId);
          console.log(`   提案ID: ${transactionId}`);
          console.log(`   目标地址: ${txInfo.to}`);
          console.log(`   调用值: ${txInfo.value}`);
          console.log(`   已执行: ${txInfo.executed ? '✅' : '❌'}`);
          console.log(`   确认数: ${txInfo.numConfirmations}`);
          
          // 获取所需确认数
          const requiredConfirmations = await multisig.numConfirmationsRequired();
          console.log(`   所需确认数: ${requiredConfirmations}`);
          
          if (Number(txInfo.numConfirmations) >= Number(requiredConfirmations) && !txInfo.executed) {
            console.log(`   ✅ 提案已获得足够确认，可以执行`);
          } else if (txInfo.executed) {
            console.log(`   ✅ 提案已执行`);
          } else {
            console.log(`   ⏳ 等待更多确认 (${txInfo.numConfirmations}/${requiredConfirmations})`);
          }
          
        } catch (error) {
          console.log(`   ❌ 获取提案状态失败: ${error.message}`);
        }

        // 4. 保存提案信息
        console.log("\n4️⃣ 保存提案信息...");
        
        const proposalInfo = {
          timestamp: new Date().toISOString(),
          network: "bscTestnet",
          multisigAddress: MULTISIG_ADDRESS,
          proxyAddress: AGENT_SYSTEM_PROXY,
          newImplementation: NEW_IMPLEMENTATION,
          transactionId: Number(transactionId),
          upgradeCalldata: UPGRADE_CALLDATA,
          submitter: deployer.address,
          submitTxHash: submitTx.hash
        };
        
        const fs = require('fs');
        const filename = `agentsystem-direct-upgrade-proposal-${Date.now()}.json`;
        fs.writeFileSync(filename, JSON.stringify(proposalInfo, null, 2));
        console.log(`   ✅ 提案信息已保存到: ${filename}`);

        // 5. 下一步指南
        console.log("\n5️⃣ 下一步操作指南...");
        console.log("=".repeat(50));
        
        console.log(`\n🔐 多签钱包操作:`);
        console.log(`1. 登录多签钱包管理界面`);
        console.log(`2. 找到提案ID: ${transactionId}`);
        console.log(`3. 确认并执行该提案`);
        console.log(`4. 等待交易确认`);
        
        console.log(`\n📋 提案详情:`);
        console.log(`   提案ID: ${transactionId}`);
        console.log(`   目标合约: ${AGENT_SYSTEM_PROXY}`);
        console.log(`   新实现: ${NEW_IMPLEMENTATION}`);
        console.log(`   调用数据: ${UPGRADE_CALLDATA}`);
        
        console.log(`\n🔍 监控命令:`);
        console.log(`   npx hardhat run scripts/upgrade/agentsystem-monitor-direct-upgrade.js --network bscTestnet`);

        console.log("\n🎉 直接升级提案提交成功!");
        
      } else {
        console.log(`   ❌ 未找到SubmitTransaction事件`);
      }
      
    } catch (error) {
      console.log(`   ❌ 提交升级提案失败: ${error.message}`);
      
      if (error.message.includes("AccessControl")) {
        console.log(`   原因: 权限不足`);
      } else if (error.message.includes("Address: low-level delegate call failed")) {
        console.log(`   原因: 委托调用失败`);
      } else {
        console.log(`   原因: 未知错误`);
      }
      throw error;
    }

  } catch (error) {
    console.error("❌ 直接升级失败:", error.message);
    console.error("详细错误:", error);
    process.exit(1);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
