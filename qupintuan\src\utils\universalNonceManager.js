// src/utils/universalNonceManager.js
/**
 * 通用Nonce管理器 - 解决所有钱包的Nonce冲突问题
 * 支持TP钱包、MetaMask、Trust Wallet等所有主流钱包
 * 
 * 核心功能：
 * 1. 账户级别的Nonce管控
 * 2. 交易队列控制
 * 3. 实时Nonce获取
 * 4. 同Nonce替换策略
 * 5. 高优先级Gas策略
 */

import { createPublicClient, http } from 'viem';
import { bscTestnet } from 'viem/chains';

/**
 * 交易状态枚举
 */
export const TRANSACTION_STATUS = {
  PENDING: 'pending',
  CONFIRMED: 'confirmed',
  FAILED: 'failed',
  REPLACED: 'replaced'
};

/**
 * 钱包类型检测
 */
export function detectWalletType() {
  if (typeof window === 'undefined') return 'unknown';
  
  const userAgent = navigator.userAgent.toLowerCase();
  const ethereum = window.ethereum;
  
  if (userAgent.includes('tokenpocket') || ethereum?.isTokenPocket) {
    return 'tokenpocket';
  } else if (userAgent.includes('trustwallet') || ethereum?.isTrust) {
    return 'trust';
  } else if (userAgent.includes('imtoken') || ethereum?.isImToken) {
    return 'imtoken';
  } else if (ethereum?.isMetaMask) {
    return 'metamask';
  } else if (userAgent.includes('mobile')) {
    return 'mobile_unknown';
  }
  
  return 'desktop';
}

/**
 * 通用Nonce管理器类
 */
class UniversalNonceManager {
  constructor() {
    // 账户级别的Nonce缓存
    this.nonceCache = new Map();
    
    // 交易队列 - 每个账户维护一个队列
    this.transactionQueues = new Map();
    
    // 待处理交易计数
    this.pendingCounts = new Map();
    
    // 交易锁定状态
    this.accountLocks = new Map();
    
    // 公共客户端缓存
    this.publicClient = null;
    
    // 钱包类型
    this.walletType = detectWalletType();
  }

  /**
   * 获取公共客户端
   */
  getPublicClient() {
    if (!this.publicClient) {
      this.publicClient = createPublicClient({
        chain: bscTestnet,
        transport: http()
      });
    }
    return this.publicClient;
  }

  /**
   * 获取账户的下一个可用Nonce
   * @param {string} address - 钱包地址
   * @param {Object} options - 选项
   * @returns {Promise<number>} Nonce值
   */
  async getNextNonce(address, options = {}) {
    const normalizedAddress = address.toLowerCase();
    const { forceRefresh = false, highPriority = false } = options;
    
    try {

      // 1. 从区块链获取最新Nonce（包含pending交易）
      const publicClient = this.getPublicClient();
      const chainNonce = await publicClient.getTransactionCount({
        address: normalizedAddress,
        blockTag: 'pending'
      });

      // 2. 获取本地缓存的Nonce
      const cachedNonce = this.nonceCache.get(normalizedAddress) || 0;
      
      // 3. 获取当前待处理交易数量
      const pendingCount = this.pendingCounts.get(normalizedAddress) || 0;
      
      // 4. 计算下一个Nonce
      let nextNonce;
      if (forceRefresh) {
        // 强制刷新：直接使用链上Nonce
        nextNonce = chainNonce;
      } else {
        // 正常模式：使用较大值并考虑待处理交易
        nextNonce = Math.max(chainNonce, cachedNonce) + pendingCount;
      }
      
      // 5. 更新缓存
      this.nonceCache.set(normalizedAddress, nextNonce + 1);
      this.pendingCounts.set(normalizedAddress, pendingCount + 1);

      return nextNonce;
      
    } catch (error) {
      // 降级方案：基于时间戳的随机Nonce
      const fallbackNonce = Date.now() % 1000000;
      return fallbackNonce;
    }
  }

  /**
   * 锁定账户交易（防止并发）
   * @param {string} address - 钱包地址
   * @returns {Promise<Function>} 解锁函数
   */
  async lockAccount(address) {
    const normalizedAddress = address.toLowerCase();
    
    // 等待现有锁释放
    while (this.accountLocks.get(normalizedAddress)) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    // 设置锁
    this.accountLocks.set(normalizedAddress, true);

    // 返回解锁函数
    return () => {
      this.accountLocks.delete(normalizedAddress);
    };
  }

  /**
   * 标记交易完成
   * @param {string} address - 钱包地址
   * @param {string} txHash - 交易哈希
   * @param {string} status - 交易状态
   */
  markTransactionComplete(address, txHash, status = TRANSACTION_STATUS.CONFIRMED) {
    const normalizedAddress = address.toLowerCase();
    
    // 减少待处理交易计数
    const pendingCount = this.pendingCounts.get(normalizedAddress) || 0;
    if (pendingCount > 0) {
      this.pendingCounts.set(normalizedAddress, pendingCount - 1);
    }
  }

  /**
   * 清理账户缓存
   * @param {string} address - 钱包地址
   */
  clearAccountCache(address) {
    const normalizedAddress = address.toLowerCase();
    
    this.nonceCache.delete(normalizedAddress);
    this.pendingCounts.delete(normalizedAddress);
    this.accountLocks.delete(normalizedAddress);
  }

  /**
   * 获取账户状态
   * @param {string} address - 钱包地址
   * @returns {Object} 账户状态信息
   */
  getAccountStatus(address) {
    const normalizedAddress = address.toLowerCase();

    return {
      address: normalizedAddress,
      cachedNonce: this.nonceCache.get(normalizedAddress) || 0,
      pendingCount: this.pendingCounts.get(normalizedAddress) || 0,
      isLocked: this.accountLocks.get(normalizedAddress) || false,
      walletType: this.walletType
    };
  }

  /**
   * 执行带Nonce管理的交易
   * @param {string} address - 钱包地址
   * @param {Function} transactionFn - 交易执行函数
   * @param {Object} options - 选项
   * @returns {Promise<Object>} 交易结果
   */
  async executeTransaction(address, transactionFn, options = {}) {
    const normalizedAddress = address.toLowerCase();
    const {
      highPriority = false,
      maxRetries = 3,
      retryDelay = 3000,
      onProgress,
      onNonceConflict
    } = options;

    // 锁定账户防止并发
    const unlock = await this.lockAccount(normalizedAddress);

    try {
      let lastError;

      for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
          // 获取Nonce
          const nonce = await this.getNextNonce(normalizedAddress, {
            forceRefresh: attempt > 1,
            highPriority
          });

          // 执行交易
          if (onProgress) {
            onProgress({ step: 'executing', attempt, nonce });
          }

          const result = await transactionFn({ nonce });

          return result;

        } catch (error) {
          lastError = error;

          // 检查是否是Nonce冲突
          if (this.isNonceError(error)) {
            this.clearAccountCache(normalizedAddress);

            if (onNonceConflict) {
              onNonceConflict({ attempt, error });
            }
          }

          // 如果不是最后一次尝试，等待后重试
          if (attempt < maxRetries) {
            await new Promise(resolve => setTimeout(resolve, retryDelay));
          }
        }
      }

      // 所有重试都失败了
      throw new Error(`交易失败，已重试 ${maxRetries} 次。最后错误: ${lastError.message}`);

    } finally {
      unlock();
    }
  }

  /**
   * 检查是否是Nonce相关错误
   * @param {Error} error - 错误对象
   * @returns {boolean} 是否是Nonce错误
   */
  isNonceError(error) {
    const message = error.message?.toLowerCase() || '';
    return (
      message.includes('nonce too low') ||
      message.includes('nonce too high') ||
      message.includes('nonce has already been used') ||
      message.includes('replacement transaction underpriced') ||
      message.includes('already known')
    );
  }

  /**
   * 获取高优先级Gas配置
   * @param {Object} baseGasConfig - 基础Gas配置
   * @param {boolean} isFirstTransaction - 是否是第一笔交易
   * @returns {Object} 优化后的Gas配置
   */
  getOptimizedGasConfig(baseGasConfig = {}, isFirstTransaction = false) {
    const walletSpecificMultiplier = this.getWalletGasMultiplier();
    const priorityMultiplier = isFirstTransaction ? 1.5 : 1.2; // 第一笔交易使用更高Gas

    const optimizedConfig = {
      ...baseGasConfig
    };

    // 如果有gasPrice，增加它
    if (baseGasConfig.gasPrice) {
      optimizedConfig.gasPrice = BigInt(
        Math.floor(Number(baseGasConfig.gasPrice) * walletSpecificMultiplier * priorityMultiplier)
      );
    }

    // 如果有EIP-1559参数，增加它们
    if (baseGasConfig.maxFeePerGas) {
      optimizedConfig.maxFeePerGas = BigInt(
        Math.floor(Number(baseGasConfig.maxFeePerGas) * walletSpecificMultiplier * priorityMultiplier)
      );
    }

    if (baseGasConfig.maxPriorityFeePerGas) {
      optimizedConfig.maxPriorityFeePerGas = BigInt(
        Math.floor(Number(baseGasConfig.maxPriorityFeePerGas) * walletSpecificMultiplier * priorityMultiplier)
      );
    }

    // 确保Gas limit足够
    if (baseGasConfig.gas) {
      optimizedConfig.gas = BigInt(
        Math.floor(Number(baseGasConfig.gas) * 1.2) // 增加20%的Gas limit缓冲
      );
    }

    return optimizedConfig;
  }

  /**
   * 获取钱包特定的Gas倍数
   * @returns {number} Gas倍数
   */
  getWalletGasMultiplier() {
    switch (this.walletType) {
      case 'tokenpocket':
        return 1.3; // TP钱包需要更高的Gas
      case 'trust':
        return 1.25;
      case 'imtoken':
        return 1.25;
      case 'mobile_unknown':
        return 1.3; // 未知移动端钱包使用保守策略
      case 'metamask':
        return 1.1;
      default:
        return 1.2; // 默认增加20%
    }
  }
}

// 全局单例实例
export const universalNonceManager = new UniversalNonceManager();

// 默认导出
export default universalNonceManager;
