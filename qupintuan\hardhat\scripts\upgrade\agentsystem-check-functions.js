// qupintuan/hardhat/scripts/upgrade/agentsystem-check-functions.js
// 检查AgentSystem合约的可用函数

const { ethers } = require("hardhat");

async function main() {
  console.log("🔍 检查AgentSystem合约的可用函数...\n");

  const AGENT_SYSTEM_PROXY = "******************************************";

  try {
    console.log("📋 合约信息:");
    console.log(`   代理合约: ${AGENT_SYSTEM_PROXY}`);
    console.log("");

    // 连接合约
    const agentSystem = await ethers.getContractAt("AgentSystem", AGENT_SYSTEM_PROXY);

    // 1. 检查合约接口
    console.log("1️⃣ 检查合约接口...");
    
    const contractInterface = agentSystem.interface;
    const functions = Object.keys(contractInterface.functions);
    
    console.log(`   总函数数: ${functions.length}`);
    console.log("\n   所有函数:");
    
    functions.forEach((func, index) => {
      console.log(`   ${index + 1}. ${func}`);
    });

    // 2. 查找用户等级相关的函数
    console.log("\n2️⃣ 查找用户等级相关的函数...");
    
    const levelFunctions = functions.filter(func => 
      func.toLowerCase().includes('level') || 
      func.toLowerCase().includes('user') ||
      func.toLowerCase().includes('set')
    );
    
    console.log(`   等级相关函数数: ${levelFunctions.length}`);
    levelFunctions.forEach((func, index) => {
      console.log(`   ${index + 1}. ${func}`);
    });

    // 3. 检查是否有setUserLevel函数
    console.log("\n3️⃣ 检查setUserLevel函数...");
    
    const hasSetUserLevel = functions.some(func => func.includes('setUserLevel'));
    console.log(`   有setUserLevel函数: ${hasSetUserLevel ? '✅' : '❌'}`);
    
    if (!hasSetUserLevel) {
      console.log("   🚨 问题: 合约中没有setUserLevel函数！");
      
      // 查找可能的替代函数
      const possibleAlternatives = functions.filter(func => 
        func.toLowerCase().includes('set') && func.toLowerCase().includes('level')
      );
      
      if (possibleAlternatives.length > 0) {
        console.log("   可能的替代函数:");
        possibleAlternatives.forEach((func, index) => {
          console.log(`     ${index + 1}. ${func}`);
        });
      }
    }

    // 4. 检查管理员函数
    console.log("\n4️⃣ 检查管理员函数...");
    
    const adminFunctions = functions.filter(func => 
      func.toLowerCase().includes('admin') || 
      func.toLowerCase().includes('role') ||
      func.toLowerCase().includes('grant') ||
      func.toLowerCase().includes('revoke')
    );
    
    console.log(`   管理员相关函数数: ${adminFunctions.length}`);
    adminFunctions.forEach((func, index) => {
      console.log(`   ${index + 1}. ${func}`);
    });

    // 5. 测试一些基本函数
    console.log("\n5️⃣ 测试基本函数...");
    
    try {
      // 测试getUserInfo
      const testUser = "0xDf98905098CB4e5D261578f600337eeeFd4082b3";
      const userInfo = await agentSystem.getUserInfo(testUser);
      console.log(`   ✅ getUserInfo可用`);
      console.log(`   测试用户当前等级: Level ${userInfo[1]}`);
      
      // 测试validateUserLevel
      const validation = await agentSystem.validateUserLevel(testUser);
      console.log(`   ✅ validateUserLevel可用`);
      console.log(`   用户等级有效: ${validation[0]}`);
      console.log(`   应有等级: Level ${validation[2]}`);
      
    } catch (error) {
      console.log(`   ❌ 基本函数测试失败: ${error.message}`);
    }

    // 6. 检查当前实现合约的源码
    console.log("\n6️⃣ 检查当前实现合约...");
    
    try {
      const provider = ethers.provider;
      const implementationSlot = "0x360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc";
      const implStorage = await provider.send("eth_getStorageAt", [AGENT_SYSTEM_PROXY, implementationSlot, "latest"]);
      const currentImplAddress = "0x" + implStorage.slice(-40);
      
      console.log(`   当前实现地址: ${currentImplAddress}`);
      
      // 检查实现合约的代码
      const implCode = await provider.getCode(currentImplAddress);
      console.log(`   实现合约代码长度: ${implCode.length} 字符`);
      
    } catch (error) {
      console.log(`   ❌ 检查实现合约失败: ${error.message}`);
    }

    // 7. 查看合约源码文件
    console.log("\n7️⃣ 检查合约源码文件...");
    
    try {
      const fs = require('fs');
      const path = require('path');
      
      // 检查AgentSystemMinimal.sol文件
      const contractPath = path.join(process.cwd(), 'contracts', 'AgentSystemMinimal.sol');
      
      if (fs.existsSync(contractPath)) {
        const contractSource = fs.readFileSync(contractPath, 'utf8');
        
        // 查找setUserLevel函数
        const hasSetUserLevelInSource = contractSource.includes('function setUserLevel');
        console.log(`   源码中有setUserLevel函数: ${hasSetUserLevelInSource ? '✅' : '❌'}`);
        
        if (hasSetUserLevelInSource) {
          // 提取函数定义
          const setUserLevelMatch = contractSource.match(/function setUserLevel[^}]+}/);
          if (setUserLevelMatch) {
            console.log("   setUserLevel函数定义:");
            console.log(`   ${setUserLevelMatch[0]}`);
          }
        } else {
          console.log("   🚨 源码中没有setUserLevel函数！");
          
          // 查找其他可能的函数
          const levelFunctionMatches = contractSource.match(/function \w*[Ll]evel\w*\([^)]*\)/g);
          if (levelFunctionMatches) {
            console.log("   源码中的等级相关函数:");
            levelFunctionMatches.forEach((match, index) => {
              console.log(`     ${index + 1}. ${match}`);
            });
          }
        }
        
      } else {
        console.log("   ❌ 找不到合约源码文件");
      }
      
    } catch (error) {
      console.log(`   ❌ 检查源码失败: ${error.message}`);
    }

    // 8. 解决方案建议
    console.log("\n8️⃣ 解决方案建议...");
    console.log("=".repeat(50));
    
    console.log("\n🔧 问题分析:");
    if (!hasSetUserLevel) {
      console.log("1. 当前合约没有setUserLevel函数");
      console.log("2. 可能需要添加这个函数到合约中");
      console.log("3. 或者使用其他方式修复用户等级");
    }
    
    console.log("\n📋 可能的解决方案:");
    console.log("1. 检查合约源码是否包含setUserLevel函数");
    console.log("2. 如果没有，添加setUserLevel函数并重新部署");
    console.log("3. 或者通过其他管理员函数来修复用户");
    console.log("4. 检查是否有其他方式重置用户等级");

  } catch (error) {
    console.error("❌ 检查失败:", error.message);
    console.error("详细错误:", error);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
