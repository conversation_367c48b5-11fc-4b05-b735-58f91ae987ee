// qupintuan/hardhat/scripts/upgrade/agentsystem-submit-upgrade-proposal.js
// 提交AgentSystem升级提案到多签钱包

const { ethers } = require("hardhat");

async function main() {
  console.log("📝 提交AgentSystem升级提案到多签钱包...\n");

  // 升级信息（从第四阶段获取）
  const MULTISIG_ADDRESS = "******************************************";
  const TIMELOCK_ADDRESS = "******************************************";
  const SALT = "0x835bfaef1f47108801e75329e3f4dcefd355c3a2a425b1d271f07d1c393dce62";

  // Schedule 调用数据
  const SCHEDULE_DATA = "0x01d5062a0000000000000000000000009096769b22b53a464d40265420b9ed5342b6acb3000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000c00000000000000000000000000000000000000000000000000000000000000000835bfaef1f47108801e75329e3f4dcefd355c3a2a425b1d271f07d1c393dce62000000000000000000000000000000000000000000000000000000000000025800000000000000000000000000000000000000000000000000000000000000644f1ef2860000000000000000000000006143a4cc8bca739ed4d610f704fcf88974c218830000000000000000000000000000000000000000000000000000000000000040000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000";

  // Execute 调用数据
  const EXECUTE_DATA = "0x134008d30000000000000000000000009096769b22b53a464d40265420b9ed5342b6acb3000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000a00000000000000000000000000000000000000000000000000000000000000000835bfaef1f47108801e75329e3f4dcefd355c3a2a425b1d271f07d1c393dce6200000000000000000000000000000000000000000000000000000000000000644f1ef2860000000000000000000000006143a4cc8bca739ed4d610f704fcf88974c218830000000000000000000000000000000000000000000000000000000000000040000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000";

  try {
    const [deployer] = await ethers.getSigners();
    console.log(`🔑 提交账户: ${deployer.address}\n`);

    // 连接到多签钱包
    const multisig = await ethers.getContractAt("MultiSigWallet", MULTISIG_ADDRESS);

    console.log("📋 升级提案信息:");
    console.log(`   多签钱包: ${MULTISIG_ADDRESS}`);
    console.log(`   Timelock: ${TIMELOCK_ADDRESS}`);
    console.log(`   Salt: ${SALT}`);
    console.log("");

    // 1. 提交Schedule提案
    console.log("1️⃣ 提交Schedule提案...");
    
    try {
      const scheduleTx = await multisig.submitTransaction(
        TIMELOCK_ADDRESS,
        0, // value
        SCHEDULE_DATA
      );
      
      console.log(`   ⏳ Schedule提案交易已提交: ${scheduleTx.hash}`);
      const scheduleReceipt = await scheduleTx.wait();
      console.log(`   ✅ Schedule提案交易确认成功!`);
      
      // 获取交易ID
      const scheduleEvents = scheduleReceipt.logs.filter(log => {
        try {
          const parsed = multisig.interface.parseLog(log);
          return parsed.name === 'SubmitTransaction';
        } catch {
          return false;
        }
      });
      
      if (scheduleEvents.length > 0) {
        const parsed = multisig.interface.parseLog(scheduleEvents[0]);
        const scheduleTransactionId = parsed.args.txIndex;
        console.log(`   📝 Schedule提案ID: ${scheduleTransactionId}`);
        
        // 保存Schedule提案ID
        global.scheduleTransactionId = scheduleTransactionId;
      }
      
    } catch (error) {
      console.log(`   ❌ Schedule提案提交失败: ${error.message}`);
      throw error;
    }

    // 2. 提交Execute提案
    console.log("\n2️⃣ 提交Execute提案...");
    
    try {
      const executeTx = await multisig.submitTransaction(
        TIMELOCK_ADDRESS,
        0, // value
        EXECUTE_DATA
      );
      
      console.log(`   ⏳ Execute提案交易已提交: ${executeTx.hash}`);
      const executeReceipt = await executeTx.wait();
      console.log(`   ✅ Execute提案交易确认成功!`);
      
      // 获取交易ID
      const executeEvents = executeReceipt.logs.filter(log => {
        try {
          const parsed = multisig.interface.parseLog(log);
          return parsed.name === 'SubmitTransaction';
        } catch {
          return false;
        }
      });
      
      if (executeEvents.length > 0) {
        const parsed = multisig.interface.parseLog(executeEvents[0]);
        const executeTransactionId = parsed.args.txIndex;
        console.log(`   📝 Execute提案ID: ${executeTransactionId}`);
        
        // 保存Execute提案ID
        global.executeTransactionId = executeTransactionId;
      }
      
    } catch (error) {
      console.log(`   ❌ Execute提案提交失败: ${error.message}`);
      throw error;
    }

    // 3. 检查提案状态
    console.log("\n3️⃣ 检查提案状态...");
    
    if (global.scheduleTransactionId !== undefined) {
      try {
        const scheduleTxInfo = await multisig.getTransaction(global.scheduleTransactionId);
        console.log(`   Schedule提案 ${global.scheduleTransactionId}:`);
        console.log(`     目标: ${scheduleTxInfo.to}`);
        console.log(`     值: ${scheduleTxInfo.value}`);
        console.log(`     已执行: ${scheduleTxInfo.executed}`);
        console.log(`     确认数: ${scheduleTxInfo.numConfirmations}`);
      } catch (error) {
        console.log(`   ❌ 获取Schedule提案状态失败: ${error.message}`);
      }
    }
    
    if (global.executeTransactionId !== undefined) {
      try {
        const executeTxInfo = await multisig.getTransaction(global.executeTransactionId);
        console.log(`   Execute提案 ${global.executeTransactionId}:`);
        console.log(`     目标: ${executeTxInfo.to}`);
        console.log(`     值: ${executeTxInfo.value}`);
        console.log(`     已执行: ${executeTxInfo.executed}`);
        console.log(`     确认数: ${executeTxInfo.numConfirmations}`);
      } catch (error) {
        console.log(`   ❌ 获取Execute提案状态失败: ${error.message}`);
      }
    }

    // 4. 保存提案信息
    console.log("\n4️⃣ 保存提案信息...");
    
    const proposalInfo = {
      timestamp: new Date().toISOString(),
      multisigAddress: MULTISIG_ADDRESS,
      timelockAddress: TIMELOCK_ADDRESS,
      salt: SALT,
      scheduleTransactionId: global.scheduleTransactionId,
      executeTransactionId: global.executeTransactionId,
      scheduleData: SCHEDULE_DATA,
      executeData: EXECUTE_DATA,
      submitter: deployer.address
    };
    
    const fs = require('fs');
    const filename = `agentsystem-upgrade-proposals-${Date.now()}.json`;
    fs.writeFileSync(filename, JSON.stringify(proposalInfo, null, 2));
    console.log(`   ✅ 提案信息已保存到: ${filename}`);

    // 5. 下一步指南
    console.log("\n5️⃣ 下一步操作指南...");
    console.log("=".repeat(50));
    
    console.log("\n🔐 多签钱包操作步骤:");
    
    if (global.scheduleTransactionId !== undefined) {
      console.log(`\n1. 确认并执行Schedule提案 (ID: ${global.scheduleTransactionId}):`);
      console.log(`   - 登录多签钱包界面`);
      console.log(`   - 找到提案ID ${global.scheduleTransactionId}`);
      console.log(`   - 确认并执行该提案`);
      console.log(`   - 等待交易确认`);
    }
    
    console.log(`\n2. 等待Timelock延迟期:`);
    console.log(`   - 延迟时间: 10分钟 (600秒)`);
    console.log(`   - 在Schedule执行后开始计时`);
    console.log(`   - 可以使用监控脚本检查状态`);
    
    if (global.executeTransactionId !== undefined) {
      console.log(`\n3. 确认并执行Execute提案 (ID: ${global.executeTransactionId}):`);
      console.log(`   - 等待延迟期结束`);
      console.log(`   - 登录多签钱包界面`);
      console.log(`   - 找到提案ID ${global.executeTransactionId}`);
      console.log(`   - 确认并执行该提案`);
      console.log(`   - 等待升级完成`);
    }
    
    console.log(`\n4. 验证升级结果:`);
    console.log(`   npx hardhat run scripts/upgrade/agentsystem-verify-upgrade.js --network bscTestnet`);

    console.log("\n🎉 升级提案提交完成!");
    console.log("📋 提案总结:");
    if (global.scheduleTransactionId !== undefined) {
      console.log(`   ✅ Schedule提案ID: ${global.scheduleTransactionId}`);
    }
    if (global.executeTransactionId !== undefined) {
      console.log(`   ✅ Execute提案ID: ${global.executeTransactionId}`);
    }
    console.log(`   ✅ Salt值: ${SALT}`);
    console.log(`   ✅ 提案信息已保存`);

  } catch (error) {
    console.error("❌ 提案提交失败:", error.message);
    console.error("详细错误:", error);
    process.exit(1);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
