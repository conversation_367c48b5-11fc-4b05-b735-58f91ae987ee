const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index-DmsQauDE-1754738192351-rjh9jemju.js","assets/vendor-Caz4khA--1754738192351-mmao4qk4a.js","assets/web3-DJUNf-KT-1754738192351-mmao4qk4a.js","assets/index-Dcug_PHz-1754738192351-gaytee33w.css"])))=>i.map(i=>d[i]);
import{_ as y}from"./vendor-Caz4khA--1754738192351-mmao4qk4a.js";async function B(){try{const n=`groupBuyPoints_${new Date().toDateString()}`,i=localStorage.getItem(n);if(i){const s=JSON.parse(i);return{dailyGroupBuyPointsIssued:s.dailyGroupBuyPointsIssued||"0",source:"本地缓存",timestamp:s.timestamp,cacheKey:n}}const u=await A(),d={dailyGroupBuyPointsIssued:u.dailyGroupBuyPointsIssued,timestamp:Date.now(),calculatedAt:new Date().toISOString()};return localStorage.setItem(n,JSON.stringify(d)),{dailyGroupBuyPointsIssued:u.dailyGroupBuyPointsIssued,source:"实时计算",timestamp:d.timestamp,cacheKey:n}}catch(a){return console.error("❌ [AdminStatsSync] 获取今日拼团积分失败:",a),{dailyGroupBuyPointsIssued:"0",source:"错误回退",error:a.message,timestamp:Date.now()}}}async function A(){try{const{CONTRACT_ADDRESSES:a,ABIS:n}=await y(async()=>{const{CONTRACT_ADDRESSES:t,ABIS:o}=await import("./index-DmsQauDE-1754738192351-rjh9jemju.js").then(e=>e.k);return{CONTRACT_ADDRESSES:t,ABIS:o}},__vite__mapDeps([0,1,2,3])),{createPublicClient:i,http:u}=await y(async()=>{const{createPublicClient:t,http:o}=await import("./web3-DJUNf-KT-1754738192351-mmao4qk4a.js").then(e=>e.x);return{createPublicClient:t,http:o}},__vite__mapDeps([2,1])),{bscTestnet:d}=await y(async()=>{const{bscTestnet:t}=await import("./web3-DJUNf-KT-1754738192351-mmao4qk4a.js").then(o=>o.A);return{bscTestnet:t}},__vite__mapDeps([2,1])),s=i({chain:d,transport:u()}),l=a[97].PointsManagement,c=new Date,S=new Date(c.getFullYear(),c.getMonth(),c.getDate()),_=new Date(S.getTime()-1440*60*1e3);try{const t=await s.readContract({address:l,abi:n.PointsManagement,functionName:"pointsStats"}),o=`pointsStats_${_.toDateString()}`;let e=0;try{const m=localStorage.getItem(o);if(m){const g=JSON.parse(m);e=Number(g.totalGroupBuyPoints||0)}}catch(m){console.warn("无法读取昨日缓存:",m)}const r=Number(t.totalGroupBuyPoints);return{dailyGroupBuyPointsIssued:Math.max(0,r-e).toString(),currentTotal:r.toString(),yesterdayTotal:e.toString(),calculationMethod:"合约统计数据差值计算"}}catch(t){console.error("❌ [AdminStatsSync] 查询合约失败:",t);const o=await D();let e=0;for(const r of o)e+=r.tier||30;return{dailyGroupBuyPointsIssued:e.toString(),completedRooms:o.length,calculationMethod:"完成房间档位积分累计"}}}catch(a){return console.error("❌ [AdminStatsSync] 计算今日积分失败:",a),{dailyGroupBuyPointsIssued:"0",error:a.message}}}async function D(){try{const{CONTRACT_ADDRESSES:a,ABIS:n}=await y(async()=>{const{CONTRACT_ADDRESSES:t,ABIS:o}=await import("./index-DmsQauDE-1754738192351-rjh9jemju.js").then(e=>e.k);return{CONTRACT_ADDRESSES:t,ABIS:o}},__vite__mapDeps([0,1,2,3])),{createPublicClient:i,http:u}=await y(async()=>{const{createPublicClient:t,http:o}=await import("./web3-DJUNf-KT-1754738192351-mmao4qk4a.js").then(e=>e.x);return{createPublicClient:t,http:o}},__vite__mapDeps([2,1])),{bscTestnet:d}=await y(async()=>{const{bscTestnet:t}=await import("./web3-DJUNf-KT-1754738192351-mmao4qk4a.js").then(o=>o.A);return{bscTestnet:t}},__vite__mapDeps([2,1])),s=i({chain:d,transport:u()}),l=a[97].GroupBuyRoom,c=new Date,S=new Date(c.getFullYear(),c.getMonth(),c.getDate()),_=Math.floor(S.getTime()/1e3);try{const t=await s.readContract({address:l,abi:n.GroupBuyRoom,functionName:"totalRooms"}),o=[],e=Math.min(Number(t),100);for(let r=Math.max(0,Number(t)-e);r<Number(t);r++)try{const p=await s.readContract({address:l,abi:n.GroupBuyRoom,functionName:"getRoom",args:[BigInt(r)]}),[m,g,P,I,w,h]=p;w&&Number(h)>=_&&o.push({id:r,tier:Number(g)/1e6,completedAt:Number(h)*1e3,winner:I,participantCount:P.length})}catch(p){console.warn(`查询房间 ${r} 失败:`,p.message)}return o}catch(t){return console.error("❌ [AdminStatsSync] 查询合约房间失败:",t),[]}}catch(a){return console.error("❌ [AdminStatsSync] 获取今日完成房间失败:",a),[]}}export{B as getTodayGroupBuyPointsIssued};
