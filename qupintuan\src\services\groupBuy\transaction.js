// src/services/groupBuy/transaction.js
// 拼团交易处理函数

import { logDebug, logError } from '@/services/logger';
import { sendTx, createOptimizedSendTx } from '@/utils/transactionHandler';
import { throwError } from './validation';
import { ERROR_CODES } from './constants';

// 检测是否为移动端环境
function isMobileEnvironment() {
  if (typeof window === 'undefined') return false;

  const userAgent = navigator.userAgent.toLowerCase();
  const ethereum = window.ethereum;

  return (
    /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent) ||
    ethereum?.isTokenPocket ||
    ethereum?.isTrust ||
    ethereum?.isImToken ||
    ethereum?.isMathWallet
  );
}

// 获取用户地址
function getUserAddress() {
  try {
    // 尝试从多个可能的位置获取用户地址
    if (window.ethereum?.selectedAddress) {
      return window.ethereum.selectedAddress;
    }

    // 从全局状态获取（如果有的话）
    if (window.__USER_ADDRESS__) {
      return window.__USER_ADDRESS__;
    }

    return null;
  } catch (error) {
    console.warn('无法获取用户地址:', error);
    return null;
  }
}

// 统一交易执行函数（优化版本）
export function executeTransaction(apiCall, txName, options = {}) {
  const { isFirstTransaction = false } = options;

  // 检测移动端环境
  const isMobile = isMobileEnvironment();
  const userAddress = getUserAddress();



  // 如果是移动端且能获取到用户地址，使用优化的交易处理
  if (isMobile && userAddress) {

    const optimizedSendTx = createOptimizedSendTx(userAddress, isFirstTransaction, txName);

    return optimizedSendTx(
      apiCall,
      {
        onTxHash: hash => {
          logDebug(`${txName} tx hash`, hash);
        },
        onReceipt: receipt => {
          logDebug(`${txName} receipt`, receipt);
        },
        onError: error => {
          logError(`${txName} error`, error);

          // 移动端特殊错误处理
          if (error.message?.includes('nonce too low')) {
            throw new Error('交易顺序冲突，建议重启TP钱包应用后重试');
          } else if (error.message?.includes('timeout')) {
            throw new Error('交易确认超时，请检查网络连接或稍后重试');
          } else if (error.message?.includes('Cannot read properties of undefined')) {
            throw new Error('钱包连接异常，请重新连接钱包后重试');
          }
        }
      }
    );
  } else {
    // 桌面端或无法获取用户地址时，使用原来的处理方式

    return sendTx(
      apiCall,
      {
        onTxHash: hash => logDebug(`${txName} tx hash`, hash),
        onReceipt: receipt => logDebug(`${txName} receipt`, receipt),
        onError: error => {
          logError(`${txName} error`, error);
          // 通用错误处理
          if (error.message?.includes('nonce too low')) {
            throw new Error('Nonce 值过低，请稍后重试或重启钱包应用');
          } else if (error.message?.includes('Cannot read properties of undefined')) {
            throw new Error('参数错误，请重新连接钱包后重试');
          }
        }
      }
    );
  }
}

// 交易重试机制
export async function retryTransaction(transactionFn, maxRetries = 3, delay = 3000) {
  let lastError;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      logDebug(`交易尝试 ${attempt}/${maxRetries}`);
      const result = await transactionFn();
      logDebug(`交易成功，尝试次数: ${attempt}`);
      return result;
    } catch (error) {
      lastError = error;
      logError(`交易失败，尝试 ${attempt}/${maxRetries}:`, error);
      
      if (attempt < maxRetries) {
        logDebug(`等待 ${delay}ms 后重试...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }
  
  throwError(ERROR_CODES.NETWORK_ERROR, `交易失败，已重试 ${maxRetries} 次`, lastError);
}

// 交易状态检查
export async function checkTransactionStatus(txHash, publicClient, timeout = 60000) {
  try {
    const receipt = await publicClient.waitForTransactionReceipt({
      hash: txHash,
      timeout
    });
    
    if (receipt.status === 'success') {
      logDebug('交易成功确认:', receipt);
      return { success: true, receipt };
    } else {
      logError('交易失败:', receipt);
      return { success: false, receipt };
    }
  } catch (error) {
    logError('检查交易状态失败:', error);
    return { success: false, error };
  }
}

// 估算 Gas 费用
export async function estimateGas(contract, functionName, args, value = 0n) {
  try {
    const gasEstimate = await contract.estimateGas[functionName](...args, {
      value
    });
    
    // 增加 20% 的 Gas 缓冲
    const gasLimit = gasEstimate * 120n / 100n;
    
    logDebug(`Gas 估算 - ${functionName}:`, {
      estimated: gasEstimate.toString(),
      withBuffer: gasLimit.toString()
    });
    
    return gasLimit;
  } catch (error) {
    logError(`Gas 估算失败 - ${functionName}:`, error);
    throw error;
  }
}

// 批量交易处理
export async function batchTransactions(transactions, options = {}) {
  const { 
    maxConcurrent = 3, 
    delayBetweenBatches = 1000,
    onProgress 
  } = options;
  
  const results = [];
  const errors = [];
  
  for (let i = 0; i < transactions.length; i += maxConcurrent) {
    const batch = transactions.slice(i, i + maxConcurrent);
    
    try {
      const batchResults = await Promise.allSettled(
        batch.map(async (tx, index) => {
          try {
            const result = await tx();
            return { index: i + index, result };
          } catch (error) {
            return { index: i + index, error };
          }
        })
      );
      
      batchResults.forEach(({ value }) => {
        if (value.error) {
          errors.push(value);
        } else {
          results.push(value);
        }
      });
      
      if (onProgress) {
        onProgress({
          completed: Math.min(i + maxConcurrent, transactions.length),
          total: transactions.length,
          results: results.length,
          errors: errors.length
        });
      }
      
      // 批次间延迟
      if (i + maxConcurrent < transactions.length && delayBetweenBatches > 0) {
        await new Promise(resolve => setTimeout(resolve, delayBetweenBatches));
      }
      
    } catch (error) {
      logError(`批量交易处理失败 - 批次 ${Math.floor(i / maxConcurrent)}:`, error);
    }
  }
  
  return { results, errors };
}

// 交易确认等待
export async function waitForConfirmations(txHash, publicClient, confirmations = 1) {
  try {
    let currentBlock = await publicClient.getBlockNumber();
    const receipt = await publicClient.getTransactionReceipt({ hash: txHash });
    
    if (!receipt) {
      throw new Error('交易收据未找到');
    }
    
    const txBlock = receipt.blockNumber;
    const confirmedBlocks = currentBlock - txBlock;
    
    if (confirmedBlocks >= confirmations) {
      return receipt;
    }
    
    // 等待更多确认
    while (confirmedBlocks < confirmations) {
      await new Promise(resolve => setTimeout(resolve, 3000)); // 等待3秒
      currentBlock = await publicClient.getBlockNumber();
      
      if (currentBlock - txBlock >= confirmations) {
        break;
      }
    }
    
    return receipt;
  } catch (error) {
    logError('等待交易确认失败:', error);
    throw error;
  }
}
