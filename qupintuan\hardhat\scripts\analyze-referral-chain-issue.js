// qupintuan/hardhat/scripts/analyze-referral-chain-issue.js
// 分析推荐链条中的等级异常问题

const { ethers } = require("hardhat");

async function main() {
  console.log("🔍 分析推荐链条中的等级异常问题...\n");

  // 合约地址（BSC测试网）
  const AGENT_SYSTEM_PROXY = "******************************************";

  // 目标用户和其直推用户
  const targetUser = "******************************************";
  const referralUsers = [
    "******************************************", // 直推用户1
    "******************************************", // 直推用户2  
    "******************************************"  // 直推用户3 (也是Level 1!)
  ];

  try {
    // 连接到合约
    const agentSystem = await ethers.getContractAt("AgentSystem", AGENT_SYSTEM_PROXY);
    
    console.log(`📋 合约地址: ${AGENT_SYSTEM_PROXY}\n`);

    // 1. 分析目标用户
    console.log("1️⃣ 分析目标用户...");
    await analyzeUser(agentSystem, targetUser, "目标用户");

    // 2. 分析所有直推用户
    console.log("\n2️⃣ 分析直推用户...");
    for (let i = 0; i < referralUsers.length; i++) {
      await analyzeUser(agentSystem, referralUsers[i], `直推用户${i + 1}`);
    }

    // 3. 检查推荐关系
    console.log("\n3️⃣ 验证推荐关系...");
    for (let i = 0; i < referralUsers.length; i++) {
      const referralAddress = referralUsers[i];
      const userInfo = await agentSystem.getUserInfo(referralAddress);
      const inviter = userInfo[0];
      
      console.log(`   直推用户${i + 1}: ${referralAddress}`);
      console.log(`     推荐人: ${inviter}`);
      console.log(`     关系正确: ${inviter.toLowerCase() === targetUser.toLowerCase() ? '✅' : '❌'}`);
    }

    // 4. 计算业绩分布
    console.log("\n4️⃣ 计算业绩分布...");
    
    const performances = [];
    for (let i = 0; i < referralUsers.length; i++) {
      const referralAddress = referralUsers[i];
      const userInfo = await agentSystem.getUserInfo(referralAddress);
      const personalPerf = Number(userInfo[5]) / 1000000;
      const totalPerf = Number(userInfo[2]) / 1000000;
      const realTotalPerf = personalPerf + totalPerf;
      
      performances.push({
        address: referralAddress,
        index: i,
        personalPerformance: personalPerf,
        totalPerformance: totalPerf,
        realTotalPerformance: realTotalPerf
      });
    }
    
    // 按业绩排序
    performances.sort((a, b) => b.realTotalPerformance - a.realTotalPerformance);
    
    console.log("   业绩排序（从高到低）:");
    performances.forEach((perf, index) => {
      console.log(`     ${index + 1}. 直推用户${perf.index + 1}: ${perf.realTotalPerformance.toFixed(2)} USDT ${index === 0 ? '(最大团队，排除)' : '(计入小团队)'}`);
    });
    
    const maxTeamPerformance = performances[0].realTotalPerformance;
    const smallTeamsPerformance = performances.slice(1).reduce((sum, p) => sum + p.realTotalPerformance, 0);
    const validTeamsCount = performances.filter(p => p.realTotalPerformance > 0).length;
    
    console.log(`\n   计算结果:`);
    console.log(`     最大团队业绩: ${maxTeamPerformance.toFixed(2)} USDT`);
    console.log(`     小团队业绩总和: ${smallTeamsPerformance.toFixed(2)} USDT`);
    console.log(`     有效团队数: ${validTeamsCount}`);
    console.log(`     升级要求: 5000 USDT`);
    console.log(`     是否满足升级条件: ${validTeamsCount >= 1 && smallTeamsPerformance >= 5000 ? '✅' : '❌'}`);

    // 5. 检查异常用户的升级历史
    console.log("\n5️⃣ 检查异常用户的升级历史...");
    
    const problemUsers = [targetUser];
    
    // 找出等级异常的直推用户
    for (let i = 0; i < referralUsers.length; i++) {
      const referralAddress = referralUsers[i];
      const userInfo = await agentSystem.getUserInfo(referralAddress);
      const level = Number(userInfo[1]);
      
      if (level > 0) {
        console.log(`   发现异常直推用户: ${referralAddress} (Level ${level})`);
        problemUsers.push(referralAddress);
      }
    }
    
    console.log(`\n   总共发现 ${problemUsers.length} 个等级异常用户`);

    // 6. 分析可能的升级触发原因
    console.log("\n6️⃣ 分析可能的升级触发原因...");
    
    console.log("   可能的原因分析:");
    console.log("   1. 合约升级前的历史数据问题");
    console.log("   2. 修复后又有新的业绩添加触发了错误升级");
    console.log("   3. 合约中仍存在其他升级触发点");
    console.log("   4. 有人手动调用了tryUpgrade函数");
    console.log("   5. 管理员误操作");

    // 7. 检查合约中所有可能触发升级的函数
    console.log("\n7️⃣ 检查合约升级触发点...");
    
    console.log("   合约中可能触发_upgradeIfEligible的函数:");
    console.log("   1. addPerformance() - 添加业绩时");
    console.log("   2. tryUpgrade() - 手动触发升级");
    console.log("   3. setUserLevel() - 管理员设置等级（不调用_upgradeIfEligible）");
    
    // 8. 检查最近是否有业绩添加
    console.log("\n8️⃣ 检查最近的业绩变化...");
    
    for (const userAddress of problemUsers) {
      console.log(`\n   检查用户: ${userAddress}`);
      
      // 检查用户的升级信息
      try {
        const upgradeInfo = await agentSystem.getUserUpgradeInfo(userAddress);
        const level = Number(upgradeInfo[0]);
        const upgradeType = Number(upgradeInfo[1]);
        const upgradeTimestamp = Number(upgradeInfo[2]);
        
        console.log(`     当前等级: Level ${level}`);
        console.log(`     升级类型: ${['未升级', '自动升级', '管理员设置'][upgradeType]}`);
        console.log(`     升级时间: ${upgradeTimestamp > 0 ? new Date(upgradeTimestamp * 1000).toLocaleString() : '未升级'}`);
        
        if (upgradeType === 1) { // 自动升级
          console.log(`     🚨 这是自动升级，需要检查是什么触发的`);
        } else if (upgradeType === 2) { // 管理员设置
          console.log(`     📝 这是管理员设置的等级`);
        }
        
      } catch (error) {
        console.log(`     ❌ 获取升级信息失败: ${error.message}`);
      }
    }

    // 9. 关键发现总结
    console.log("\n9️⃣ 关键发现总结...");
    console.log("=".repeat(60));
    
    console.log(`\n🔍 重要发现:`);
    console.log(`1. 目标用户 ${targetUser} 等级异常 (Level 1)`);
    console.log(`2. 直推用户3 ${referralUsers[2]} 也等级异常 (Level 1)`);
    console.log(`3. 两个用户的小团队业绩都不足5000 USDT`);
    console.log(`4. 两个用户的升级类型都是"自动升级"`);
    
    console.log(`\n🚨 问题分析:`);
    console.log(`这表明问题不是单一用户的问题，而是系统性问题！`);
    console.log(`可能的原因:`);
    console.log(`1. 合约修复不彻底，仍有bug`);
    console.log(`2. 有其他代码路径在调用升级逻辑`);
    console.log(`3. 业绩计算逻辑有问题`);
    console.log(`4. 推荐关系数据有异常`);
    
    console.log(`\n📋 建议下一步:`);
    console.log(`1. 检查合约代码中所有调用_upgradeIfEligible的地方`);
    console.log(`2. 验证业绩计算逻辑是否正确`);
    console.log(`3. 检查是否有外部脚本在调用升级函数`);
    console.log(`4. 分析这两个用户的升级时间是否相同`);
    console.log(`5. 检查推荐关系数据的完整性`);

  } catch (error) {
    console.error("❌ 分析失败:", error.message);
    console.error("详细错误:", error);
  }
}

async function analyzeUser(agentSystem, userAddress, userType) {
  console.log(`\n   ${userType}: ${userAddress}`);
  
  try {
    // 获取用户信息
    const userInfo = await agentSystem.getUserInfo(userAddress);
    const level = Number(userInfo[1]);
    const totalPerformance = Number(userInfo[2]) / 1000000;
    const referralsCount = Number(userInfo[3]);
    const personalPerformance = Number(userInfo[5]) / 1000000;
    const upgradeType = Number(userInfo[6]);
    const upgradeTimestamp = Number(userInfo[7]);
    
    console.log(`     等级: Level ${level}`);
    console.log(`     个人业绩: ${personalPerformance.toFixed(2)} USDT`);
    console.log(`     团队业绩: ${totalPerformance.toFixed(2)} USDT`);
    console.log(`     直推人数: ${referralsCount}`);
    console.log(`     升级类型: ${['未升级', '自动升级', '管理员设置'][upgradeType]}`);
    console.log(`     升级时间: ${upgradeTimestamp > 0 ? new Date(upgradeTimestamp * 1000).toLocaleString() : '未升级'}`);
    
    // 验证等级
    try {
      const validation = await agentSystem.validateUserLevel(userAddress);
      const isValid = validation[0];
      const expectedLevel = Number(validation[2]);
      const smallTeamsPerformance = Number(validation[3]) / 1000000;
      
      console.log(`     等级有效: ${isValid ? '✅' : '❌'}`);
      console.log(`     应有等级: Level ${expectedLevel}`);
      console.log(`     小团队业绩: ${smallTeamsPerformance.toFixed(2)} USDT`);
      
      if (!isValid) {
        console.log(`     🚨 等级异常！当前Level ${level}，应为Level ${expectedLevel}`);
      }
      
    } catch (error) {
      console.log(`     ❌ 验证等级失败: ${error.message}`);
    }
    
  } catch (error) {
    console.log(`     ❌ 获取用户信息失败: ${error.message}`);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
