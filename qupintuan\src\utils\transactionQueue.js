// src/utils/transactionQueue.js
/**
 * 交易队列管理器 - 处理业务流程中的多步交易
 * 确保交易按正确顺序执行，前一笔交易确认后再执行下一笔
 */

import { universalNonceManager } from './universalNonceManager.js';

/**
 * 交易步骤状态
 */
export const STEP_STATUS = {
  PENDING: 'pending',
  EXECUTING: 'executing', 
  COMPLETED: 'completed',
  FAILED: 'failed',
  SKIPPED: 'skipped'
};

/**
 * 业务流程定义
 */
export const BUSINESS_FLOWS = {
  CREATE_GROUP_BUY: {
    name: '创建拼团房间',
    steps: [
      { id: 'create_room', name: '创建拼团房间', required: true },
      { id: 'approve_qpt', name: '授权QPT代币', required: true },
      { id: 'lock_qpt', name: '锁定QPT代币', required: true }
    ]
  },
  JOIN_GROUP_BUY: {
    name: '参与拼团房间',
    steps: [
      { id: 'approve_usdt', name: '授权USDT代币', required: true },
      { id: 'join_room', name: '加入拼团房间', required: true }
    ]
  },
  STAKE_NODE: {
    name: '质押激活节点',
    steps: [
      { id: 'approve_qpt', name: '授权QPT代币', required: true },
      { id: 'stake_node', name: '执行质押操作', required: true }
    ]
  },
  QPT_BUYBACK: {
    name: 'QPT回购房间',
    steps: [
      { id: 'approve_qpt', name: '授权QPT代币', required: true },
      { id: 'join_buyback', name: '参与回购房间', required: true }
    ]
  },
  UNSTAKE_NODE: {
    name: '取消质押节点',
    steps: [
      { id: 'unstake_node', name: '取消质押', required: true },
      { id: 'withdraw_qpt', name: '提取QPT', required: true }
    ]
  }
};

/**
 * 交易队列管理器类
 */
class TransactionQueue {
  constructor() {
    this.activeQueues = new Map(); // 活跃的交易队列
    this.queueHistory = new Map(); // 队列历史记录
  }

  /**
   * 执行业务流程
   * @param {string} flowType - 业务流程类型
   * @param {string} userAddress - 用户地址
   * @param {Array} stepFunctions - 步骤执行函数数组
   * @param {Object} options - 选项
   * @returns {Promise<Object>} 执行结果
   */
  async executeBusinessFlow(flowType, userAddress, stepFunctions, options = {}) {
    const {
      onStepStart,
      onStepComplete,
      onStepError,
      onFlowComplete,
      onFlowError,
      skipSteps = [],
      retryFailedSteps = true
    } = options;

    const flow = BUSINESS_FLOWS[flowType];
    if (!flow) {
      throw new Error(`未知的业务流程类型: ${flowType}`);
    }

    const queueId = `${flowType}_${userAddress}_${Date.now()}`;
    const normalizedAddress = userAddress.toLowerCase();

    // 创建队列状态
    const queueState = {
      id: queueId,
      flowType,
      userAddress: normalizedAddress,
      steps: flow.steps.map((step, index) => ({
        ...step,
        index,
        status: skipSteps.includes(step.id) ? STEP_STATUS.SKIPPED : STEP_STATUS.PENDING,
        result: null,
        error: null,
        startTime: null,
        endTime: null,
        txHash: null
      })),
      startTime: new Date(),
      endTime: null,
      status: 'running'
    };

    this.activeQueues.set(queueId, queueState);

    try {
      const results = [];

      // 按顺序执行每个步骤
      for (let i = 0; i < flow.steps.length; i++) {
        const step = queueState.steps[i];
        const stepFunction = stepFunctions[i];

        // 跳过不需要执行的步骤
        if (step.status === STEP_STATUS.SKIPPED) {
          continue;
        }

        // 检查是否有对应的执行函数
        if (!stepFunction || typeof stepFunction !== 'function') {
          throw new Error(`步骤 ${step.name} 缺少执行函数`);
        }

        let stepResult = null;
        let retryCount = 0;
        const maxRetries = retryFailedSteps ? 3 : 1;

        while (retryCount < maxRetries) {
          try {
            // 更新步骤状态
            step.status = STEP_STATUS.EXECUTING;
            step.startTime = new Date();

            // 调用步骤开始回调
            if (onStepStart) {
              onStepStart(step, i + 1, flow.steps.length);
            }

            // 使用Nonce管理器执行交易
            stepResult = await universalNonceManager.executeTransaction(
              normalizedAddress,
              stepFunction,
              {
                highPriority: i === 0 // 第一步使用高优先级
              }
            );

            // 步骤成功
            step.status = STEP_STATUS.COMPLETED;
            step.endTime = new Date();
            step.result = stepResult;
            step.txHash = stepResult.hash || stepResult.txHash;

            // 调用步骤完成回调
            if (onStepComplete) {
              onStepComplete(step, stepResult, i + 1, flow.steps.length);
            }

            results.push(stepResult);
            break; // 成功，跳出重试循环

          } catch (error) {
            retryCount++;
            step.error = error;

            // 调用步骤错误回调
            if (onStepError) {
              onStepError(step, error, retryCount, maxRetries);
            }

            // 如果还有重试机会
            if (retryCount < maxRetries) {
              await new Promise(resolve => setTimeout(resolve, 3000));
            } else {
              // 重试次数用完，步骤失败
              step.status = STEP_STATUS.FAILED;
              step.endTime = new Date();
              throw error;
            }
          }
        }
      }

      // 所有步骤完成
      queueState.status = 'completed';
      queueState.endTime = new Date();

      // 调用流程完成回调
      if (onFlowComplete) {
        onFlowComplete(queueState, results);
      }

      // 移动到历史记录
      this.queueHistory.set(queueId, queueState);
      this.activeQueues.delete(queueId);

      return {
        success: true,
        queueId,
        results,
        totalSteps: results.length,
        duration: queueState.endTime - queueState.startTime
      };

    } catch (error) {
      // 流程失败
      queueState.status = 'failed';
      queueState.endTime = new Date();
      queueState.error = error;

      // 调用流程错误回调
      if (onFlowError) {
        onFlowError(queueState, error);
      }

      // 移动到历史记录
      this.queueHistory.set(queueId, queueState);
      this.activeQueues.delete(queueId);

      throw error;
    }
  }

  /**
   * 获取队列状态
   * @param {string} queueId - 队列ID
   * @returns {Object|null} 队列状态
   */
  getQueueStatus(queueId) {
    return this.activeQueues.get(queueId) || this.queueHistory.get(queueId) || null;
  }

  /**
   * 获取用户的活跃队列
   * @param {string} userAddress - 用户地址
   * @returns {Array} 活跃队列列表
   */
  getUserActiveQueues(userAddress) {
    const normalizedAddress = userAddress.toLowerCase();
    return Array.from(this.activeQueues.values())
      .filter(queue => queue.userAddress === normalizedAddress);
  }

  /**
   * 取消队列
   * @param {string} queueId - 队列ID
   */
  cancelQueue(queueId) {
    const queue = this.activeQueues.get(queueId);
    if (queue) {
      queue.status = 'cancelled';
      queue.endTime = new Date();
      
      this.queueHistory.set(queueId, queue);
      this.activeQueues.delete(queueId);
    }
  }
}

// 全局单例实例
export const transactionQueue = new TransactionQueue();

// 默认导出
export default transactionQueue;
