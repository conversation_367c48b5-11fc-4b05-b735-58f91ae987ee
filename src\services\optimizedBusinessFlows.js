// src/services/optimizedBusinessFlows.js
/**
 * 优化后的业务流程 - 使用通用Nonce管理和交易队列
 * 解决所有钱包的Nonce冲突问题，确保交易按正确顺序执行
 */

import { transactionQueue, BUSINESS_FLOWS } from '@/utils/transactionQueue.js';
import { universalNonceManager } from '@/utils/universalNonceManager.js';
import { toast } from 'react-hot-toast';

// 导入现有的API函数
import {
  createRoomWithQPTVerification,
  approveQPTForCreate,
  lockQPTForCreate,
  approveUSDTForJoin,
  joinRoomWithPaymentVerification
} from '@/apis/groupBuy/roomManagement.js';

import {
  approveQPT,
  joinRoom as joinQPTBuybackRoom
} from '@/apis/qptBuybackApi.js';

/**
 * 优化后的创建拼团房间流程（3次弹窗）
 * @param {Object} signer - 钱包签名者
 * @param {string} tierAmountStr - 拼团金额字符串
 * @returns {Promise<Object>} 执行结果
 */
export async function optimizedCreateGroupBuy(signer, tierAmountStr) {
  const userAddress = signer.account?.address;
  if (!userAddress) {
    throw new Error('无法获取用户地址');
  }

  console.log('🚀 [OptimizedBusinessFlows] 开始优化的创建拼团流程:', {
    userAddress,
    tierAmountStr,
    walletType: universalNonceManager.walletType
  });

  // 转换金额格式
  const tierNum = Number(tierAmountStr) / 1000000; // USDT 有 6 位小数精度

  // 定义三个步骤的执行函数
  const stepFunctions = [
    // 步骤1：创建拼团房间
    async ({ nonce }) => {
      console.log('📞 [Step1] 创建拼团房间，Nonce:', nonce);
      
      // 获取优化的Gas配置（第一笔交易使用高优先级）
      const gasConfig = universalNonceManager.getOptimizedGasConfig({}, true);
      
      return await createRoomWithQPTVerification({ 
        chainId: 97, 
        tier: tierNum, 
        signer,
        gasConfig,
        nonce
      });
    },
    
    // 步骤2：授权QPT给QPTLocker合约
    async ({ nonce }) => {
      console.log('📞 [Step2] 授权QPT代币，Nonce:', nonce);
      
      const gasConfig = universalNonceManager.getOptimizedGasConfig({}, false);
      
      return await approveQPTForCreate({ 
        chainId: 97, 
        tier: tierNum, 
        signer,
        gasConfig,
        nonce
      });
    },
    
    // 步骤3：锁定QPT
    async ({ nonce }) => {
      console.log('📞 [Step3] 锁定QPT代币，Nonce:', nonce);
      
      const gasConfig = universalNonceManager.getOptimizedGasConfig({}, false);
      
      // 需要从步骤1的结果中获取roomId
      const roomId = stepResults[0]?.roomId;
      if (!roomId) {
        throw new Error('无法获取房间ID，请重试');
      }
      
      return await lockQPTForCreate({ 
        chainId: 97, 
        tier: tierNum, 
        roomId, 
        signer,
        gasConfig,
        nonce
      });
    }
  ];

  let stepResults = [];

  // 执行业务流程
  const result = await transactionQueue.executeBusinessFlow(
    'CREATE_GROUP_BUY',
    userAddress,
    stepFunctions,
    {
      onStepStart: (step, current, total) => {
        console.log(`🔄 [OptimizedBusinessFlows] 开始执行步骤 ${current}/${total}: ${step.name}`);
      },
      
      onStepComplete: (step, stepResult, current, total) => {
        console.log(`✅ [OptimizedBusinessFlows] 步骤完成 ${current}/${total}: ${step.name}`, {
          txHash: stepResult.hash || stepResult.txHash,
          result: stepResult
        });
        
        // 保存步骤结果供后续步骤使用
        stepResults.push(stepResult);
        
        // 标记交易完成
        universalNonceManager.markTransactionComplete(
          userAddress, 
          stepResult.hash || stepResult.txHash
        );
      },
      
      onStepError: (step, error, retryCount, maxRetries) => {
        console.error(`❌ [OptimizedBusinessFlows] 步骤失败: ${step.name}`, {
          error: error.message,
          retryCount,
          maxRetries
        });
      },
      
      onFlowComplete: (queueState, results) => {
        console.log('🎉 [OptimizedBusinessFlows] 创建拼团流程完成:', {
          queueId: queueState.id,
          roomId: results[0]?.roomId,
          totalSteps: results.length
        });
      },
      
      onFlowError: (queueState, error) => {
        console.error('❌ [OptimizedBusinessFlows] 创建拼团流程失败:', {
          queueId: queueState.id,
          error: error.message
        });
      }
    }
  );

  return {
    success: true,
    roomId: stepResults[0]?.roomId,
    receipt: stepResults[0]?.receipt,
    queueId: result.queueId,
    results: stepResults,
    message: '发起人三步操作完成：创建房间 → 授权QPT → 锁仓QPT'
  };
}

/**
 * 优化后的参与拼团房间流程（2次弹窗）
 * @param {Object} signer - 钱包签名者
 * @param {number} roomId - 房间ID
 * @returns {Promise<Object>} 执行结果
 */
export async function optimizedJoinGroupBuy(signer, roomId) {
  const userAddress = signer.account?.address;
  if (!userAddress) {
    throw new Error('无法获取用户地址');
  }

  console.log('🚀 [OptimizedBusinessFlows] 开始优化的参与拼团流程:', {
    userAddress,
    roomId,
    walletType: universalNonceManager.walletType
  });

  // 定义两个步骤的执行函数
  const stepFunctions = [
    // 步骤1：授权USDT
    async ({ nonce }) => {
      console.log('📞 [Step1] 授权USDT代币，Nonce:', nonce);
      
      const gasConfig = universalNonceManager.getOptimizedGasConfig({}, true);
      
      return await approveUSDTForJoin({ 
        chainId: 97, 
        roomId, 
        signer,
        gasConfig,
        nonce
      });
    },
    
    // 步骤2：加入拼团房间
    async ({ nonce }) => {
      console.log('📞 [Step2] 加入拼团房间，Nonce:', nonce);
      
      const gasConfig = universalNonceManager.getOptimizedGasConfig({}, false);
      
      return await joinRoomWithPaymentVerification({ 
        chainId: 97, 
        roomId, 
        signer,
        gasConfig,
        nonce
      });
    }
  ];

  let stepResults = [];

  // 执行业务流程
  const result = await transactionQueue.executeBusinessFlow(
    'JOIN_GROUP_BUY',
    userAddress,
    stepFunctions,
    {
      onStepComplete: (step, stepResult) => {
        stepResults.push(stepResult);
        universalNonceManager.markTransactionComplete(
          userAddress, 
          stepResult.hash || stepResult.txHash
        );
      }
    }
  );

  return {
    success: true,
    roomId,
    receipt: stepResults[1]?.receipt,
    queueId: result.queueId,
    results: stepResults,
    message: '参与者两步操作完成：授权USDT → 加入房间'
  };
}

/**
 * 优化后的质押激活节点流程（2次弹窗）
 * @param {Object} walletClient - 钱包客户端
 * @param {string} account - 用户账户
 * @returns {Promise<Object>} 执行结果
 */
export async function optimizedStakeNode(walletClient, account) {
  console.log('🚀 [OptimizedBusinessFlows] 开始优化的质押激活节点流程:', {
    account,
    walletType: universalNonceManager.walletType
  });

  // 导入必要的合约地址和ABI
  const { CONTRACT_ADDRESSES, ABIS } = await import('@/constants/contracts.js');
  const { createPublicClient, http } = await import('viem');
  const { bscTestnet } = await import('viem/chains');

  const publicClient = createPublicClient({
    chain: bscTestnet,
    transport: http()
  });

  // 获取质押所需的QPT数量
  const stakingAddress = CONTRACT_ADDRESSES[97].NodeStaking;
  const qptTokenAddress = CONTRACT_ADDRESSES[97].QPT;
  const qptLockerAddress = CONTRACT_ADDRESSES[97].QPTLocker;

  const requiredStake = await publicClient.readContract({
    address: stakingAddress,
    abi: ABIS.NodeStaking,
    functionName: 'getCurrentRequiredStake'
  });

  // 定义两个步骤的执行函数
  const stepFunctions = [
    // 步骤1：授权QPT
    async ({ nonce }) => {
      console.log('📞 [Step1] 授权QPT代币，Nonce:', nonce);
      
      const gasConfig = universalNonceManager.getOptimizedGasConfig({}, true);
      
      const txHash = await walletClient.writeContract({
        address: qptTokenAddress,
        abi: ABIS.QPTToken,
        functionName: 'approve',
        args: [qptLockerAddress, requiredStake],
        account,
        ...gasConfig,
        nonce
      });

      const receipt = await publicClient.waitForTransactionReceipt({ hash: txHash });
      return { hash: txHash, receipt };
    },
    
    // 步骤2：执行质押
    async ({ nonce }) => {
      console.log('📞 [Step2] 执行质押操作，Nonce:', nonce);
      
      const gasConfig = universalNonceManager.getOptimizedGasConfig({}, false);
      
      const txHash = await walletClient.writeContract({
        address: stakingAddress,
        abi: ABIS.NodeStaking,
        functionName: 'stakeNode',
        account,
        ...gasConfig,
        nonce
      });

      const receipt = await publicClient.waitForTransactionReceipt({ hash: txHash });
      return { hash: txHash, receipt };
    }
  ];

  let stepResults = [];

  // 执行业务流程
  const result = await transactionQueue.executeBusinessFlow(
    'STAKE_NODE',
    account,
    stepFunctions,
    {
      onStepComplete: (step, stepResult) => {
        stepResults.push(stepResult);
        universalNonceManager.markTransactionComplete(account, stepResult.hash);
      }
    }
  );

  return {
    success: true,
    queueId: result.queueId,
    results: stepResults,
    message: '节点质押完成：授权QPT → 执行质押'
  };
}

/**
 * 优化后的QPT回购房间流程（2次弹窗）
 * @param {Object} walletClient - 钱包客户端
 * @param {string} account - 用户账户
 * @param {number} roomId - 房间ID
 * @returns {Promise<Object>} 执行结果
 */
export async function optimizedJoinQPTBuyback(walletClient, account, roomId) {
  console.log('🚀 [OptimizedBusinessFlows] 开始优化的QPT回购房间流程:', {
    account,
    roomId,
    walletType: universalNonceManager.walletType
  });

  // 定义两个步骤的执行函数
  const stepFunctions = [
    // 步骤1：授权QPT
    async ({ nonce }) => {
      console.log('📞 [Step1] 授权QPT代币，Nonce:', nonce);
      
      const gasConfig = universalNonceManager.getOptimizedGasConfig({}, true);
      
      return await approveQPT({
        chainId: 97,
        roomId,
        walletClient,
        account,
        gasConfig,
        nonce
      });
    },
    
    // 步骤2：参与回购房间
    async ({ nonce }) => {
      console.log('📞 [Step2] 参与回购房间，Nonce:', nonce);
      
      const gasConfig = universalNonceManager.getOptimizedGasConfig({}, false);
      
      return await joinQPTBuybackRoom({
        chainId: 97,
        roomId,
        walletClient,
        account,
        gasConfig,
        nonce
      });
    }
  ];

  let stepResults = [];

  // 执行业务流程
  const result = await transactionQueue.executeBusinessFlow(
    'QPT_BUYBACK',
    account,
    stepFunctions,
    {
      onStepComplete: (step, stepResult) => {
        stepResults.push(stepResult);
        universalNonceManager.markTransactionComplete(account, stepResult.hash || stepResult.txHash);
      }
    }
  );

  return {
    success: true,
    roomId,
    queueId: result.queueId,
    results: stepResults,
    message: 'QPT回购参与完成：授权QPT → 参与回购'
  };
}

/**
 * 优化后的多签钱包确认流程
 * @param {Object} walletClient - 钱包客户端
 * @param {string} account - 用户账户
 * @param {number} txId - 交易ID
 * @returns {Promise<Object>} 执行结果
 */
export async function optimizedMultiSigConfirm(walletClient, account, txId) {
  console.log('🚀 [OptimizedBusinessFlows] 开始优化的多签确认流程:', {
    account,
    txId,
    walletType: universalNonceManager.walletType
  });

  // 导入多签服务
  const { multiSigService } = await import('@/services/multiSigService.js');

  // 定义执行函数
  const stepFunctions = [
    async ({ nonce }) => {
      console.log('📞 [Step1] 确认多签交易，Nonce:', nonce);

      const gasConfig = universalNonceManager.getOptimizedGasConfig({}, true);

      return await multiSigService.confirmTransaction(txId, walletClient, {
        nonce,
        ...gasConfig
      });
    }
  ];

  let stepResults = [];

  // 执行业务流程
  const result = await transactionQueue.executeBusinessFlow(
    'MULTISIG_CONFIRM',
    account,
    stepFunctions,
    {
      onStepComplete: (step, stepResult) => {
        stepResults.push(stepResult);
        universalNonceManager.markTransactionComplete(account, stepResult.hash || stepResult.txHash);
      }
    }
  );

  return {
    success: true,
    txId,
    queueId: result.queueId,
    results: stepResults,
    message: '多签交易确认完成'
  };
}

/**
 * 优化后的取消质押节点流程（2次弹窗）
 * @param {Object} walletClient - 钱包客户端
 * @param {string} account - 用户账户
 * @returns {Promise<Object>} 执行结果
 */
export async function optimizedUnstakeNode(walletClient, account) {
  console.log('🚀 [OptimizedBusinessFlows] 开始优化的取消质押节点流程:', {
    account,
    walletType: universalNonceManager.walletType
  });

  // 导入必要的合约地址和ABI
  const { CONTRACT_ADDRESSES, ABIS } = await import('@/constants/contracts.js');
  const { createPublicClient, http } = await import('viem');
  const { bscTestnet } = await import('viem/chains');

  const publicClient = createPublicClient({
    chain: bscTestnet,
    transport: http()
  });

  const stakingAddress = CONTRACT_ADDRESSES[97].NodeStaking;
  const qptLockerAddress = CONTRACT_ADDRESSES[97].QPTLocker;

  // 定义两个步骤的执行函数
  const stepFunctions = [
    // 步骤1：取消质押
    async ({ nonce }) => {
      console.log('📞 [Step1] 取消质押，Nonce:', nonce);

      const gasConfig = universalNonceManager.getOptimizedGasConfig({}, true);

      const txHash = await walletClient.writeContract({
        address: stakingAddress,
        abi: ABIS.NodeStaking,
        functionName: 'unstakeNode',
        account,
        ...gasConfig,
        nonce
      });

      const receipt = await publicClient.waitForTransactionReceipt({ hash: txHash });
      return { hash: txHash, receipt };
    },

    // 步骤2：提取QPT
    async ({ nonce }) => {
      console.log('📞 [Step2] 提取QPT，Nonce:', nonce);

      const gasConfig = universalNonceManager.getOptimizedGasConfig({}, false);

      const txHash = await walletClient.writeContract({
        address: qptLockerAddress,
        abi: ABIS.QPTLocker,
        functionName: 'withdrawQPT',
        account,
        ...gasConfig,
        nonce
      });

      const receipt = await publicClient.waitForTransactionReceipt({ hash: txHash });
      return { hash: txHash, receipt };
    }
  ];

  let stepResults = [];

  // 执行业务流程
  const result = await transactionQueue.executeBusinessFlow(
    'UNSTAKE_NODE',
    account,
    stepFunctions,
    {
      onStepComplete: (step, stepResult) => {
        stepResults.push(stepResult);
        universalNonceManager.markTransactionComplete(account, stepResult.hash);
      }
    }
  );

  return {
    success: true,
    queueId: result.queueId,
    results: stepResults,
    message: '取消质押完成：取消质押 → 提取QPT'
  };
}

/**
 * 优化后的单个交易执行（通用）
 * @param {Object} params - 参数
 * @returns {Promise<Object>} 执行结果
 */
export async function optimizedSingleTransaction(params) {
  const {
    userAddress,
    transactionFn,
    operationName = '交易',
    isHighPriority = false,
    gasStrategy = 'standard'
  } = params;

  console.log('🚀 [OptimizedBusinessFlows] 开始优化的单个交易:', {
    userAddress,
    operationName,
    isHighPriority,
    gasStrategy,
    walletType: universalNonceManager.walletType
  });

  try {
    // 使用通用Nonce管理器执行交易
    const result = await universalNonceManager.executeTransaction(
      userAddress,
      async ({ nonce }) => {
        console.log('📞 [SingleTransaction] 执行交易，Nonce:', nonce);

        // 获取优化的Gas配置
        const gasConfig = universalNonceManager.getOptimizedGasConfig({}, isHighPriority);

        // 执行交易函数
        return await transactionFn({ nonce, gasConfig });
      },
      {
        highPriority: isHighPriority,
        maxRetries: 3,
        retryDelay: 3000,
        onProgress: (progress) => {
          console.log('📊 [SingleTransaction] 交易进度:', progress);
        }
      }
    );

    // 标记交易完成
    universalNonceManager.markTransactionComplete(
      userAddress,
      result.hash || result.txHash
    );

    console.log('✅ [OptimizedBusinessFlows] 单个交易完成:', {
      operationName,
      txHash: result.hash || result.txHash
    });

    return {
      success: true,
      result,
      txHash: result.hash || result.txHash,
      message: `${operationName}完成`
    };

  } catch (error) {
    console.error('❌ [OptimizedBusinessFlows] 单个交易失败:', error);
    throw error;
  }
}
