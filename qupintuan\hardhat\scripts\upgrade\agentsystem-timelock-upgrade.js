// qupintuan/hardhat/scripts/upgrade/agentsystem-timelock-upgrade.js
// 通过Timelock升级AgentSystem（Timelock有权限）

const { ethers } = require("hardhat");

async function main() {
  console.log("⏰ 通过Timelock升级AgentSystem...\n");

  // 升级信息
  const MULTISIG_ADDRESS = "******************************************";
  const TIMELOCK_ADDRESS = "******************************************";
  const AGENT_SYSTEM_PROXY = "******************************************";
  const NEW_IMPLEMENTATION = "******************************************";

  try {
    const [deployer] = await ethers.getSigners();
    console.log(`🔑 提交账户: ${deployer.address}\n`);

    console.log("📋 升级信息:");
    console.log(`   多签钱包: ${MULTISIG_ADDRESS}`);
    console.log(`   Timelock: ${TIMELOCK_ADDRESS}`);
    console.log(`   代理合约: ${AGENT_SYSTEM_PROXY}`);
    console.log(`   新实现: ${NEW_IMPLEMENTATION}`);
    console.log("");

    // 1. 准备Timelock升级调用数据
    console.log("1️⃣ 准备Timelock升级调用数据...");
    
    // 准备upgradeTo调用数据
    const upgradeInterface = new ethers.Interface([
      "function upgradeTo(address newImplementation)"
    ]);
    
    const upgradeCalldata = upgradeInterface.encodeFunctionData("upgradeTo", [
      NEW_IMPLEMENTATION
    ]);
    
    console.log(`   升级函数: upgradeTo(address)`);
    console.log(`   升级调用数据: ${upgradeCalldata}`);

    // 2. 准备Timelock schedule调用数据
    console.log("\n2️⃣ 准备Timelock schedule调用数据...");
    
    const timelockInterface = new ethers.Interface([
      "function schedule(address target, uint256 value, bytes calldata data, bytes32 predecessor, bytes32 salt, uint256 delay)"
    ]);
    
    const salt = ethers.keccak256(ethers.toUtf8Bytes(`AgentSystem-Upgrade-${Date.now()}`));
    const delay = 600; // 10分钟延迟
    
    const scheduleCalldata = timelockInterface.encodeFunctionData("schedule", [
      AGENT_SYSTEM_PROXY,  // target
      0,                   // value
      upgradeCalldata,     // data
      ethers.ZeroHash,     // predecessor
      salt,                // salt
      delay                // delay
    ]);
    
    console.log(`   Schedule调用数据: ${scheduleCalldata}`);
    console.log(`   Salt: ${salt}`);
    console.log(`   延迟: ${delay} 秒 (10分钟)`);

    // 3. 准备Timelock execute调用数据
    console.log("\n3️⃣ 准备Timelock execute调用数据...");
    
    const executeInterface = new ethers.Interface([
      "function execute(address target, uint256 value, bytes calldata data, bytes32 predecessor, bytes32 salt)"
    ]);
    
    const executeCalldata = executeInterface.encodeFunctionData("execute", [
      AGENT_SYSTEM_PROXY,  // target
      0,                   // value
      upgradeCalldata,     // data
      ethers.ZeroHash,     // predecessor
      salt                 // salt (必须与schedule时相同)
    ]);
    
    console.log(`   Execute调用数据: ${executeCalldata}`);

    // 4. 连接多签钱包并提交Schedule提案
    console.log("\n4️⃣ 提交Schedule提案到多签钱包...");
    
    const multisig = await ethers.getContractAt("MultiSigWallet", MULTISIG_ADDRESS);
    
    try {
      const scheduleSubmitTx = await multisig.submitTransaction(
        TIMELOCK_ADDRESS,
        0,
        scheduleCalldata
      );
      
      console.log(`   ⏳ Schedule提案已提交: ${scheduleSubmitTx.hash}`);
      const scheduleReceipt = await scheduleSubmitTx.wait();
      console.log(`   ✅ Schedule提案确认成功!`);
      
      // 获取Schedule提案ID
      const scheduleEvents = scheduleReceipt.logs.filter(log => {
        try {
          const parsed = multisig.interface.parseLog(log);
          return parsed.name === 'SubmitTransaction';
        } catch {
          return false;
        }
      });
      
      let scheduleProposalId;
      if (scheduleEvents.length > 0) {
        const parsed = multisig.interface.parseLog(scheduleEvents[0]);
        scheduleProposalId = parsed.args.txIndex;
        console.log(`   📝 Schedule提案ID: ${scheduleProposalId}`);
      }

      // 5. 提交Execute提案到多签钱包
      console.log("\n5️⃣ 提交Execute提案到多签钱包...");
      
      const executeSubmitTx = await multisig.submitTransaction(
        TIMELOCK_ADDRESS,
        0,
        executeCalldata
      );
      
      console.log(`   ⏳ Execute提案已提交: ${executeSubmitTx.hash}`);
      const executeReceipt = await executeSubmitTx.wait();
      console.log(`   ✅ Execute提案确认成功!`);
      
      // 获取Execute提案ID
      const executeEvents = executeReceipt.logs.filter(log => {
        try {
          const parsed = multisig.interface.parseLog(log);
          return parsed.name === 'SubmitTransaction';
        } catch {
          return false;
        }
      });
      
      let executeProposalId;
      if (executeEvents.length > 0) {
        const parsed = multisig.interface.parseLog(executeEvents[0]);
        executeProposalId = parsed.args.txIndex;
        console.log(`   📝 Execute提案ID: ${executeProposalId}`);
      }

      // 6. 保存提案信息
      console.log("\n6️⃣ 保存提案信息...");
      
      const proposalInfo = {
        timestamp: new Date().toISOString(),
        network: "bscTestnet",
        multisigAddress: MULTISIG_ADDRESS,
        timelockAddress: TIMELOCK_ADDRESS,
        proxyAddress: AGENT_SYSTEM_PROXY,
        newImplementation: NEW_IMPLEMENTATION,
        salt: salt,
        delay: delay,
        scheduleProposalId: scheduleProposalId,
        executeProposalId: executeProposalId,
        scheduleCalldata: scheduleCalldata,
        executeCalldata: executeCalldata,
        upgradeCalldata: upgradeCalldata,
        submitter: deployer.address
      };
      
      const fs = require('fs');
      const filename = `agentsystem-timelock-upgrade-proposals-${Date.now()}.json`;
      fs.writeFileSync(filename, JSON.stringify(proposalInfo, null, 2));
      console.log(`   ✅ 提案信息已保存到: ${filename}`);

      // 7. 下一步指南
      console.log("\n7️⃣ 下一步操作指南...");
      console.log("=".repeat(50));
      
      console.log(`\n🔐 多签钱包操作步骤:`);
      console.log(`\n第一步 - 执行Schedule提案:`);
      if (scheduleProposalId) {
        console.log(`   1. 登录多签钱包管理界面`);
        console.log(`   2. 确认并执行Schedule提案ID: ${scheduleProposalId}`);
        console.log(`   3. 等待交易确认`);
      }
      
      console.log(`\n第二步 - 等待延迟期:`);
      console.log(`   1. 延迟时间: 10分钟 (600秒)`);
      console.log(`   2. 在Schedule执行后开始计时`);
      
      console.log(`\n第三步 - 执行Execute提案:`);
      if (executeProposalId) {
        console.log(`   1. 等待延迟期结束`);
        console.log(`   2. 登录多签钱包管理界面`);
        console.log(`   3. 确认并执行Execute提案ID: ${executeProposalId}`);
        console.log(`   4. 等待升级完成`);
      }
      
      console.log(`\n📋 重要提醒:`);
      console.log(`   ✅ Timelock有ADMIN_ROLE权限，这次应该会成功`);
      console.log(`   ✅ 使用正确的upgradeTo函数`);
      console.log(`   ✅ Execute必须使用相同的salt值: ${salt}`);
      
      console.log(`\n🔍 监控命令:`);
      console.log(`   npx hardhat run scripts/upgrade/agentsystem-monitor-timelock-upgrade.js --network bscTestnet`);

      console.log("\n🎉 Timelock升级提案提交成功!");
      
    } catch (error) {
      console.log(`   ❌ 提交提案失败: ${error.message}`);
      throw error;
    }

  } catch (error) {
    console.error("❌ Timelock升级失败:", error.message);
    console.error("详细错误:", error);
    process.exit(1);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
