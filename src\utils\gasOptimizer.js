// src/utils/gasOptimizer.js
/**
 * Gas费优化器 - 实现高优先级Gas策略
 * 特别针对第一笔交易使用更高的Gas费，减少在pending pool中的滞留时间
 */

import { createPublicClient, http } from 'viem';
import { bscTestnet } from 'viem/chains';
import { detectWalletType } from './universalNonceManager.js';

/**
 * Gas策略类型
 */
export const GAS_STRATEGY = {
  ECONOMY: 'economy',      // 经济模式
  STANDARD: 'standard',    // 标准模式
  FAST: 'fast',           // 快速模式
  URGENT: 'urgent'        // 紧急模式
};

/**
 * 钱包特定的Gas配置
 */
const WALLET_GAS_CONFIG = {
  tokenpocket: {
    multiplier: 1.3,
    minGasPrice: 5000000000n, // 5 Gwei
    maxGasPrice: 50000000000n, // 50 Gwei
    priorityFeeMultiplier: 1.5
  },
  trust: {
    multiplier: 1.25,
    minGasPrice: 5000000000n,
    maxGasPrice: 40000000000n,
    priorityFeeMultiplier: 1.4
  },
  imtoken: {
    multiplier: 1.25,
    minGasPrice: 5000000000n,
    maxGasPrice: 40000000000n,
    priorityFeeMultiplier: 1.4
  },
  metamask: {
    multiplier: 1.1,
    minGasPrice: 3000000000n,
    maxGasPrice: 30000000000n,
    priorityFeeMultiplier: 1.2
  },
  mobile_unknown: {
    multiplier: 1.3,
    minGasPrice: 5000000000n,
    maxGasPrice: 50000000000n,
    priorityFeeMultiplier: 1.5
  },
  desktop: {
    multiplier: 1.2,
    minGasPrice: 3000000000n,
    maxGasPrice: 35000000000n,
    priorityFeeMultiplier: 1.3
  }
};

/**
 * Gas优化器类
 */
class GasOptimizer {
  constructor() {
    this.publicClient = null;
    this.walletType = detectWalletType();
    this.gasHistory = new Map(); // Gas价格历史记录
    this.networkCongestion = 'normal'; // 网络拥堵状态
    
    console.log('⛽ [GasOptimizer] 初始化完成，钱包类型:', this.walletType);
  }

  /**
   * 获取公共客户端
   */
  getPublicClient() {
    if (!this.publicClient) {
      this.publicClient = createPublicClient({
        chain: bscTestnet,
        transport: http()
      });
    }
    return this.publicClient;
  }

  /**
   * 获取当前网络Gas价格
   * @returns {Promise<Object>} Gas价格信息
   */
  async getCurrentGasPrice() {
    try {
      const publicClient = this.getPublicClient();
      
      // 获取当前Gas价格
      const gasPrice = await publicClient.getGasPrice();
      
      // 尝试获取EIP-1559费用历史（如果支持）
      let feeHistory = null;
      try {
        feeHistory = await publicClient.getFeeHistory({
          blockCount: 4,
          rewardPercentiles: [25, 50, 75]
        });
      } catch (error) {
        console.warn('⚠️ [GasOptimizer] 无法获取费用历史:', error.message);
      }

      const gasPriceInfo = {
        gasPrice,
        timestamp: Date.now(),
        feeHistory,
        baseFeePerGas: feeHistory?.baseFeePerGas?.[feeHistory.baseFeePerGas.length - 1],
        priorityFeePerGas: feeHistory?.reward?.[feeHistory.reward.length - 1]?.[1] // 中位数
      };

      // 更新Gas历史记录
      this.updateGasHistory(gasPriceInfo);
      
      // 评估网络拥堵状态
      this.assessNetworkCongestion(gasPriceInfo);

      console.log('⛽ [GasOptimizer] 当前Gas价格:', {
        gasPrice: gasPrice.toString(),
        baseFee: gasPriceInfo.baseFeePerGas?.toString(),
        priorityFee: gasPriceInfo.priorityFeePerGas?.toString(),
        congestion: this.networkCongestion
      });

      return gasPriceInfo;
      
    } catch (error) {
      console.error('❌ [GasOptimizer] 获取Gas价格失败:', error);
      
      // 降级方案：使用默认Gas价格
      return {
        gasPrice: 5000000000n, // 5 Gwei
        timestamp: Date.now(),
        feeHistory: null,
        baseFeePerGas: 3000000000n,
        priorityFeePerGas: 2000000000n
      };
    }
  }

  /**
   * 更新Gas历史记录
   * @param {Object} gasPriceInfo - Gas价格信息
   */
  updateGasHistory(gasPriceInfo) {
    const key = Math.floor(gasPriceInfo.timestamp / 60000); // 按分钟分组
    this.gasHistory.set(key, gasPriceInfo);
    
    // 只保留最近10分钟的历史记录
    const cutoff = key - 10;
    for (const [historyKey] of this.gasHistory) {
      if (historyKey < cutoff) {
        this.gasHistory.delete(historyKey);
      }
    }
  }

  /**
   * 评估网络拥堵状态
   * @param {Object} gasPriceInfo - Gas价格信息
   */
  assessNetworkCongestion(gasPriceInfo) {
    const currentGasPrice = Number(gasPriceInfo.gasPrice);
    
    // 基于Gas价格判断网络拥堵状态
    if (currentGasPrice > 20000000000) { // > 20 Gwei
      this.networkCongestion = 'high';
    } else if (currentGasPrice > 10000000000) { // > 10 Gwei
      this.networkCongestion = 'medium';
    } else {
      this.networkCongestion = 'low';
    }

    console.log('📊 [GasOptimizer] 网络拥堵状态:', this.networkCongestion, 'Gas价格:', currentGasPrice);
  }

  /**
   * 获取优化的Gas配置
   * @param {Object} options - 选项
   * @returns {Promise<Object>} 优化后的Gas配置
   */
  async getOptimizedGasConfig(options = {}) {
    const {
      strategy = GAS_STRATEGY.STANDARD,
      isFirstTransaction = false,
      isUrgent = false,
      baseGasLimit = null,
      customMultiplier = null
    } = options;

    try {
      // 获取当前Gas价格
      const gasPriceInfo = await this.getCurrentGasPrice();
      
      // 获取钱包特定配置
      const walletConfig = WALLET_GAS_CONFIG[this.walletType] || WALLET_GAS_CONFIG.desktop;
      
      // 计算Gas倍数
      let totalMultiplier = walletConfig.multiplier;
      
      // 第一笔交易使用更高的Gas
      if (isFirstTransaction) {
        totalMultiplier *= 1.5;
        console.log('🚀 [GasOptimizer] 第一笔交易，使用高优先级Gas');
      }
      
      // 紧急交易
      if (isUrgent) {
        totalMultiplier *= 1.3;
        console.log('🚨 [GasOptimizer] 紧急交易，进一步提高Gas');
      }
      
      // 根据策略调整
      switch (strategy) {
        case GAS_STRATEGY.ECONOMY:
          totalMultiplier *= 0.9;
          break;
        case GAS_STRATEGY.FAST:
          totalMultiplier *= 1.2;
          break;
        case GAS_STRATEGY.URGENT:
          totalMultiplier *= 1.5;
          break;
        default: // STANDARD
          break;
      }
      
      // 根据网络拥堵状态调整
      switch (this.networkCongestion) {
        case 'high':
          totalMultiplier *= 1.4;
          break;
        case 'medium':
          totalMultiplier *= 1.2;
          break;
        default: // low
          break;
      }
      
      // 自定义倍数
      if (customMultiplier) {
        totalMultiplier *= customMultiplier;
      }

      // 构建Gas配置
      const gasConfig = {};

      // Legacy Gas Price (适用于不支持EIP-1559的网络)
      if (gasPriceInfo.gasPrice) {
        let optimizedGasPrice = BigInt(Math.floor(Number(gasPriceInfo.gasPrice) * totalMultiplier));
        
        // 确保在合理范围内
        optimizedGasPrice = this.clampGasPrice(optimizedGasPrice, walletConfig);
        
        gasConfig.gasPrice = optimizedGasPrice;
      }

      // EIP-1559 Gas配置
      if (gasPriceInfo.baseFeePerGas && gasPriceInfo.priorityFeePerGas) {
        const baseFee = gasPriceInfo.baseFeePerGas;
        let priorityFee = BigInt(Math.floor(Number(gasPriceInfo.priorityFeePerGas) * walletConfig.priorityFeeMultiplier * totalMultiplier));
        
        // 确保优先费用合理
        const minPriorityFee = 1000000000n; // 1 Gwei
        const maxPriorityFee = 10000000000n; // 10 Gwei
        priorityFee = priorityFee < minPriorityFee ? minPriorityFee : priorityFee;
        priorityFee = priorityFee > maxPriorityFee ? maxPriorityFee : priorityFee;
        
        gasConfig.maxPriorityFeePerGas = priorityFee;
        gasConfig.maxFeePerGas = baseFee * 2n + priorityFee; // 基础费用的2倍 + 优先费用
      }

      // Gas Limit
      if (baseGasLimit) {
        gasConfig.gas = BigInt(Math.floor(Number(baseGasLimit) * 1.2)); // 增加20%缓冲
      }

      console.log('⛽ [GasOptimizer] Gas配置优化完成:', {
        strategy,
        isFirstTransaction,
        isUrgent,
        walletType: this.walletType,
        networkCongestion: this.networkCongestion,
        totalMultiplier: totalMultiplier.toFixed(2),
        gasConfig: {
          gasPrice: gasConfig.gasPrice?.toString(),
          maxFeePerGas: gasConfig.maxFeePerGas?.toString(),
          maxPriorityFeePerGas: gasConfig.maxPriorityFeePerGas?.toString(),
          gas: gasConfig.gas?.toString()
        }
      });

      return gasConfig;
      
    } catch (error) {
      console.error('❌ [GasOptimizer] Gas配置优化失败:', error);
      
      // 降级方案：返回基础配置
      return this.getFallbackGasConfig(isFirstTransaction);
    }
  }

  /**
   * 限制Gas价格在合理范围内
   * @param {BigInt} gasPrice - Gas价格
   * @param {Object} walletConfig - 钱包配置
   * @returns {BigInt} 限制后的Gas价格
   */
  clampGasPrice(gasPrice, walletConfig) {
    if (gasPrice < walletConfig.minGasPrice) {
      return walletConfig.minGasPrice;
    }
    if (gasPrice > walletConfig.maxGasPrice) {
      return walletConfig.maxGasPrice;
    }
    return gasPrice;
  }

  /**
   * 获取降级Gas配置
   * @param {boolean} isFirstTransaction - 是否是第一笔交易
   * @returns {Object} 降级Gas配置
   */
  getFallbackGasConfig(isFirstTransaction = false) {
    const walletConfig = WALLET_GAS_CONFIG[this.walletType] || WALLET_GAS_CONFIG.desktop;
    const multiplier = isFirstTransaction ? 1.5 : 1.0;
    
    return {
      gasPrice: BigInt(Math.floor(Number(walletConfig.minGasPrice) * walletConfig.multiplier * multiplier)),
      gas: 500000n // 默认Gas limit
    };
  }

  /**
   * 估算交易Gas limit
   * @param {Object} contract - 合约实例
   * @param {string} functionName - 函数名
   * @param {Array} args - 参数
   * @param {Object} options - 选项
   * @returns {Promise<BigInt>} 估算的Gas limit
   */
  async estimateGasLimit(contract, functionName, args = [], options = {}) {
    try {
      const gasEstimate = await contract.estimateGas[functionName](...args, options);
      
      // 增加20%的缓冲
      const gasWithBuffer = gasEstimate * 120n / 100n;
      
      console.log('⛽ [GasOptimizer] Gas估算:', {
        function: functionName,
        estimated: gasEstimate.toString(),
        withBuffer: gasWithBuffer.toString()
      });
      
      return gasWithBuffer;
      
    } catch (error) {
      console.error('❌ [GasOptimizer] Gas估算失败:', error);
      
      // 返回默认值
      return 500000n;
    }
  }

  /**
   * 获取Gas使用统计
   * @returns {Object} Gas使用统计
   */
  getGasStats() {
    const recentHistory = Array.from(this.gasHistory.values()).slice(-5);
    
    if (recentHistory.length === 0) {
      return {
        averageGasPrice: 0,
        minGasPrice: 0,
        maxGasPrice: 0,
        trend: 'stable',
        networkCongestion: this.networkCongestion
      };
    }
    
    const gasPrices = recentHistory.map(h => Number(h.gasPrice));
    const averageGasPrice = gasPrices.reduce((a, b) => a + b, 0) / gasPrices.length;
    const minGasPrice = Math.min(...gasPrices);
    const maxGasPrice = Math.max(...gasPrices);
    
    // 判断趋势
    let trend = 'stable';
    if (gasPrices.length >= 2) {
      const recent = gasPrices.slice(-2);
      if (recent[1] > recent[0] * 1.1) {
        trend = 'rising';
      } else if (recent[1] < recent[0] * 0.9) {
        trend = 'falling';
      }
    }
    
    return {
      averageGasPrice,
      minGasPrice,
      maxGasPrice,
      trend,
      networkCongestion: this.networkCongestion,
      sampleSize: recentHistory.length
    };
  }
}

// 全局单例实例
export const gasOptimizer = new GasOptimizer();

// 默认导出
export default gasOptimizer;
