// src/utils/optimizedTransactionManager.js
/**
 * 优化的交易管理器 - 整合所有Nonce优化组件
 * 提供统一的接口来处理所有业务流程的交易
 */

import { universalNonceManager } from './universalNonceManager.js';
import { transactionQueue, BUSINESS_FLOWS } from './transactionQueue.js';
import { gasOptimizer, GAS_STRATEGY } from './gasOptimizer.js';
import { enhancedErrorHandler } from './enhancedErrorHandler.js';
import { createOptimizedSendTx } from './transactionHandler.js';

/**
 * 优化的交易管理器类
 */
class OptimizedTransactionManager {
  constructor() {
    this.activeTransactions = new Map(); // 活跃交易记录
    this.transactionHistory = new Map(); // 交易历史记录
    
    console.log('🔧 [OptimizedTransactionManager] 初始化完成');
  }

  /**
   * 执行单个优化交易
   * @param {Object} params - 参数
   * @returns {Promise<Object>} 交易结果
   */
  async executeSingleTransaction(params) {
    const {
      userAddress,
      transactionFn,
      operationName = '交易',
      isFirstTransaction = false,
      gasStrategy = GAS_STRATEGY.STANDARD,
      maxRetries = 3,
      onProgress,
      onSuccess,
      onError
    } = params;

    if (!userAddress || !transactionFn) {
      throw new Error('userAddress和transactionFn是必需的参数');
    }

    const transactionId = `single_${userAddress}_${Date.now()}`;
    
    console.log('🚀 [OptimizedTransactionManager] 执行单个交易:', {
      transactionId,
      userAddress,
      operationName,
      isFirstTransaction,
      gasStrategy
    });

    try {
      // 记录交易开始
      this.activeTransactions.set(transactionId, {
        id: transactionId,
        userAddress,
        operationName,
        startTime: new Date(),
        status: 'executing'
      });

      // 执行交易
      const result = await universalNonceManager.executeTransaction(
        userAddress,
        async ({ nonce }) => {
          // 获取优化的Gas配置
          const gasConfig = await gasOptimizer.getOptimizedGasConfig({
            strategy: gasStrategy,
            isFirstTransaction,
            isUrgent: gasStrategy === GAS_STRATEGY.URGENT
          });

          console.log('⛽ [OptimizedTransactionManager] 使用Gas配置:', gasConfig);

          // 执行交易函数
          return await transactionFn({ nonce, gasConfig });
        },
        {
          highPriority: isFirstTransaction,
          maxRetries,
          onProgress: (progress) => {
            if (onProgress) {
              onProgress({ transactionId, ...progress });
            }
          }
        }
      );

      // 标记交易完成
      const transaction = this.activeTransactions.get(transactionId);
      transaction.status = 'completed';
      transaction.endTime = new Date();
      transaction.result = result;
      transaction.txHash = result.hash || result.txHash;

      // 移动到历史记录
      this.transactionHistory.set(transactionId, transaction);
      this.activeTransactions.delete(transactionId);

      console.log('✅ [OptimizedTransactionManager] 单个交易完成:', {
        transactionId,
        txHash: transaction.txHash,
        duration: transaction.endTime - transaction.startTime
      });

      if (onSuccess) {
        onSuccess({ transactionId, result });
      }

      return {
        success: true,
        transactionId,
        result,
        txHash: transaction.txHash
      };

    } catch (error) {
      // 处理错误
      const transaction = this.activeTransactions.get(transactionId);
      if (transaction) {
        transaction.status = 'failed';
        transaction.endTime = new Date();
        transaction.error = error;

        this.transactionHistory.set(transactionId, transaction);
        this.activeTransactions.delete(transactionId);
      }

      console.error('❌ [OptimizedTransactionManager] 单个交易失败:', {
        transactionId,
        error: error.message
      });

      // 使用增强错误处理器
      const errorResult = enhancedErrorHandler.handleError(error, {
        operation: operationName,
        userAddress
      });

      if (onError) {
        onError({ transactionId, error, errorResult });
      }

      throw error;
    }
  }

  /**
   * 执行业务流程（多步交易）
   * @param {Object} params - 参数
   * @returns {Promise<Object>} 执行结果
   */
  async executeBusinessFlow(params) {
    const {
      flowType,
      userAddress,
      stepFunctions,
      onStepStart,
      onStepComplete,
      onStepError,
      onFlowComplete,
      onFlowError,
      skipSteps = [],
      retryFailedSteps = true
    } = params;

    if (!BUSINESS_FLOWS[flowType]) {
      throw new Error(`未知的业务流程类型: ${flowType}`);
    }

    console.log('🚀 [OptimizedTransactionManager] 执行业务流程:', {
      flowType,
      userAddress,
      totalSteps: stepFunctions.length
    });

    try {
      const result = await transactionQueue.executeBusinessFlow(
        flowType,
        userAddress,
        stepFunctions,
        {
          onStepStart: (step, current, total) => {
            console.log(`🔄 [OptimizedTransactionManager] 步骤开始 ${current}/${total}: ${step.name}`);
            if (onStepStart) {
              onStepStart(step, current, total);
            }
          },
          
          onStepComplete: (step, stepResult, current, total) => {
            console.log(`✅ [OptimizedTransactionManager] 步骤完成 ${current}/${total}: ${step.name}`);
            
            // 标记交易完成
            universalNonceManager.markTransactionComplete(
              userAddress,
              stepResult.hash || stepResult.txHash
            );
            
            if (onStepComplete) {
              onStepComplete(step, stepResult, current, total);
            }
          },
          
          onStepError: (step, error, retryCount, maxRetries) => {
            console.error(`❌ [OptimizedTransactionManager] 步骤失败: ${step.name}`, error);
            
            // 使用增强错误处理器
            enhancedErrorHandler.handleError(error, {
              operation: step.name,
              userAddress
            });
            
            if (onStepError) {
              onStepError(step, error, retryCount, maxRetries);
            }
          },
          
          onFlowComplete: (queueState, results) => {
            console.log('🎉 [OptimizedTransactionManager] 业务流程完成:', {
              flowType,
              queueId: queueState.id,
              totalSteps: results.length
            });
            
            if (onFlowComplete) {
              onFlowComplete(queueState, results);
            }
          },
          
          onFlowError: (queueState, error) => {
            console.error('❌ [OptimizedTransactionManager] 业务流程失败:', {
              flowType,
              queueId: queueState.id,
              error: error.message
            });
            
            if (onFlowError) {
              onFlowError(queueState, error);
            }
          },
          
          skipSteps,
          retryFailedSteps
        }
      );

      return result;

    } catch (error) {
      console.error('❌ [OptimizedTransactionManager] 业务流程执行失败:', error);
      throw error;
    }
  }

  /**
   * 获取用户的交易状态
   * @param {string} userAddress - 用户地址
   * @returns {Object} 交易状态信息
   */
  getUserTransactionStatus(userAddress) {
    const normalizedAddress = userAddress.toLowerCase();
    
    // 获取活跃交易
    const activeTransactions = Array.from(this.activeTransactions.values())
      .filter(tx => tx.userAddress.toLowerCase() === normalizedAddress);
    
    // 获取最近的历史交易
    const recentHistory = Array.from(this.transactionHistory.values())
      .filter(tx => tx.userAddress.toLowerCase() === normalizedAddress)
      .sort((a, b) => b.startTime - a.startTime)
      .slice(0, 10);
    
    // 获取Nonce管理器状态
    const nonceStatus = universalNonceManager.getAccountStatus(userAddress);
    
    // 获取队列状态
    const activeQueues = transactionQueue.getUserActiveQueues(userAddress);
    
    return {
      userAddress: normalizedAddress,
      activeTransactions,
      recentHistory,
      nonceStatus,
      activeQueues,
      walletType: universalNonceManager.walletType
    };
  }

  /**
   * 清理用户的交易缓存
   * @param {string} userAddress - 用户地址
   */
  clearUserCache(userAddress) {
    const normalizedAddress = userAddress.toLowerCase();
    
    // 清理Nonce缓存
    universalNonceManager.clearAccountCache(normalizedAddress);
    
    // 清理交易历史（保留最近5条）
    const userHistory = Array.from(this.transactionHistory.entries())
      .filter(([id, tx]) => tx.userAddress.toLowerCase() === normalizedAddress)
      .sort(([, a], [, b]) => b.startTime - a.startTime);
    
    // 删除旧的历史记录
    userHistory.slice(5).forEach(([id]) => {
      this.transactionHistory.delete(id);
    });
    
    console.log('🧹 [OptimizedTransactionManager] 用户缓存已清理:', normalizedAddress);
  }

  /**
   * 获取系统统计信息
   * @returns {Object} 统计信息
   */
  getSystemStats() {
    const nonceStats = universalNonceManager.getAccountStatus('system');
    const gasStats = gasOptimizer.getGasStats();
    const errorStats = enhancedErrorHandler.getErrorStats();
    
    return {
      activeTransactions: this.activeTransactions.size,
      totalHistoryRecords: this.transactionHistory.size,
      nonceStats,
      gasStats,
      errorStats,
      walletType: universalNonceManager.walletType
    };
  }
}

// 全局单例实例
export const optimizedTransactionManager = new OptimizedTransactionManager();

// 默认导出
export default optimizedTransactionManager;
