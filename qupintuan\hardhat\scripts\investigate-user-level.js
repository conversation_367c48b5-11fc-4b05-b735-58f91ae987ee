// qupintuan/hardhat/scripts/investigate-user-level.js
// 调查特定用户的等级问题

const { ethers } = require("hardhat");

async function main() {
  console.log("🔍 开始调查用户等级问题...\n");

  // 目标用户地址
  const targetUser = "******************************************";
  
  // 合约地址（BSC测试网）
  const AGENT_SYSTEM_PROXY = "******************************************";

  try {
    // 连接到合约
    const agentSystem = await ethers.getContractAt("AgentSystem", AGENT_SYSTEM_PROXY);
    
    console.log(`📋 调查用户: ${targetUser}`);
    console.log(`📋 合约地址: ${AGENT_SYSTEM_PROXY}\n`);

    // 1. 获取用户基本信息
    console.log("1️⃣ 获取用户基本信息...");
    const userInfo = await agentSystem.getUserInfo(targetUser);
    
    const inviter = userInfo[0];
    const level = userInfo[1];
    const totalPerformance = ethers.formatUnits(userInfo[2], 6); // 转换为USDT
    const referralsCount = userInfo[3];
    const personalPerformance = ethers.formatUnits(userInfo[5], 6); // 转换为USDT
    
    console.log(`   推荐人: ${inviter}`);
    console.log(`   当前等级: Level ${level}`);
    console.log(`   团队业绩: ${totalPerformance} USDT`);
    console.log(`   个人业绩: ${personalPerformance} USDT`);
    console.log(`   直推人数: ${referralsCount} 人\n`);

    // 2. 尝试获取升级信息（新版本合约功能）
    console.log("2️⃣ 获取升级信息...");
    try {
      const upgradeInfo = await agentSystem.getUserUpgradeInfo(targetUser);
      const upgradeLevel = upgradeInfo[0];
      const upgradeType = upgradeInfo[1];
      const upgradeTimestamp = upgradeInfo[2];
      const isLevelValid = upgradeInfo[3];
      
      const upgradeTypeNames = ['未升级', '自动升级', '管理员设置'];
      
      console.log(`   等级: Level ${upgradeLevel}`);
      console.log(`   升级类型: ${upgradeTypeNames[upgradeType]} (${upgradeType})`);
      console.log(`   升级时间: ${upgradeTimestamp > 0 ? new Date(Number(upgradeTimestamp) * 1000).toLocaleString() : '未升级'}`);
      console.log(`   等级有效性: ${isLevelValid ? '✅ 有效' : '❌ 无效'}\n`);
    } catch (error) {
      console.log(`   ⚠️ 无法获取升级信息（可能是旧版本合约）: ${error.message}\n`);
    }

    // 3. 尝试验证用户等级（新版本合约功能）
    console.log("3️⃣ 验证用户等级...");
    try {
      const validation = await agentSystem.validateUserLevel(targetUser);
      const isValid = validation[0];
      const upgradeType = validation[1];
      const expectedLevel = validation[2];
      const smallTeamsPerformance = ethers.formatUnits(validation[3], 6);
      const validTeamsCount = validation[4];
      
      const upgradeTypeNames = ['未升级', '自动升级', '管理员设置'];
      
      console.log(`   等级是否有效: ${isValid ? '✅ 有效' : '❌ 无效'}`);
      console.log(`   升级类型: ${upgradeTypeNames[upgradeType]} (${upgradeType})`);
      console.log(`   应有等级: Level ${expectedLevel}`);
      console.log(`   小团队业绩: ${smallTeamsPerformance} USDT`);
      console.log(`   有效团队数: ${validTeamsCount} 个\n`);
    } catch (error) {
      console.log(`   ⚠️ 无法验证等级（可能是旧版本合约）: ${error.message}\n`);
    }

    // 4. 获取直推用户信息
    if (referralsCount > 0) {
      console.log("4️⃣ 获取直推用户信息...");
      const referrals = [];
      
      for (let i = 0; i < referralsCount; i++) {
        try {
          const referralAddress = await agentSystem.getReferral(targetUser, i);
          const referralInfo = await agentSystem.getUserInfo(referralAddress);
          
          const referralLevel = referralInfo[1];
          const referralTotalPerf = ethers.formatUnits(referralInfo[2], 6);
          const referralPersonalPerf = ethers.formatUnits(referralInfo[5], 6);
          const realTotalPerf = parseFloat(referralPersonalPerf) + parseFloat(referralTotalPerf);
          
          referrals.push({
            address: referralAddress,
            level: Number(referralLevel),
            totalPerformance: parseFloat(referralTotalPerf),
            personalPerformance: parseFloat(referralPersonalPerf),
            realTotalPerformance: realTotalPerf
          });
          
          console.log(`   #${i + 1} ${referralAddress}`);
          console.log(`       等级: Level ${referralLevel}`);
          console.log(`       个人业绩: ${referralPersonalPerf} USDT`);
          console.log(`       团队业绩: ${referralTotalPerf} USDT`);
          console.log(`       真实总业绩: ${realTotalPerf.toFixed(2)} USDT`);
          
        } catch (error) {
          console.log(`   ❌ 获取第${i + 1}个直推用户失败: ${error.message}`);
        }
      }
      
      // 5. 计算小团队业绩
      console.log("\n5️⃣ 计算小团队业绩...");
      if (referrals.length > 0) {
        // 按真实总业绩排序
        referrals.sort((a, b) => b.realTotalPerformance - a.realTotalPerformance);
        
        const maxTeamPerformance = referrals[0].realTotalPerformance;
        const smallTeamsPerformance = referrals.slice(1).reduce((sum, r) => sum + r.realTotalPerformance, 0);
        const validTeamsCount = referrals.filter(r => r.realTotalPerformance > 0).length;
        
        console.log(`   最大团队业绩: ${maxTeamPerformance.toFixed(2)} USDT (排除)`);
        console.log(`   小团队业绩总和: ${smallTeamsPerformance.toFixed(2)} USDT`);
        console.log(`   有效团队数量: ${validTeamsCount} 个`);
        
        // 6. 判断应有等级
        console.log("\n6️⃣ 判断应有等级...");
        const levelRequirements = [0, 5000, 30000, 100000, 500000];
        
        let shouldBeLevel = 0;
        if (validTeamsCount >= 1) {
          for (let i = 1; i < levelRequirements.length; i++) {
            if (smallTeamsPerformance >= levelRequirements[i]) {
              shouldBeLevel = i;
            } else {
              break;
            }
          }
        }
        
        console.log(`   根据业绩应为: Level ${shouldBeLevel}`);
        console.log(`   当前等级: Level ${level}`);
        
        if (Number(level) > shouldBeLevel) {
          console.log(`   🚨 等级异常！当前等级高于应有等级`);
          console.log(`   📊 业绩分析:`);
          console.log(`      - 需要 ${levelRequirements[Number(level)]} USDT 才能达到 Level ${level}`);
          console.log(`      - 实际只有 ${smallTeamsPerformance.toFixed(2)} USDT`);
          console.log(`      - 差距: ${(levelRequirements[Number(level)] - smallTeamsPerformance).toFixed(2)} USDT`);
        } else if (Number(level) < shouldBeLevel) {
          console.log(`   📈 可以升级！满足升级条件`);
        } else {
          console.log(`   ✅ 等级正常，与业绩匹配`);
        }
      }
    }

    // 7. 检查推荐人信息
    console.log("\n7️⃣ 检查推荐人信息...");
    if (inviter !== ethers.ZeroAddress) {
      try {
        const inviterInfo = await agentSystem.getUserInfo(inviter);
        const inviterLevel = inviterInfo[1];
        const inviterTotalPerf = ethers.formatUnits(inviterInfo[2], 6);
        
        console.log(`   推荐人地址: ${inviter}`);
        console.log(`   推荐人等级: Level ${inviterLevel}`);
        console.log(`   推荐人团队业绩: ${inviterTotalPerf} USDT`);
        
        // 检查是否是系统管理员
        const systemAdmin = await agentSystem.systemAdmin();
        if (inviter.toLowerCase() === systemAdmin.toLowerCase()) {
          console.log(`   🔑 推荐人是系统管理员`);
        }
        
      } catch (error) {
        console.log(`   ❌ 获取推荐人信息失败: ${error.message}`);
      }
    } else {
      console.log(`   ⚠️ 无推荐人`);
    }

    // 8. 总结
    console.log("\n📋 调查总结:");
    console.log("=".repeat(50));
    
    if (Number(level) > 0) {
      console.log(`✅ 用户确实是 Level ${level} 代理`);
      console.log(`📊 团队业绩: ${totalPerformance} USDT`);
      console.log(`👥 直推人数: ${referralsCount} 人`);
      
      if (Number(level) === 1 && parseFloat(totalPerformance) < 5000) {
        console.log(`🚨 问题确认: 用户等级与业绩不匹配！`);
        console.log(`   - 当前等级: Level 1 (需要5000 USDT)`);
        console.log(`   - 实际业绩: ${totalPerformance} USDT`);
        console.log(`   - 建议: 使用等级验证工具进一步调查升级类型`);
      }
    } else {
      console.log(`ℹ️ 用户是普通用户 (Level 0)`);
    }

  } catch (error) {
    console.error("❌ 调查失败:", error.message);
    console.error("详细错误:", error);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
