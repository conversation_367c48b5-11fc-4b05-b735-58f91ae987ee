// src/utils/enhancedErrorHandler.js
/**
 * 增强的错误处理和重试机制
 * 专门处理Nonce冲突、网络错误、钱包错误等各种情况
 */

import { toast } from 'react-hot-toast';
import { detectWalletType } from './universalNonceManager.js';

/**
 * 错误类型枚举
 */
export const ERROR_TYPES = {
  NONCE_ERROR: 'nonce_error',
  NETWORK_ERROR: 'network_error',
  WALLET_ERROR: 'wallet_error',
  CONTRACT_ERROR: 'contract_error',
  GAS_ERROR: 'gas_error',
  USER_REJECTED: 'user_rejected',
  INSUFFICIENT_FUNDS: 'insufficient_funds',
  UNKNOWN_ERROR: 'unknown_error'
};

/**
 * 错误严重程度
 */
export const ERROR_SEVERITY = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical'
};

/**
 * 钱包特定的错误信息
 */
const WALLET_ERROR_MESSAGES = {
  tokenpocket: {
    connection: '请确保TP钱包应用已打开并保持在后台运行',
    network: '请检查TP钱包的网络设置，确保连接到BSC测试网',
    nonce: '检测到交易顺序冲突，建议重启TP钱包应用后重试',
    gas: 'Gas费不足，请在TP钱包中增加Gas费用或等待网络拥堵缓解'
  },
  trust: {
    connection: '请确保Trust钱包应用已打开并保持连接',
    network: '请在Trust钱包中切换到BSC测试网',
    nonce: '交易顺序冲突，建议重启Trust钱包后重试',
    gas: 'Gas费不足，请增加Gas费用'
  },
  imtoken: {
    connection: '请确保imToken钱包应用已打开',
    network: '请在imToken中切换到BSC测试网',
    nonce: '交易顺序冲突，建议重启imToken后重试',
    gas: 'Gas费不足，请增加Gas费用'
  },
  metamask: {
    connection: '请确保MetaMask已安装并解锁',
    network: '请在MetaMask中切换到BSC测试网',
    nonce: '交易Nonce冲突，建议重置MetaMask账户或等待片刻重试',
    gas: 'Gas费不足，请增加Gas费用'
  },
  mobile_unknown: {
    connection: '请确保钱包应用已打开并保持在后台运行',
    network: '请检查钱包的网络设置，确保连接到正确的网络',
    nonce: '检测到交易顺序冲突，建议重启钱包应用后重试',
    gas: 'Gas费不足，请增加Gas费用'
  },
  desktop: {
    connection: '请检查钱包连接状态',
    network: '请切换到正确的网络',
    nonce: '交易Nonce冲突，请稍后重试',
    gas: 'Gas费不足，请增加Gas费用'
  }
};

/**
 * 增强错误处理器类
 */
class EnhancedErrorHandler {
  constructor() {
    this.walletType = detectWalletType();
    this.errorHistory = new Map(); // 错误历史记录
    this.retryAttempts = new Map(); // 重试次数记录
    
    console.log('🛡️ [EnhancedErrorHandler] 初始化完成，钱包类型:', this.walletType);
  }

  /**
   * 分析错误类型
   * @param {Error} error - 错误对象
   * @returns {Object} 错误分析结果
   */
  analyzeError(error) {
    const message = error.message?.toLowerCase() || '';
    const code = error.code || '';
    
    let errorType = ERROR_TYPES.UNKNOWN_ERROR;
    let severity = ERROR_SEVERITY.MEDIUM;
    let isRetryable = false;
    let suggestedAction = '';

    // Nonce相关错误
    if (message.includes('nonce too low') || 
        message.includes('nonce too high') ||
        message.includes('nonce has already been used') ||
        message.includes('replacement transaction underpriced') ||
        message.includes('already known')) {
      errorType = ERROR_TYPES.NONCE_ERROR;
      severity = ERROR_SEVERITY.MEDIUM;
      isRetryable = true;
      suggestedAction = this.getWalletSpecificMessage('nonce');
    }
    
    // 网络相关错误
    else if (message.includes('network') || 
             message.includes('timeout') ||
             message.includes('connection') ||
             message.includes('fetch') ||
             code === 'NETWORK_ERROR') {
      errorType = ERROR_TYPES.NETWORK_ERROR;
      severity = ERROR_SEVERITY.HIGH;
      isRetryable = true;
      suggestedAction = '网络连接不稳定，请检查网络后重试';
    }
    
    // 钱包相关错误
    else if (message.includes('user rejected') ||
             message.includes('user denied') ||
             code === 4001) {
      errorType = ERROR_TYPES.USER_REJECTED;
      severity = ERROR_SEVERITY.LOW;
      isRetryable = false;
      suggestedAction = '用户取消了交易';
    }
    
    // Gas相关错误
    else if (message.includes('gas') ||
             message.includes('out of gas') ||
             message.includes('intrinsic gas too low')) {
      errorType = ERROR_TYPES.GAS_ERROR;
      severity = ERROR_SEVERITY.MEDIUM;
      isRetryable = true;
      suggestedAction = this.getWalletSpecificMessage('gas');
    }
    
    // 余额不足
    else if (message.includes('insufficient funds') ||
             message.includes('insufficient balance')) {
      errorType = ERROR_TYPES.INSUFFICIENT_FUNDS;
      severity = ERROR_SEVERITY.HIGH;
      isRetryable = false;
      suggestedAction = '账户余额不足，请充值后重试';
    }
    
    // 合约相关错误
    else if (message.includes('revert') ||
             message.includes('execution reverted') ||
             message.includes('contract')) {
      errorType = ERROR_TYPES.CONTRACT_ERROR;
      severity = ERROR_SEVERITY.HIGH;
      isRetryable = false;
      suggestedAction = '合约执行失败，请检查交易参数';
    }

    const analysis = {
      errorType,
      severity,
      isRetryable,
      suggestedAction,
      originalMessage: error.message,
      walletType: this.walletType,
      timestamp: new Date().toISOString()
    };

    console.log('🔍 [EnhancedErrorHandler] 错误分析:', analysis);
    return analysis;
  }

  /**
   * 获取钱包特定的错误信息
   * @param {string} category - 错误类别
   * @returns {string} 钱包特定的错误信息
   */
  getWalletSpecificMessage(category) {
    const walletMessages = WALLET_ERROR_MESSAGES[this.walletType] || WALLET_ERROR_MESSAGES.desktop;
    return walletMessages[category] || '请检查钱包状态后重试';
  }

  /**
   * 处理错误并显示用户友好的提示
   * @param {Error} error - 错误对象
   * @param {Object} context - 错误上下文
   * @returns {Object} 处理结果
   */
  handleError(error, context = {}) {
    const { operation = '操作', userAddress = '', txHash = '' } = context;
    
    // 分析错误
    const analysis = this.analyzeError(error);
    
    // 记录错误历史
    this.recordError(analysis, context);
    
    // 生成用户友好的错误信息
    const userMessage = this.generateUserMessage(analysis, operation);
    
    // 显示错误提示
    this.showErrorToast(analysis, userMessage);
    
    // 生成解决建议
    const suggestions = this.generateSuggestions(analysis);
    
    console.error('❌ [EnhancedErrorHandler] 错误处理完成:', {
      operation,
      errorType: analysis.errorType,
      severity: analysis.severity,
      userMessage,
      suggestions
    });

    return {
      analysis,
      userMessage,
      suggestions,
      shouldRetry: analysis.isRetryable,
      retryDelay: this.calculateRetryDelay(analysis)
    };
  }

  /**
   * 记录错误历史
   * @param {Object} analysis - 错误分析结果
   * @param {Object} context - 错误上下文
   */
  recordError(analysis, context) {
    const errorKey = `${analysis.errorType}_${context.userAddress || 'unknown'}`;
    const history = this.errorHistory.get(errorKey) || [];
    
    history.push({
      ...analysis,
      context,
      count: history.length + 1
    });
    
    // 只保留最近10次错误记录
    if (history.length > 10) {
      history.shift();
    }
    
    this.errorHistory.set(errorKey, history);
  }

  /**
   * 生成用户友好的错误信息
   * @param {Object} analysis - 错误分析结果
   * @param {string} operation - 操作名称
   * @returns {string} 用户友好的错误信息
   */
  generateUserMessage(analysis, operation) {
    const { errorType, suggestedAction } = analysis;
    
    let baseMessage = `${operation}失败`;
    
    switch (errorType) {
      case ERROR_TYPES.NONCE_ERROR:
        baseMessage = `${operation}失败：交易顺序冲突`;
        break;
      case ERROR_TYPES.NETWORK_ERROR:
        baseMessage = `${operation}失败：网络连接问题`;
        break;
      case ERROR_TYPES.USER_REJECTED:
        baseMessage = `${operation}已取消`;
        break;
      case ERROR_TYPES.GAS_ERROR:
        baseMessage = `${operation}失败：Gas费不足`;
        break;
      case ERROR_TYPES.INSUFFICIENT_FUNDS:
        baseMessage = `${operation}失败：余额不足`;
        break;
      case ERROR_TYPES.CONTRACT_ERROR:
        baseMessage = `${operation}失败：合约执行错误`;
        break;
      default:
        baseMessage = `${operation}失败：未知错误`;
    }
    
    return suggestedAction ? `${baseMessage}\n${suggestedAction}` : baseMessage;
  }

  /**
   * 显示错误提示
   * @param {Object} analysis - 错误分析结果
   * @param {string} userMessage - 用户消息
   */
  showErrorToast(analysis, userMessage) {
    const { severity, errorType } = analysis;
    
    let toastOptions = {
      duration: 6000,
      position: 'top-center'
    };
    
    // 根据严重程度调整显示时间
    switch (severity) {
      case ERROR_SEVERITY.CRITICAL:
        toastOptions.duration = 10000;
        break;
      case ERROR_SEVERITY.HIGH:
        toastOptions.duration = 8000;
        break;
      case ERROR_SEVERITY.LOW:
        toastOptions.duration = 4000;
        break;
    }
    
    // 根据错误类型选择图标
    let icon = '❌';
    if (errorType === ERROR_TYPES.USER_REJECTED) {
      icon = '🚫';
    } else if (errorType === ERROR_TYPES.NETWORK_ERROR) {
      icon = '🌐';
    } else if (errorType === ERROR_TYPES.NONCE_ERROR) {
      icon = '🔄';
    }
    
    toast.error(userMessage, {
      ...toastOptions,
      icon
    });
  }

  /**
   * 生成解决建议
   * @param {Object} analysis - 错误分析结果
   * @returns {Array} 解决建议列表
   */
  generateSuggestions(analysis) {
    const { errorType } = analysis;
    const suggestions = [];
    
    switch (errorType) {
      case ERROR_TYPES.NONCE_ERROR:
        suggestions.push('等待1-2分钟后重试');
        suggestions.push('重启钱包应用');
        if (this.walletType === 'metamask') {
          suggestions.push('在MetaMask设置中重置账户');
        }
        break;
        
      case ERROR_TYPES.NETWORK_ERROR:
        suggestions.push('检查网络连接');
        suggestions.push('切换到更稳定的网络');
        suggestions.push('稍后重试');
        break;
        
      case ERROR_TYPES.GAS_ERROR:
        suggestions.push('增加Gas费用');
        suggestions.push('等待网络拥堵缓解');
        break;
        
      case ERROR_TYPES.INSUFFICIENT_FUNDS:
        suggestions.push('检查账户余额');
        suggestions.push('充值足够的代币');
        break;
        
      case ERROR_TYPES.CONTRACT_ERROR:
        suggestions.push('检查交易参数');
        suggestions.push('联系技术支持');
        break;
        
      default:
        suggestions.push('稍后重试');
        suggestions.push('联系技术支持');
    }
    
    return suggestions;
  }

  /**
   * 计算重试延迟时间
   * @param {Object} analysis - 错误分析结果
   * @returns {number} 延迟时间（毫秒）
   */
  calculateRetryDelay(analysis) {
    const { errorType, severity } = analysis;
    
    let baseDelay = 3000; // 3秒基础延迟
    
    switch (errorType) {
      case ERROR_TYPES.NONCE_ERROR:
        baseDelay = 5000; // Nonce错误需要更长等待
        break;
      case ERROR_TYPES.NETWORK_ERROR:
        baseDelay = 2000;
        break;
      case ERROR_TYPES.GAS_ERROR:
        baseDelay = 4000;
        break;
    }
    
    // 根据严重程度调整
    switch (severity) {
      case ERROR_SEVERITY.HIGH:
        baseDelay *= 1.5;
        break;
      case ERROR_SEVERITY.CRITICAL:
        baseDelay *= 2;
        break;
    }
    
    return baseDelay;
  }

  /**
   * 检查是否应该重试
   * @param {string} errorKey - 错误键
   * @param {number} maxRetries - 最大重试次数
   * @returns {boolean} 是否应该重试
   */
  shouldRetry(errorKey, maxRetries = 3) {
    const retryCount = this.retryAttempts.get(errorKey) || 0;
    return retryCount < maxRetries;
  }

  /**
   * 增加重试计数
   * @param {string} errorKey - 错误键
   */
  incrementRetryCount(errorKey) {
    const currentCount = this.retryAttempts.get(errorKey) || 0;
    this.retryAttempts.set(errorKey, currentCount + 1);
  }

  /**
   * 重置重试计数
   * @param {string} errorKey - 错误键
   */
  resetRetryCount(errorKey) {
    this.retryAttempts.delete(errorKey);
  }

  /**
   * 获取错误统计
   * @returns {Object} 错误统计信息
   */
  getErrorStats() {
    const stats = {
      totalErrors: 0,
      errorsByType: {},
      errorsBySeverity: {},
      recentErrors: []
    };
    
    for (const history of this.errorHistory.values()) {
      for (const error of history) {
        stats.totalErrors++;
        
        // 按类型统计
        stats.errorsByType[error.errorType] = (stats.errorsByType[error.errorType] || 0) + 1;
        
        // 按严重程度统计
        stats.errorsBySeverity[error.severity] = (stats.errorsBySeverity[error.severity] || 0) + 1;
        
        // 最近的错误
        if (stats.recentErrors.length < 10) {
          stats.recentErrors.push(error);
        }
      }
    }
    
    return stats;
  }
}

// 全局单例实例
export const enhancedErrorHandler = new EnhancedErrorHandler();

// 默认导出
export default enhancedErrorHandler;
