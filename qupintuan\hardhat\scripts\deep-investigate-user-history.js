// qupintuan/hardhat/scripts/deep-investigate-user-history.js
// 深度调查用户的完整历史记录，查找重复升级的原因

const { ethers } = require("hardhat");

async function main() {
  console.log("🔍 深度调查用户历史记录...\n");

  // 目标用户地址
  const targetUser = "******************************************";
  
  // 合约地址（BSC测试网）
  const AGENT_SYSTEM_PROXY = "******************************************";

  try {
    // 连接到合约
    const agentSystem = await ethers.getContractAt("AgentSystem", AGENT_SYSTEM_PROXY);
    
    console.log(`📋 调查用户: ${targetUser}`);
    console.log(`📋 合约地址: ${AGENT_SYSTEM_PROXY}\n`);

    // 1. 获取当前状态
    console.log("1️⃣ 当前用户状态...");
    const userInfo = await agentSystem.getUserInfo(targetUser);
    const currentLevel = Number(userInfo[1]);
    const totalPerformance = ethers.formatUnits(userInfo[2], 6);
    const personalPerformance = ethers.formatUnits(userInfo[5], 6);
    const upgradeType = Number(userInfo[6]);
    const upgradeTimestamp = Number(userInfo[7]);
    
    console.log(`   当前等级: Level ${currentLevel}`);
    console.log(`   团队业绩: ${totalPerformance} USDT`);
    console.log(`   个人业绩: ${personalPerformance} USDT`);
    console.log(`   升级类型: ${['未升级', '自动升级', '管理员设置'][upgradeType]} (${upgradeType})`);
    console.log(`   升级时间: ${upgradeTimestamp > 0 ? new Date(upgradeTimestamp * 1000).toLocaleString() : '未升级'}`);

    // 2. 查询所有等级变更事件
    console.log("\n2️⃣ 查询等级变更历史...");
    
    try {
      // 获取LevelUpdated事件
      const levelUpdatedEvents = await agentSystem.queryFilter(
        agentSystem.filters.LevelUpdated(targetUser),
        0 // 从创世区块开始
      );
      
      console.log(`   发现 ${levelUpdatedEvents.length} 个等级更新事件:`);
      
      for (let i = 0; i < levelUpdatedEvents.length; i++) {
        const event = levelUpdatedEvents[i];
        const block = await event.getBlock();
        const timestamp = new Date(block.timestamp * 1000);
        
        console.log(`\n   事件 ${i + 1}:`);
        console.log(`     区块: ${event.blockNumber}`);
        console.log(`     时间: ${timestamp.toLocaleString()}`);
        console.log(`     交易: ${event.transactionHash}`);
        console.log(`     用户: ${event.args.user}`);
        console.log(`     新等级: Level ${event.args.level}`);
        console.log(`     小团队业绩: ${ethers.formatUnits(event.args.smallTeamsPerformance, 6)} USDT`);
        console.log(`     有效团队数: ${event.args.validTeamsCount}`);
      }
      
    } catch (error) {
      console.log(`   ❌ 获取等级更新事件失败: ${error.message}`);
    }

    // 3. 查询升级类型变更事件
    console.log("\n3️⃣ 查询升级类型变更历史...");
    
    try {
      // 获取LevelUpgradeTypeChanged事件
      const upgradeTypeEvents = await agentSystem.queryFilter(
        agentSystem.filters.LevelUpgradeTypeChanged(targetUser),
        0 // 从创世区块开始
      );
      
      console.log(`   发现 ${upgradeTypeEvents.length} 个升级类型变更事件:`);
      
      for (let i = 0; i < upgradeTypeEvents.length; i++) {
        const event = upgradeTypeEvents[i];
        const block = await event.getBlock();
        const timestamp = new Date(block.timestamp * 1000);
        
        console.log(`\n   事件 ${i + 1}:`);
        console.log(`     区块: ${event.blockNumber}`);
        console.log(`     时间: ${timestamp.toLocaleString()}`);
        console.log(`     交易: ${event.transactionHash}`);
        console.log(`     用户: ${event.args.user}`);
        console.log(`     新等级: Level ${event.args.level}`);
        console.log(`     升级类型: ${['未升级', '自动升级', '管理员设置'][event.args.upgradeType]} (${event.args.upgradeType})`);
        console.log(`     时间戳: ${new Date(Number(event.args.timestamp) * 1000).toLocaleString()}`);
      }
      
    } catch (error) {
      console.log(`   ❌ 获取升级类型事件失败: ${error.message}`);
    }

    // 4. 查询业绩添加事件
    console.log("\n4️⃣ 查询业绩添加历史...");
    
    try {
      // 获取PerformanceAdded事件（用户作为获胜者）
      const performanceEvents = await agentSystem.queryFilter(
        agentSystem.filters.PerformanceAdded(targetUser),
        -5000 // 最近5000个区块
      );
      
      console.log(`   发现 ${performanceEvents.length} 个业绩添加事件（用户作为获胜者）:`);
      
      for (let i = 0; i < Math.min(performanceEvents.length, 10); i++) { // 只显示最近10个
        const event = performanceEvents[i];
        const block = await event.getBlock();
        const timestamp = new Date(block.timestamp * 1000);
        
        console.log(`\n   事件 ${i + 1}:`);
        console.log(`     区块: ${event.blockNumber}`);
        console.log(`     时间: ${timestamp.toLocaleString()}`);
        console.log(`     交易: ${event.transactionHash}`);
        console.log(`     获胜者: ${event.args.user}`);
        console.log(`     业绩: ${ethers.formatUnits(event.args.amount, 6)} USDT`);
      }
      
    } catch (error) {
      console.log(`   ❌ 获取业绩事件失败: ${error.message}`);
    }

    // 5. 查询团队业绩添加事件
    console.log("\n5️⃣ 查询团队业绩添加历史...");
    
    try {
      // 获取TeamPerformanceAdded事件（用户作为推荐人）
      const teamPerformanceEvents = await agentSystem.queryFilter(
        agentSystem.filters.TeamPerformanceAdded(null, targetUser),
        -5000 // 最近5000个区块
      );
      
      console.log(`   发现 ${teamPerformanceEvents.length} 个团队业绩添加事件（用户作为推荐人）:`);
      
      for (let i = 0; i < Math.min(teamPerformanceEvents.length, 10); i++) { // 只显示最近10个
        const event = teamPerformanceEvents[i];
        const block = await event.getBlock();
        const timestamp = new Date(block.timestamp * 1000);
        
        console.log(`\n   事件 ${i + 1}:`);
        console.log(`     区块: ${event.blockNumber}`);
        console.log(`     时间: ${timestamp.toLocaleString()}`);
        console.log(`     交易: ${event.transactionHash}`);
        console.log(`     直推用户: ${event.args.user}`);
        console.log(`     推荐人: ${event.args.inviter}`);
        console.log(`     业绩: ${ethers.formatUnits(event.args.amount, 6)} USDT`);
      }
      
    } catch (error) {
      console.log(`   ❌ 获取团队业绩事件失败: ${error.message}`);
    }

    // 6. 分析最近的交易
    console.log("\n6️⃣ 分析最近可能触发升级的交易...");
    
    // 查找最近调用了可能触发升级的函数的交易
    const latestBlock = await ethers.provider.getBlockNumber();
    const fromBlock = Math.max(0, latestBlock - 1000); // 最近1000个区块
    
    console.log(`   检查区块范围: ${fromBlock} - ${latestBlock}`);
    
    try {
      // 查找所有与该用户相关的交易
      const allEvents = await agentSystem.queryFilter("*", fromBlock);
      
      const userRelatedEvents = allEvents.filter(event => {
        if (!event.args) return false;
        
        // 检查事件参数中是否包含目标用户地址
        const argsArray = Object.values(event.args);
        return argsArray.some(arg => 
          typeof arg === 'string' && 
          arg.toLowerCase() === targetUser.toLowerCase()
        );
      });
      
      console.log(`   发现 ${userRelatedEvents.length} 个与用户相关的事件:`);
      
      for (let i = 0; i < Math.min(userRelatedEvents.length, 5); i++) { // 只显示最近5个
        const event = userRelatedEvents[i];
        const block = await event.getBlock();
        const timestamp = new Date(block.timestamp * 1000);
        
        console.log(`\n   事件 ${i + 1}:`);
        console.log(`     事件名: ${event.fragment?.name || '未知'}`);
        console.log(`     区块: ${event.blockNumber}`);
        console.log(`     时间: ${timestamp.toLocaleString()}`);
        console.log(`     交易: ${event.transactionHash}`);
        
        // 获取交易详情
        try {
          const tx = await ethers.provider.getTransaction(event.transactionHash);
          console.log(`     发起者: ${tx.from}`);
          console.log(`     目标: ${tx.to}`);
          
          // 尝试解析交易数据
          try {
            const decoded = agentSystem.interface.parseTransaction({ data: tx.data });
            console.log(`     函数: ${decoded.name}`);
            console.log(`     参数: ${JSON.stringify(decoded.args, null, 2)}`);
          } catch (decodeError) {
            console.log(`     函数: 无法解析`);
          }
          
        } catch (txError) {
          console.log(`     交易详情获取失败: ${txError.message}`);
        }
      }
      
    } catch (error) {
      console.log(`   ❌ 分析交易失败: ${error.message}`);
    }

    // 7. 检查直推用户的最近活动
    console.log("\n7️⃣ 检查直推用户的最近活动...");
    
    try {
      const referrals = await agentSystem.getUserReferrals(targetUser);
      console.log(`   用户有 ${referrals.length} 个直推用户:`);
      
      for (let i = 0; i < referrals.length; i++) {
        const referralAddress = referrals[i];
        console.log(`\n   直推用户 ${i + 1}: ${referralAddress}`);
        
        // 获取直推用户信息
        const referralInfo = await agentSystem.getUserInfo(referralAddress);
        const referralLevel = Number(referralInfo[1]);
        const referralTotalPerf = ethers.formatUnits(referralInfo[2], 6);
        const referralPersonalPerf = ethers.formatUnits(referralInfo[5], 6);
        
        console.log(`     等级: Level ${referralLevel}`);
        console.log(`     个人业绩: ${referralPersonalPerf} USDT`);
        console.log(`     团队业绩: ${referralTotalPerf} USDT`);
        
        // 查找该直推用户最近的业绩添加
        try {
          const recentPerformance = await agentSystem.queryFilter(
            agentSystem.filters.PerformanceAdded(referralAddress),
            -1000 // 最近1000个区块
          );
          
          if (recentPerformance.length > 0) {
            const lastEvent = recentPerformance[recentPerformance.length - 1];
            const block = await lastEvent.getBlock();
            const timestamp = new Date(block.timestamp * 1000);
            
            console.log(`     最近业绩添加:`);
            console.log(`       时间: ${timestamp.toLocaleString()}`);
            console.log(`       业绩: ${ethers.formatUnits(lastEvent.args.amount, 6)} USDT`);
            console.log(`       交易: ${lastEvent.transactionHash}`);
          }
          
        } catch (error) {
          console.log(`     获取最近业绩失败: ${error.message}`);
        }
      }
      
    } catch (error) {
      console.log(`   ❌ 检查直推用户失败: ${error.message}`);
    }

    // 8. 总结分析
    console.log("\n8️⃣ 问题分析总结...");
    console.log("=".repeat(60));
    
    console.log(`\n🔍 关键发现:`);
    console.log(`1. 用户当前等级: Level ${currentLevel}`);
    console.log(`2. 升级类型: ${['未升级', '自动升级', '管理员设置'][upgradeType]}`);
    console.log(`3. 最后升级时间: ${upgradeTimestamp > 0 ? new Date(upgradeTimestamp * 1000).toLocaleString() : '未升级'}`);
    
    console.log(`\n🚨 需要重点关注:`);
    console.log(`1. 如果有多次等级变更事件，说明用户被多次修复`);
    console.log(`2. 如果最近有业绩添加事件，可能触发了新的升级`);
    console.log(`3. 如果升级类型是"自动升级"但业绩不足，说明升级逻辑仍有问题`);
    console.log(`4. 如果有管理员操作，需要确认是否为误操作`);
    
    console.log(`\n📋 建议下一步:`);
    console.log(`1. 检查合约代码是否还有其他升级触发点`);
    console.log(`2. 验证所有可能调用_upgradeIfEligible的函数`);
    console.log(`3. 检查是否有其他合约或脚本在调用升级函数`);
    console.log(`4. 确认管理员权限的使用情况`);

  } catch (error) {
    console.error("❌ 深度调查失败:", error.message);
    console.error("详细错误:", error);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
