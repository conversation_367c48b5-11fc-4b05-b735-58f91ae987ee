// qupintuan/hardhat/scripts/deploy-fixed-agentsystem.js
// 部署修复后的AgentSystem合约

const { ethers, upgrades } = require("hardhat");

async function main() {
  console.log("🚀 部署修复后的AgentSystem合约...\n");

  // 合约地址配置
  const CURRENT_PROXY = "******************************************";
  const TIMELOCK_ADDRESS = process.env.SECURE_TIMELOCK_ADDRESS || "******************************************";

  try {
    // 获取部署账户
    const [deployer] = await ethers.getSigners();
    console.log("🔑 部署账户:", deployer.address);
    
    const balance = await ethers.provider.getBalance(deployer.address);
    console.log("💰 账户余额:", ethers.formatEther(balance), "BNB\n");

    // 1. 编译并验证合约
    console.log("1️⃣ 编译合约...");
    const AgentSystemFactory = await ethers.getContractFactory("AgentSystem");
    console.log("   ✅ 合约编译成功");

    // 2. 验证存储布局兼容性
    console.log("\n2️⃣ 验证存储布局兼容性...");
    try {
      await upgrades.validateUpgrade(CURRENT_PROXY, AgentSystemFactory);
      console.log("   ✅ 存储布局兼容性检查通过");
    } catch (error) {
      console.log("   ⚠️ 存储布局警告:", error.message);
      console.log("   继续部署新实现合约...");
    }

    // 3. 部署新实现合约
    console.log("\n3️⃣ 部署新实现合约...");
    
    // 准备升级，但不立即执行
    const newImplementationAddress = await upgrades.prepareUpgrade(
      CURRENT_PROXY,
      AgentSystemFactory,
      {
        kind: 'uups'
      }
    );
    
    console.log("   ✅ 新实现合约地址:", newImplementationAddress);

    // 4. 验证新实现合约
    console.log("\n4️⃣ 验证新实现合约...");
    
    try {
      const newImpl = await ethers.getContractAt("AgentSystem", newImplementationAddress);
      
      // 检查关键函数是否存在
      const proxiableUUID = await newImpl.proxiableUUID();
      console.log("   ✅ UUPS UUID:", proxiableUUID);
      
      // 验证修复是否正确 - 检查合约代码
      const code = await ethers.provider.getCode(newImplementationAddress);
      console.log("   ✅ 合约代码长度:", code.length, "字符");
      
    } catch (error) {
      console.log("   ❌ 新实现合约验证失败:", error.message);
      throw error;
    }

    // 5. 生成升级提案数据
    console.log("\n5️⃣ 生成升级提案数据...");
    
    const upgradeInterface = new ethers.Interface([
      "function upgradeToAndCall(address newImplementation, bytes calldata data)"
    ]);
    
    const initData = "0x"; // 不需要初始化数据
    const upgradeCalldata = upgradeInterface.encodeFunctionData("upgradeToAndCall", [
      newImplementationAddress,
      initData
    ]);
    
    console.log("   目标合约:", CURRENT_PROXY);
    console.log("   新实现地址:", newImplementationAddress);
    console.log("   升级调用数据:", upgradeCalldata);

    // 6. 计算Timelock操作ID
    console.log("\n6️⃣ 计算Timelock操作信息...");
    
    const timelock = await ethers.getContractAt("TimelockController", TIMELOCK_ADDRESS);
    const salt = ethers.keccak256(ethers.toUtf8Bytes(`AgentSystem-Fix-${Date.now()}`));
    
    const operationId = await timelock.hashOperation(
      CURRENT_PROXY,      // target
      0,                  // value
      upgradeCalldata,    // data
      ethers.ZeroHash,    // predecessor
      salt               // salt
    );
    
    console.log("   操作ID:", operationId);
    console.log("   Salt:", salt);

    // 7. 保存升级信息
    console.log("\n7️⃣ 保存升级信息...");
    
    const upgradeInfo = {
      timestamp: new Date().toISOString(),
      network: "bscTestnet",
      currentProxy: CURRENT_PROXY,
      newImplementation: newImplementationAddress,
      upgradeCalldata: upgradeCalldata,
      timelockAddress: TIMELOCK_ADDRESS,
      operationId: operationId,
      salt: salt,
      deployer: deployer.address,
      gasEstimate: "待估算",
      description: "修复AgentSystem升级逻辑bug - 修复_upgradeIfEligible函数中的索引错误"
    };
    
    const fs = require('fs');
    const filename = `agentsystem-upgrade-fix-${Date.now()}.json`;
    fs.writeFileSync(filename, JSON.stringify(upgradeInfo, null, 2));
    console.log("   ✅ 升级信息已保存到:", filename);

    // 8. 显示下一步操作指南
    console.log("\n8️⃣ 下一步操作指南...");
    console.log("=".repeat(60));
    
    console.log("\n🔧 升级步骤:");
    console.log("1. 通过多签钱包提交升级提案:");
    console.log(`   目标地址: ${CURRENT_PROXY}`);
    console.log(`   调用数据: ${upgradeCalldata}`);
    console.log("");
    console.log("2. 或者通过Timelock调度升级:");
    console.log(`   npx hardhat run scripts/schedule-agentsystem-upgrade.js --network bscTestnet`);
    console.log("");
    console.log("3. 升级完成后验证:");
    console.log(`   npx hardhat run scripts/verify-agentsystem-fix.js --network bscTestnet`);

    console.log("\n🚨 重要提醒:");
    console.log("- 升级前请确保暂停所有业绩添加操作");
    console.log("- 升级后需要修复所有受影响用户的等级");
    console.log("- 建议在升级后立即运行验证脚本");

    console.log("\n🎉 新实现合约部署完成！");
    console.log("📋 修复内容:");
    console.log("✅ 修复_upgradeIfEligible函数中的关键bug");
    console.log("✅ 正确的升级条件: REQ[lvl + 1] 而不是 REQ[lvl]");
    console.log("✅ 添加边界检查防止数组越界");
    console.log("✅ Level 0 → Level 1 现在正确需要 5000 USDT");

  } catch (error) {
    console.error("❌ 部署失败:", error.message);
    console.error("详细错误:", error);
    process.exit(1);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
