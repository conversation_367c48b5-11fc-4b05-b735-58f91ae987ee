const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/roomManagement-CRZ0InUC-*************-mmao4qk4a.js","assets/vendor-Caz4khA--*************-mmao4qk4a.js"])))=>i.map(i=>d[i]);
import{_ as y}from"./vendor-Caz4khA--*************-mmao4qk4a.js";import{E as g,l as w,s as $,i as f}from"./index-DmsQauDE-*************-rjh9jemju.js";import{fetchTotalRooms as D,fetchRoom as E}from"./basicOperations-CmXjWg1V-*************-mmao4qk4a.js";import{createRoomWithQPTVerification as C,approveQPTForCreate as Q,lockQPTForCreate as M,joinRoomWithPaymentVerification as b}from"./roomManagement-CRZ0InUC-*************-mmao4qk4a.js";import{e as B}from"./rewardOperations-2IHLUMdh-*************-mmao4qk4a.js";import{v as L,p as O,a as R,t as p,e as h}from"./transaction-D--126Ts-*************-mmao4qk4a.js";import"./web3-DJUNf-KT-*************-mmao4qk4a.js";class S{constructor(){this.metrics=new Map,this.networkRequests=[],this.isEnabled=!1}startTimer(t){this.isEnabled&&this.metrics.set(t,{startTime:performance.now(),endTime:null,duration:null})}endTimer(t){if(!this.isEnabled)return;const e=this.metrics.get(t);if(e){e.endTime=performance.now(),e.duration=e.endTime-e.startTime;const s=`${t}_${Math.floor(e.duration/1e3)}s`,o=this.lastLogTimes?.get(s)||0,n=Date.now();if(n-o<5e3)return;this.lastLogTimes||(this.lastLogTimes=new Map),this.lastLogTimes.set(s,n),e.duration>8e3||e.duration>5e3}}recordNetworkRequest(t,e="GET",s=0,o=!0){this.isEnabled&&(this.networkRequests.push({url:t,method:e,duration:s,success:o,timestamp:Date.now()}),this.networkRequests.length>100&&(this.networkRequests=this.networkRequests.slice(-100)),s>1e4&&console.warn(`🚨 [网络监控] 请求超时: ${t} (${s}ms)`))}getPerformanceReport(){if(!this.isEnabled)return null;const t=Array.from(this.metrics.entries()).filter(([n,a])=>a.duration!==null).map(([n,a])=>({name:n,duration:a.duration,status:a.duration>5e3?"慢":a.duration>2e3?"中等":"快"})).sort((n,a)=>a.duration-n.duration),e=this.networkRequests.slice(-20),s=this.networkRequests.filter(n=>!n.success),o=this.networkRequests.filter(n=>n.duration>5e3);return{timing:t,network:{total:this.networkRequests.length,recent:e,failed:s.length,slow:o.length,averageTime:this.networkRequests.length>0?this.networkRequests.reduce((n,a)=>n+a.duration,0)/this.networkRequests.length:0},recommendations:this.generateRecommendations(t,this.networkRequests)}}generateRecommendations(t,e){const s=[],o=t.filter(c=>c.duration>5e3);o.length>0&&s.push({type:"性能优化",message:`发现 ${o.length} 个慢操作，建议优化: ${o.map(c=>c.name).join(", ")}`});const n=e.filter(c=>!c.success).length/e.length;n>.1&&s.push({type:"网络优化",message:`网络请求失败率过高 (${(n*100).toFixed(1)}%)，建议检查RPC节点或网络连接`});const a=e.length>0?e.reduce((c,i)=>c+i.duration,0)/e.length:0;return a>3e3&&s.push({type:"网络优化",message:`平均请求时间过长 (${a.toFixed(0)}ms)，建议优化RPC配置或使用更快的节点`}),s}clear(){this.metrics.clear(),this.networkRequests=[]}}const _=new S,T=r=>_.startTimer(r),q=r=>_.endTimer(r),m=(r,t,e,s)=>_.recordNetworkRequest(r,t,e,s);class N{constructor(){this.pendingRequests=new Map,this.requestCounts=new Map,this.lastRequestTimes=new Map}async deduplicate(t,e,s=1e3){const o=Date.now();if(this.pendingRequests.has(t))return this.pendingRequests.get(t);const n=this.lastRequestTimes.get(t)||0;if(o-n<s)return null;const a=(this.requestCounts.get(t)||0)+1;if(this.requestCounts.set(t,a),a>10&&(console.warn(`⚠️ [请求去重] 请求过于频繁: ${t} (第${a}次)`),a>20))return this.lastRequestTimes.set(t,o+5e3),console.warn(`🚫 [请求去重] 请求被限制，强制延迟5秒: ${t}`),null;const c=(async()=>{try{return this.lastRequestTimes.set(t,o),await e()}catch(i){throw console.error(`❌ [请求去重] 请求失败: ${t}`,i),i}finally{this.pendingRequests.delete(t),setTimeout(()=>{this.requestCounts.delete(t)},300*1e3)}})();return this.pendingRequests.set(t,c),c}clear(t){this.pendingRequests.delete(t),this.requestCounts.delete(t),this.lastRequestTimes.delete(t)}clearAll(){this.pendingRequests.clear(),this.requestCounts.clear(),this.lastRequestTimes.clear()}getStats(){return{pendingCount:this.pendingRequests.size,totalRequests:Array.from(this.requestCounts.values()).reduce((t,e)=>t+e,0),uniqueRequests:this.requestCounts.size,mostFrequentRequest:this.getMostFrequentRequest()}}getMostFrequentRequest(){let t=0,e=null;for(const[s,o]of this.requestCounts.entries())o>t&&(t=o,e=s);return e?{key:e,count:t}:null}}const x=new N,I=(r,t,e)=>x.deduplicate(r,t,e);async function G(r,t){console.log("📞 [groupBuyService] 调用 createAndLock，参数:",{tierAmountStr:t});try{if(/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)||window.ethereum?.isTokenPocket||window.ethereum?.isTrust||window.ethereum?.isImToken||window.ethereum?.isMathWallet){if(console.log("📱 [createAndLock] 检测到移动端环境"),!window.ethereum)throw new Error("未检测到钱包，请确保钱包应用已打开");try{await window.ethereum.request({method:"eth_requestAccounts"}),await new Promise(u=>setTimeout(u,500))}catch{throw new Error("钱包连接失败，请检查钱包状态")}}L(r);const s=O(t),o=await r.getChainId();R(o);const n=Number(s)/1e6,a=[30,50,100,200,500,1e3];a.includes(n)||p(g.INVALID_TIER_AMOUNT,`不支持的拼团金额: ${n}，支持的档位: ${a.join(", ")} (原始值: ${t})`),w({component:"groupBuyService",function:"createAndLock",message:`开始创建拼团房间，金额: ${n}`,tierNum:n,chainId:o}),console.log("🚀 [createAndLock] 开始发起人固定三步操作流程");const{toast:c}=await y(async()=>{const{toast:u}=await import("./vendor-Caz4khA--*************-mmao4qk4a.js").then(l=>l.a9);return{toast:u}},[]);console.log("📞 [createAndLock] 步骤 1/3: 创建拼团房间"),c.loading("第1步：正在创建拼团房间...",{id:"step1"});const i=await h(()=>C({chainId:97,tier:n,signer:r}),"createRoomWithQPTVerification");console.log("✅ [createAndLock] 步骤 1/3: 拼团房间创建成功:",`房间ID: ${i.roomId}`),c.success(`✅ 第1步完成：拼团房间 #${i.roomId} 创建成功！`,{id:"step1"}),console.log("📞 [createAndLock] 步骤 2/3: 授权QPT给QPTLocker合约"),c.loading("第2步：正在授权QPT给锁仓合约...",{id:"step2"});const k=await h(()=>Q({chainId:97,tier:n,signer:r}),"approveQPTForCreate");console.log("✅ [createAndLock] 步骤 2/3: QPT授权成功:",k.message),c.success("✅ 第2步完成：QPT授权成功！",{id:"step2"}),console.log("📞 [createAndLock] 步骤 3/3: 锁仓QPT"),c.loading("第3步：正在锁仓QPT...",{id:"step3"});const A=await h(()=>M({chainId:97,tier:n,roomId:i.roomId,signer:r}),"lockQPTForCreate");console.log("✅ [createAndLock] 步骤 3/3: QPT锁仓成功:",A.message),c.success("✅ 第3步完成：QPT锁仓成功！",{id:"step3"}),console.log("🔍 [createAndLock] createResult 调试信息:",{createResult:i,hasRoomId:i?.roomId!==void 0,hasReceipt:i?.receipt!==void 0,roomIdValue:i?.roomId,receiptHash:i?.receipt?.hash}),(!i||i.roomId===void 0||i.roomId===null||!i.receipt)&&(console.error("❌ [createAndLock] 创建结果验证失败:",{createResult:i,hasCreateResult:!!i,hasRoomId:i?.roomId!==void 0&&i?.roomId!==null,hasReceipt:!!i?.receipt,roomId:i?.roomId,roomIdType:typeof i?.roomId,receipt:i?.receipt}),p(g.CREATE_AND_LOCK_FAILED,"创建房间失败，未获取到有效的 roomId 或 receipt"));const{roomId:d,receipt:P}=i;console.log("🔍 [createAndLock] 验证QPT锁仓状态...");try{const{validateCreatorQPTLock:u}=await y(async()=>{const{validateCreatorQPTLock:v}=await import("./roomManagement-CRZ0InUC-*************-mmao4qk4a.js");return{validateCreatorQPTLock:v}},__vite__mapDeps([0,1])),l=await u({chainId:97,roomId:d});if(!l.isValid)throw console.error("❌ [createAndLock] QPT锁仓验证失败:",l),new Error(`QPT锁仓验证失败: ${l.message}`);console.log("✅ [createAndLock] QPT锁仓验证通过:",{roomId:d,lockedAmount:l.amount,creator:l.creator})}catch(u){console.error("❌ [createAndLock] QPT锁仓验证异常:",u),c.error(`⚠️ QPT锁仓验证失败: ${u.message}`,{duration:8e3,position:"top-center"})}console.log("✅ [createAndLock] 发起人三步操作完成");try{const l=`lottery_${i.roomId}`;localStorage.getItem(l)&&localStorage.removeItem(l)}catch{}return w({component:"groupBuyService",function:"createAndLock",message:`发起人三步操作完成，房间ID: ${d}，交易哈希: ${P.hash}`,roomId:d,receipt:P,approveResult:k.message,lockResult:A.message}),console.log("🎉 [createAndLock] 发起人固定三步操作完成，房间创建成功"),c.success(`🎉 拼团房间创建完成！房间ID: ${i.roomId}`,{duration:6e3,position:"top-center",style:{background:"linear-gradient(135deg, #10b981 0%, #059669 100%)",color:"#fff",fontWeight:"bold",fontSize:"16px",padding:"16px 24px",borderRadius:"12px",boxShadow:"0 8px 32px rgba(0,0,0,0.3)"}}),$("groupBuy",5e3),{receipt:P,roomId:d,createResult:`房间ID: ${i.roomId}`,approveResult:k.message,lockResult:A.message,message:"发起人三步操作完成：创建房间 → 授权QPT → 锁仓QPT"}}catch(e){f({component:"groupBuyService",function:"createAndLock",message:`创建拼团房间失败: ${e.message}`,error:e,tierAmountStr:t});const{toast:s}=await y(async()=>{const{toast:a}=await import("./vendor-Caz4khA--*************-mmao4qk4a.js").then(c=>c.a9);return{toast:a}},[]);s.dismiss("step1"),s.dismiss("step2"),s.dismiss("step3");let o="创建拼团房间失败";const n=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)||window.ethereum?.isTokenPocket||window.ethereum?.isTrust||window.ethereum?.isImToken||window.ethereum?.isMathWallet;throw e.message&&(e.message.includes("用户拒绝")||e.message.includes("User rejected")?o="用户取消了交易":e.message.includes("余额不足")||e.message.includes("insufficient funds")?o="余额不足，请检查BNB和QPT余额":e.message.includes("nonce too low")?o=n?"Nonce太低，请重启钱包应用后重试":"Nonce太低，请稍后重试":e.message.includes("网络")||e.message.includes("network")||e.message.includes("连接")?o="网络连接失败，请检查网络设置":e.message.includes("钱包")||e.message.includes("Cannot read properties of undefined")?o="钱包连接问题，请重新连接钱包后重试":o=`创建失败: ${e.message}`),s.error(o,{duration:6e3,position:"top-center"}),e}}async function H(r){const t=T("fetchTotalRooms");try{R(r);const e=await I(`fetchTotalRooms_${r}`,()=>D({chainId:r}),5e3);return m("fetchTotalRooms",!0),e}catch(e){throw m("fetchTotalRooms",!1),f({component:"groupBuyService",function:"fetchTotalRooms",message:`获取房间总数失败: ${e.message}`,error:e,chainId:r}),e}finally{q(t)}}async function J(r,t){const e=T("fetchRoom");try{R(r),(!t||t<=0)&&p(g.INVALID_ROOM_DATA,"无效的房间ID");const s=await I(`fetchRoom_${r}_${t}`,()=>E({chainId:r,roomId:t}),3e3);return m("fetchRoom",!0),s}catch(s){throw m("fetchRoom",!1),f({component:"groupBuyService",function:"fetchRoom",message:`获取房间信息失败: ${s.message}`,error:s,chainId:r,roomId:t}),s}finally{q(e)}}async function X({signer:r,chainId:t,roomId:e}){const s=T("joinRoom");try{L(r),R(t),(!e||e<=0)&&p(g.INVALID_ROOM_DATA,"无效的房间ID"),w({component:"groupBuyService",function:"joinRoom",message:`开始加入房间: ${e}`,chainId:t,roomId:e});const o=await h(()=>b({signer:r,chainId:t,roomId:e}),"joinRoom");return m("joinRoom",!0),$("groupBuy",3e3),o}catch(o){throw m("joinRoom",!1),f({component:"groupBuyService",function:"joinRoom",message:`加入房间失败: ${o.message}`,error:o,chainId:t,roomId:e}),o}finally{q(s)}}async function Y({signer:r,chainId:t,roomId:e}){const s=T("claimReward");try{L(r),R(t),(!e||e<=0)&&p(g.INVALID_ROOM_DATA,"无效的房间ID"),w({component:"groupBuyService",function:"claimReward",message:`开始领取奖励，房间: ${e}`,chainId:t,roomId:e});const o=await h(()=>B({signer:r,chainId:t,roomId:e}),"claimReward");return m("claimReward",!0),$("groupBuy",3e3),o}catch(o){throw m("claimReward",!1),f({component:"groupBuyService",function:"claimReward",message:`领取奖励失败: ${o.message}`,error:o,chainId:t,roomId:e}),o}finally{q(s)}}export{Y as claimReward,G as createAndLock,J as fetchRoom,H as fetchTotalRooms,X as joinRoom};
