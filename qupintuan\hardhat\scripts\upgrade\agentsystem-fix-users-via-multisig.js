// qupintuan/hardhat/scripts/upgrade/agentsystem-fix-users-via-multisig.js
// 通过多签钱包修复用户等级异常

const { ethers } = require("hardhat");

async function main() {
  console.log("🔧 通过多签钱包修复用户等级异常...\n");

  const MULTISIG_ADDRESS = "******************************************";
  const AGENT_SYSTEM_PROXY = "******************************************";
  
  // 已知问题用户
  const problemUsers = [
    "******************************************", // Level 1 → Level 0
    "******************************************"  // Level 1 → Level 0
  ];

  try {
    const [deployer] = await ethers.getSigners();
    console.log(`🔑 提交账户: ${deployer.address}\n`);

    console.log("📋 修复信息:");
    console.log(`   多签钱包: ${MULTISIG_ADDRESS}`);
    console.log(`   代理合约: ${AGENT_SYSTEM_PROXY}`);
    console.log(`   问题用户数: ${problemUsers.length}`);
    console.log("");

    // 连接合约
    const agentSystem = await ethers.getContractAt("AgentSystem", AGENT_SYSTEM_PROXY);
    const multisig = await ethers.getContractAt("MultiSigWallet", MULTISIG_ADDRESS);

    // 1. 分析问题用户
    console.log("1️⃣ 分析问题用户...");
    
    const fixActions = [];
    
    for (let i = 0; i < problemUsers.length; i++) {
      const userAddress = problemUsers[i];
      console.log(`\n   用户 ${i + 1}: ${userAddress}`);
      
      try {
        // 获取用户信息
        const userInfo = await agentSystem.getUserInfo(userAddress);
        const currentLevel = Number(userInfo[1]);
        const totalPerformance = ethers.formatUnits(userInfo[2], 6);
        
        console.log(`     当前等级: Level ${currentLevel}`);
        console.log(`     团队业绩: ${totalPerformance} USDT`);
        
        // 验证等级
        const validation = await agentSystem.validateUserLevel(userAddress);
        const isValid = validation[0];
        const expectedLevel = Number(validation[2]);
        const smallTeamsPerformance = ethers.formatUnits(validation[3], 6);
        
        console.log(`     等级有效: ${isValid ? '✅' : '❌'}`);
        console.log(`     应有等级: Level ${expectedLevel}`);
        console.log(`     小团队业绩: ${smallTeamsPerformance} USDT`);
        
        if (!isValid && currentLevel > expectedLevel) {
          console.log(`     🔧 需要降级: Level ${currentLevel} → Level ${expectedLevel}`);
          fixActions.push({
            user: userAddress,
            from: currentLevel,
            to: expectedLevel,
            reason: `业绩不足，小团队业绩 ${smallTeamsPerformance} USDT < 5000 USDT`
          });
        } else if (isValid) {
          console.log(`     ✅ 等级正常`);
        }
        
      } catch (error) {
        console.log(`     ❌ 分析失败: ${error.message}`);
      }
    }

    // 2. 准备修复调用数据
    console.log("\n2️⃣ 准备修复调用数据...");
    
    if (fixActions.length === 0) {
      console.log("   ✅ 没有需要修复的用户");
      return;
    }
    
    console.log(`   发现 ${fixActions.length} 个需要修复的用户`);
    
    const setUserLevelInterface = new ethers.Interface([
      "function setUserLevel(address user, uint8 level)"
    ]);
    
    const fixProposals = [];
    
    for (let i = 0; i < fixActions.length; i++) {
      const action = fixActions[i];
      
      const calldata = setUserLevelInterface.encodeFunctionData("setUserLevel", [
        action.user,
        action.to
      ]);
      
      fixProposals.push({
        user: action.user,
        from: action.from,
        to: action.to,
        reason: action.reason,
        calldata: calldata
      });
      
      console.log(`   用户 ${action.user}:`);
      console.log(`     操作: setUserLevel(${action.user}, ${action.to})`);
      console.log(`     调用数据: ${calldata}`);
    }

    // 3. 提交修复提案到多签钱包
    console.log("\n3️⃣ 提交修复提案到多签钱包...");
    
    const submittedProposals = [];
    
    for (let i = 0; i < fixProposals.length; i++) {
      const proposal = fixProposals[i];
      
      console.log(`\n   [${i + 1}/${fixProposals.length}] 提交修复提案...`);
      console.log(`   用户: ${proposal.user}`);
      console.log(`   修复: Level ${proposal.from} → Level ${proposal.to}`);
      console.log(`   原因: ${proposal.reason}`);
      
      try {
        const submitTx = await multisig.submitTransaction(
          AGENT_SYSTEM_PROXY,
          0,
          proposal.calldata
        );
        
        console.log(`   ⏳ 修复提案已提交: ${submitTx.hash}`);
        const receipt = await submitTx.wait();
        console.log(`   ✅ 修复提案确认成功!`);
        
        // 获取提案ID
        const submitEvents = receipt.logs.filter(log => {
          try {
            const parsed = multisig.interface.parseLog(log);
            return parsed.name === 'SubmitTransaction';
          } catch {
            return false;
          }
        });
        
        if (submitEvents.length > 0) {
          const parsed = multisig.interface.parseLog(submitEvents[0]);
          const proposalId = parsed.args.txIndex;
          console.log(`   📝 修复提案ID: ${proposalId}`);
          
          submittedProposals.push({
            ...proposal,
            proposalId: Number(proposalId),
            txHash: submitTx.hash
          });
        }
        
      } catch (error) {
        console.log(`   ❌ 提交修复提案失败: ${error.message}`);
      }
    }

    // 4. 保存修复提案信息
    console.log("\n4️⃣ 保存修复提案信息...");
    
    const fixInfo = {
      timestamp: new Date().toISOString(),
      network: "bscTestnet",
      multisigAddress: MULTISIG_ADDRESS,
      proxyAddress: AGENT_SYSTEM_PROXY,
      totalUsers: problemUsers.length,
      fixActions: fixActions.length,
      submittedProposals: submittedProposals.length,
      proposals: submittedProposals,
      submitter: deployer.address
    };
    
    const fs = require('fs');
    const filename = `agentsystem-user-fix-proposals-${Date.now()}.json`;
    fs.writeFileSync(filename, JSON.stringify(fixInfo, null, 2));
    console.log(`   ✅ 修复提案信息已保存到: ${filename}`);

    // 5. 下一步指南
    console.log("\n5️⃣ 下一步操作指南...");
    console.log("=".repeat(50));
    
    console.log(`\n🔐 多签钱包操作:`);
    console.log(`需要确认并执行以下修复提案:`);
    
    submittedProposals.forEach((proposal, index) => {
      console.log(`\n${index + 1}. 提案ID: ${proposal.proposalId}`);
      console.log(`   用户: ${proposal.user}`);
      console.log(`   修复: Level ${proposal.from} → Level ${proposal.to}`);
      console.log(`   原因: ${proposal.reason}`);
    });
    
    console.log(`\n📋 操作步骤:`);
    console.log(`1. 登录多签钱包管理界面`);
    console.log(`2. 逐个确认并执行上述提案`);
    console.log(`3. 等待所有交易确认`);
    console.log(`4. 运行验证脚本确认修复结果`);
    
    console.log(`\n🔍 验证命令:`);
    console.log(`   npx hardhat run scripts/verify-agentsystem-fix.js --network bscTestnet`);

    console.log("\n🎉 用户修复提案提交完成!");
    console.log(`📊 提交总结:`);
    console.log(`   问题用户数: ${problemUsers.length}`);
    console.log(`   需要修复数: ${fixActions.length}`);
    console.log(`   提案提交数: ${submittedProposals.length}`);

  } catch (error) {
    console.error("❌ 修复提案提交失败:", error.message);
    console.error("详细错误:", error);
    process.exit(1);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
