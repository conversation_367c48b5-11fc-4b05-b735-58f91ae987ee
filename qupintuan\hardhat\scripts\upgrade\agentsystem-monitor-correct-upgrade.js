// qupintuan/hardhat/scripts/upgrade/agentsystem-monitor-correct-upgrade.js
// 监控AgentSystem正确升级进度

const { ethers } = require("hardhat");

async function main() {
  console.log("👀 监控AgentSystem正确升级进度...\n");

  // 升级信息
  const MULTISIG_ADDRESS = "******************************************";
  const AGENT_SYSTEM_PROXY = "******************************************";
  const NEW_IMPLEMENTATION = "******************************************";
  const CORRECT_UPGRADE_PROPOSAL_ID = 41;

  try {
    console.log("📋 监控信息:");
    console.log(`   多签钱包: ${MULTISIG_ADDRESS}`);
    console.log(`   代理合约: ${AGENT_SYSTEM_PROXY}`);
    console.log(`   新实现: ${NEW_IMPLEMENTATION}`);
    console.log(`   升级提案ID: ${CORRECT_UPGRADE_PROPOSAL_ID}`);
    console.log(`   升级函数: upgradeTo(address)`);
    console.log("");

    // 连接合约
    const multisig = await ethers.getContractAt("MultiSigWallet", MULTISIG_ADDRESS);
    const agentSystem = await ethers.getContractAt("AgentSystem", AGENT_SYSTEM_PROXY);

    // 1. 检查多签提案状态
    console.log("1️⃣ 检查多签提案状态...");
    
    try {
      const proposal = await multisig.getTransaction(CORRECT_UPGRADE_PROPOSAL_ID);
      console.log(`   提案ID: ${CORRECT_UPGRADE_PROPOSAL_ID}`);
      console.log(`   目标地址: ${proposal.to}`);
      console.log(`   调用值: ${proposal.value}`);
      console.log(`   已执行: ${proposal.executed ? '✅' : '❌'}`);
      console.log(`   确认数: ${proposal.numConfirmations}`);
      
      const requiredConfirmations = await multisig.numConfirmationsRequired();
      console.log(`   所需确认数: ${requiredConfirmations}`);
      
      if (Number(proposal.numConfirmations) >= Number(requiredConfirmations) && !proposal.executed) {
        console.log(`   ✅ 提案已获得足够确认，可以执行`);
      } else if (proposal.executed) {
        console.log(`   ✅ 提案已执行`);
      } else {
        console.log(`   ⏳ 等待更多确认 (${proposal.numConfirmations}/${requiredConfirmations})`);
      }
      
      // 显示调用数据
      console.log(`   调用数据: ${proposal.data}`);
      
      // 验证调用数据是否正确
      const expectedCalldata = "0x3659cfe60000000000000000000000001895fe4d13dc43b9ffa9b2ee2e020023c16f42c2";
      if (proposal.data.toLowerCase() === expectedCalldata.toLowerCase()) {
        console.log(`   ✅ 调用数据正确（upgradeTo函数）`);
      } else {
        console.log(`   ❌ 调用数据不匹配`);
        console.log(`   期望: ${expectedCalldata}`);
        console.log(`   实际: ${proposal.data}`);
      }
      
    } catch (error) {
      console.log(`   ❌ 获取多签提案状态失败: ${error.message}`);
    }

    // 2. 检查当前实现地址
    console.log("\n2️⃣ 检查当前实现地址...");
    
    try {
      const provider = ethers.provider;
      const implementationSlot = "0x360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc";
      const implStorage = await provider.send("eth_getStorageAt", [AGENT_SYSTEM_PROXY, implementationSlot, "latest"]);
      const currentImplAddress = "0x" + implStorage.slice(-40);
      
      console.log(`   当前实现: ${currentImplAddress}`);
      console.log(`   目标实现: ${NEW_IMPLEMENTATION}`);
      console.log(`   升级完成: ${currentImplAddress.toLowerCase() === NEW_IMPLEMENTATION.toLowerCase() ? '✅' : '❌'}`);
      
      if (currentImplAddress.toLowerCase() === NEW_IMPLEMENTATION.toLowerCase()) {
        console.log(`   🎉 升级已完成！`);
      }
      
    } catch (error) {
      console.log(`   ❌ 检查实现地址失败: ${error.message}`);
    }

    // 3. 测试新功能（如果升级完成）
    console.log("\n3️⃣ 测试新功能...");
    
    try {
      // 测试validateUserLevel函数
      const testUser = "******************************************";
      const validation = await agentSystem.validateUserLevel(testUser);
      
      console.log(`   ✅ validateUserLevel函数可用`);
      console.log(`   测试用户: ${testUser}`);
      console.log(`   等级有效: ${validation[0] ? '✅' : '❌'}`);
      console.log(`   升级类型: ${['未升级', '自动升级', '管理员设置'][Number(validation[1])]}`);
      console.log(`   应有等级: Level ${validation[2]}`);
      console.log(`   小团队业绩: ${ethers.formatUnits(validation[3], 6)} USDT`);
      console.log(`   有效团队数: ${validation[4]}`);
      
      // 测试getUserUpgradeInfo函数
      try {
        const upgradeInfo = await agentSystem.getUserUpgradeInfo(testUser);
        console.log(`   ✅ getUserUpgradeInfo函数可用`);
        console.log(`   用户等级: Level ${upgradeInfo[0]}`);
        console.log(`   升级类型: ${['未升级', '自动升级', '管理员设置'][Number(upgradeInfo[1])]}`);
        console.log(`   升级时间: ${Number(upgradeInfo[2]) > 0 ? new Date(Number(upgradeInfo[2]) * 1000).toLocaleString() : '未升级'}`);
        console.log(`   等级有效: ${upgradeInfo[3] ? '✅' : '❌'}`);
      } catch (error) {
        console.log(`   ❌ getUserUpgradeInfo函数不可用: ${error.message}`);
      }
      
    } catch (error) {
      console.log(`   ❌ 新功能不可用（升级未完成）: ${error.message}`);
    }

    // 4. 检查问题用户状态
    console.log("\n4️⃣ 检查问题用户状态...");
    
    const problemUsers = [
      "******************************************",
      "0x96549975B0704d528E3034f81036e8D728F68084"
    ];
    
    for (const userAddress of problemUsers) {
      try {
        const userInfo = await agentSystem.getUserInfo(userAddress);
        const currentLevel = Number(userInfo[1]);
        
        console.log(`   用户 ${userAddress}:`);
        console.log(`     当前等级: Level ${currentLevel}`);
        
        // 尝试验证等级（如果升级完成）
        try {
          const validation = await agentSystem.validateUserLevel(userAddress);
          const isValid = validation[0];
          const expectedLevel = Number(validation[2]);
          const smallTeamsPerformance = ethers.formatUnits(validation[3], 6);
          
          console.log(`     等级有效: ${isValid ? '✅' : '❌'}`);
          console.log(`     应有等级: Level ${expectedLevel}`);
          console.log(`     小团队业绩: ${smallTeamsPerformance} USDT`);
          
          if (!isValid) {
            console.log(`     🚨 需要修复: Level ${currentLevel} → Level ${expectedLevel}`);
            
            // 分析问题原因
            if (currentLevel > expectedLevel) {
              const requiredPerformance = [0, 5000, 30000, 100000, 500000][currentLevel];
              const deficit = requiredPerformance - parseFloat(smallTeamsPerformance);
              console.log(`     原因: 业绩不足，缺少 ${deficit.toFixed(2)} USDT`);
            }
          } else {
            console.log(`     ✅ 等级正常`);
          }
          
        } catch (error) {
          console.log(`     ⚠️ 验证功能不可用（升级未完成）`);
        }
        
      } catch (error) {
        console.log(`   ❌ 检查用户 ${userAddress} 失败: ${error.message}`);
      }
    }

    // 5. 升级状态总结
    console.log("\n5️⃣ 升级状态总结...");
    console.log("=".repeat(50));
    
    try {
      const proposal = await multisig.getTransaction(CORRECT_UPGRADE_PROPOSAL_ID);
      const provider = ethers.provider;
      const implementationSlot = "0x360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc";
      const implStorage = await provider.send("eth_getStorageAt", [AGENT_SYSTEM_PROXY, implementationSlot, "latest"]);
      const currentImplAddress = "0x" + implStorage.slice(-40);
      const isUpgraded = currentImplAddress.toLowerCase() === NEW_IMPLEMENTATION.toLowerCase();
      
      console.log("\n📊 当前状态:");
      console.log(`   升级提案已执行: ${proposal.executed ? '✅' : '❌'}`);
      console.log(`   合约已升级: ${isUpgraded ? '✅' : '❌'}`);
      console.log(`   新功能可用: ${isUpgraded ? '✅' : '❌'}`);
      console.log(`   升级函数: upgradeTo (正确) ✅`);
      
      if (!proposal.executed) {
        console.log("\n📋 下一步: 执行升级提案");
        console.log(`   1. 登录多签钱包`);
        console.log(`   2. 确认并执行提案ID ${CORRECT_UPGRADE_PROPOSAL_ID}`);
        console.log(`   3. 这次应该会成功，因为使用了正确的upgradeTo函数`);
      } else if (!isUpgraded) {
        console.log("\n⚠️ 异常: 提案已执行但合约未升级");
        console.log(`   需要检查执行日志和错误信息`);
      } else {
        console.log("\n🎉 升级完成!");
        console.log(`   📋 下一步:`);
        console.log(`   1. 验证升级结果:`);
        console.log(`      npx hardhat run scripts/verify-agentsystem-fix.js --network bscTestnet`);
        console.log(`   2. 修复问题用户:`);
        console.log(`      npx hardhat run scripts/fix-all-invalid-users.js --network bscTestnet`);
        
        // 检查修复是否生效
        try {
          const testValidation = await agentSystem.validateUserLevel("******************************************");
          if (!testValidation[0]) {
            console.log(`   3. 🚨 升级逻辑修复已生效，问题用户现在显示为无效等级`);
            console.log(`      这证明bug已修复：Level 0用户现在需要5000 USDT才能升级到Level 1`);
          }
        } catch (error) {
          // 忽略错误
        }
      }
      
    } catch (error) {
      console.log(`❌ 状态总结失败: ${error.message}`);
    }

    // 6. 与之前失败的对比
    console.log("\n6️⃣ 与之前失败的对比...");
    console.log("-".repeat(40));
    
    console.log("\n❌ 之前失败的原因:");
    console.log("   - 使用了upgradeToAndCall函数（0x4f1ef286）");
    console.log("   - 委托调用失败");
    console.log("   - 可能的初始化问题");
    
    console.log("\n✅ 这次的改进:");
    console.log("   - 使用upgradeTo函数（0x3659cfe6）");
    console.log("   - 参考成功的ProductManagement升级模式");
    console.log("   - 直接调用代理合约，不通过Timelock");
    console.log("   - 更简洁的升级流程");

  } catch (error) {
    console.error("❌ 监控失败:", error.message);
    console.error("详细错误:", error);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
