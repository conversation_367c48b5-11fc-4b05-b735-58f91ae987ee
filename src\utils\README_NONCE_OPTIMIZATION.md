# Nonce优化系统使用指南

## 概述

本系统提供了一套完整的Nonce管理和交易优化解决方案，专门解决移动端钱包（特别是TP钱包）的Nonce冲突问题。

## 核心组件

### 1. 通用Nonce管理器 (`universalNonceManager.js`)
- 账户级别的Nonce管控
- 支持所有主流钱包（TP、MetaMask、Trust、imToken等）
- 自动检测和处理Nonce冲突
- 交易队列控制

### 2. 交易队列管理器 (`transactionQueue.js`)
- 多步交易流程管理
- 确保交易按正确顺序执行
- 支持重试和错误恢复
- 业务流程模板化

### 3. Gas优化器 (`gasOptimizer.js`)
- 高优先级Gas策略
- 钱包特定的Gas配置
- 网络拥堵状态感知
- 第一笔交易优先级提升

### 4. 增强错误处理器 (`enhancedErrorHandler.js`)
- 智能错误分析和分类
- 钱包特定的错误信息
- 用户友好的解决建议
- 错误统计和监控

### 5. 优化的交易管理器 (`optimizedTransactionManager.js`)
- 统一的交易管理接口
- 整合所有优化组件
- 交易状态监控
- 系统统计信息

## 使用方法

### 1. 单个交易优化

```javascript
import { optimizedTransactionManager } from '@/utils/optimizedTransactionManager.js';

// 执行单个优化交易
const result = await optimizedTransactionManager.executeSingleTransaction({
  userAddress: '0x...',
  transactionFn: async ({ nonce, gasConfig }) => {
    // 你的交易逻辑
    return await walletClient.writeContract({
      address: contractAddress,
      abi: contractABI,
      functionName: 'yourFunction',
      args: [arg1, arg2],
      nonce,
      ...gasConfig
    });
  },
  operationName: '授权代币',
  isFirstTransaction: true, // 第一笔交易使用高优先级Gas
  gasStrategy: 'fast',
  onProgress: (progress) => {
    console.log('交易进度:', progress);
  },
  onSuccess: (result) => {
    console.log('交易成功:', result);
  },
  onError: (error) => {
    console.error('交易失败:', error);
  }
});
```

### 2. 业务流程优化

```javascript
import { optimizedCreateGroupBuy } from '@/services/optimizedBusinessFlows.js';

// 优化的创建拼团流程（3步交易）
const result = await optimizedCreateGroupBuy(signer, tierAmountStr);

// 或者使用通用的业务流程执行器
const result = await optimizedTransactionManager.executeBusinessFlow({
  flowType: 'CREATE_GROUP_BUY',
  userAddress: signer.account.address,
  stepFunctions: [
    // 步骤1：创建房间
    async ({ nonce, gasConfig }) => { /* ... */ },
    // 步骤2：授权QPT
    async ({ nonce, gasConfig }) => { /* ... */ },
    // 步骤3：锁定QPT
    async ({ nonce, gasConfig }) => { /* ... */ }
  ],
  onStepComplete: (step, result) => {
    console.log(`步骤完成: ${step.name}`, result);
  }
});
```

### 3. 现有代码集成

```javascript
import { createOptimizedSendTx } from '@/utils/transactionHandler.js';

// 创建优化的sendTx函数
const optimizedSendTx = createOptimizedSendTx(
  userAddress, 
  true, // 是否是第一笔交易
  '创建拼团房间' // 操作名称
);

// 使用优化的sendTx替换原有的sendTx
const result = await optimizedSendTx(
  () => createRoomWithQPTVerification({ chainId: 97, tier: tierNum, signer }),
  {
    onTxHash: (hash) => console.log('交易哈希:', hash),
    onReceipt: (receipt) => console.log('交易收据:', receipt),
    onError: (error) => console.error('交易错误:', error)
  }
);
```

## 支持的业务流程

### 1. 创建拼团房间 (CREATE_GROUP_BUY)
- 步骤1：创建拼团房间
- 步骤2：授权QPT代币
- 步骤3：锁定QPT代币

### 2. 参与拼团房间 (JOIN_GROUP_BUY)
- 步骤1：授权USDT代币
- 步骤2：加入拼团房间

### 3. 质押激活节点 (STAKE_NODE)
- 步骤1：授权QPT代币
- 步骤2：执行质押操作

### 4. QPT回购房间 (QPT_BUYBACK)
- 步骤1：授权QPT代币
- 步骤2：参与回购房间

## 钱包兼容性

系统自动检测钱包类型并应用相应的优化策略：

- **TP钱包**: Gas倍数 1.3，优先费用倍数 1.5
- **Trust钱包**: Gas倍数 1.25，优先费用倍数 1.4
- **imToken**: Gas倍数 1.25，优先费用倍数 1.4
- **MetaMask**: Gas倍数 1.1，优先费用倍数 1.2
- **未知移动端钱包**: Gas倍数 1.3，优先费用倍数 1.5
- **桌面端钱包**: Gas倍数 1.2，优先费用倍数 1.3

## 错误处理

系统提供智能错误处理和用户友好的解决建议：

### Nonce错误
- 自动清理Nonce缓存
- 建议重启钱包应用
- 提供等待时间建议

### 网络错误
- 自动重试机制
- 网络连接检查建议
- 切换网络建议

### Gas错误
- 自动增加Gas费用
- 网络拥堵状态提示
- Gas费用优化建议

## 监控和调试

### 获取用户交易状态
```javascript
const status = optimizedTransactionManager.getUserTransactionStatus(userAddress);
console.log('用户交易状态:', status);
```

### 获取系统统计
```javascript
const stats = optimizedTransactionManager.getSystemStats();
console.log('系统统计:', stats);
```

### 清理用户缓存
```javascript
optimizedTransactionManager.clearUserCache(userAddress);
```

## 最佳实践

1. **第一笔交易优先级**: 总是将业务流程的第一笔交易标记为高优先级
2. **错误处理**: 实现完整的错误处理回调，提供用户友好的错误信息
3. **进度反馈**: 使用进度回调向用户显示交易状态
4. **缓存管理**: 定期清理用户缓存，避免内存泄漏
5. **监控统计**: 定期检查系统统计，监控系统健康状态

## 故障排除

### 常见问题

1. **Nonce仍然冲突**
   - 检查是否正确传入userAddress
   - 确认useOptimizedNonce参数为true
   - 尝试清理用户缓存

2. **Gas费用过高**
   - 检查gasStrategy设置
   - 调整钱包特定的Gas倍数
   - 监控网络拥堵状态

3. **交易失败率高**
   - 检查网络连接稳定性
   - 增加重试次数
   - 检查合约参数正确性

### 调试模式

在开发环境中，系统会输出详细的调试信息，包括：
- Nonce计算过程
- Gas配置优化
- 错误分析结果
- 交易执行状态

## 更新日志

### v1.0.0
- 初始版本发布
- 支持所有主流钱包
- 完整的业务流程优化
- 智能错误处理和重试机制
