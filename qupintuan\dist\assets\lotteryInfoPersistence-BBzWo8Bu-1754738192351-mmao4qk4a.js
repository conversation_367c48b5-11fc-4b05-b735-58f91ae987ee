function i(t,e){try{const r=`lottery_${t}`,a={...e,savedAt:Date.now(),version:"1.0"};return localStorage.setItem(r,JSON.stringify(a)),y(t,a),!0}catch{return!1}}function l(t){try{const e=`lottery_${t}`,r=localStorage.getItem(e);return r?JSON.parse(r):u(t)}catch{return null}}function y(t,e){try{const r="all_lottery_info",a=JSON.parse(localStorage.getItem(r)||"{}");a[t]=e,localStorage.setItem(r,JSON.stringify(a))}catch{}}function u(t){try{const r=JSON.parse(localStorage.getItem("all_lottery_info")||"{}");return r[t]?r[t]:null}catch{return null}}function f(t){const e=l(t);return!!(e&&e.txHash&&e.winner)}function p(t){try{const e=`lottery_${t}`;localStorage.removeItem(e);const r="all_lottery_info",a=JSON.parse(localStorage.getItem(r)||"{}");return a[t]&&(delete a[t],localStorage.setItem(r,JSON.stringify(a))),!0}catch{return!1}}function c(t){if(!t||!t.id)return t;try{const e=l(t.id);if(e){let r=0;if(t.createTime)r=Date.now()-new Date(t.createTime).getTime();else if(t.createdAt){const o=typeof t.createdAt=="number"?t.createdAt*1e3:new Date(t.createdAt).getTime();r=Date.now()-o}if(r<300*1e3&&(!t.participants||t.participants.length===0)||!t.participants||t.participants.length<8||t.roomType==="qpt-buyback"&&!t.locked)return t;const n={...t};return!n.readyForWinner&&e.txHash&&(n.readyForWinner=!0),!n.lotteryTxHash&&e.txHash&&(n.lotteryTxHash=e.txHash),!n.lotteryTimestamp&&e.timestamp&&(n.lotteryTimestamp=e.timestamp),!n.calculatedWinner&&e.winner&&(n.calculatedWinner=e.winner),e.txHash&&e.winner&&(n.lotteryInfo={readyForWinner:!0,winner:e.winner,lotteryTxHash:e.txHash,lotteryTimestamp:e.timestamp||0,winnerIndex:e.winnerIndex||0,txHash:e.txHash}),n._lotteryInfoSource="local_storage",n._lotteryInfoEnhanced=!0,n}return t}catch{return t}}function S(t){return Array.isArray(t)?t.map(e=>c(e)):t}function h(t=720*60*60*1e3){try{const e="all_lottery_info",r=JSON.parse(localStorage.getItem(e)||"{}"),a=Date.now();let n=0;return Object.keys(r).forEach(o=>{const s=r[o];s.savedAt&&a-s.savedAt>t&&(delete r[o],localStorage.removeItem(`lottery_${o}`),n++)}),n>0&&(localStorage.setItem(e,JSON.stringify(r)),console.log(`🧹 已清理 ${n} 个过期的开奖信息`)),n}catch(e){return console.error("清理过期开奖信息失败:",e),0}}function m(){try{const e=JSON.parse(localStorage.getItem("all_lottery_info")||"{}"),r={totalRooms:Object.keys(e).length,roomIds:Object.keys(e).map(n=>parseInt(n)),oldestSaveTime:null,newestSaveTime:null},a=Object.values(e).map(n=>n.savedAt).filter(n=>n);return a.length>0&&(r.oldestSaveTime=Math.min(...a),r.newestSaveTime=Math.max(...a)),r}catch(t){return console.error("获取开奖信息统计失败:",t),{totalRooms:0,roomIds:[],oldestSaveTime:null,newestSaveTime:null}}}function g(){}function d(t){try{const e=document.cookie.split(";"),r=`lottery_cookie_${t}=`;let a=null;for(let n of e)if(n=n.trim(),n.startsWith(r)){const o=n.substring(r.length);a=JSON.parse(atob(o));break}console.log("🍪 cookie:",a||"无数据")}catch(e){console.log("❌ cookie 读取失败:",e)}try{const e=window._lotteryHashData?.[t];console.log("🔗 URL hash:",e||"无数据")}catch(e){console.log("❌ URL hash 读取失败:",e)}}typeof window<"u"&&(window.lotteryInfoDebug={saveLotteryInfo:i,getLotteryInfo:l,hasLotteryInfo:f,enhanceRoomWithLotteryInfo:c,cleanupExpiredLotteryInfo:h,getLotteryInfoStats:m,debugShowAllLotteryInfo:g,debugCheckRoomLotteryInfo:d});export{h as cleanupExpiredLotteryInfo,p as clearLotteryInfo,d as debugCheckRoomLotteryInfo,g as debugShowAllLotteryInfo,c as enhanceRoomWithLotteryInfo,S as enhanceRoomsWithLotteryInfo,l as getLotteryInfo,m as getLotteryInfoStats,f as hasLotteryInfo,i as saveLotteryInfo};
