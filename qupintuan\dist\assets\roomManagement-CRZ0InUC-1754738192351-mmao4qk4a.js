const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index-DmsQauDE-*************-rjh9jemju.js","assets/vendor-Caz4khA--*************-mmao4qk4a.js","assets/web3-DJUNf-KT-*************-mmao4qk4a.js","assets/index-Dcug_PHz-*************-gaytee33w.css"])))=>i.map(i=>d[i]);
import{_ as n}from"./vendor-Caz4khA--*************-mmao4qk4a.js";async function q({chainId:w,tier:_,signer:u}){try{if(!u)throw new Error("未检测到钱包签名者");const e=u.account?.address;if(!e)throw new Error("无法获取用户地址");const{getContractAddress:s}=await n(async()=>{const{getContractAddress:o}=await import("./index-DmsQauDE-*************-rjh9jemju.js").then(c=>c.j);return{getContractAddress:o}},__vite__mapDeps([0,1,2,3])),{ABIS:g}=await n(async()=>{const{ABIS:o}=await import("./index-DmsQauDE-*************-rjh9jemju.js").then(c=>c.k);return{ABIS:o}},__vite__mapDeps([0,1,2,3])),{createPublicClient:p,http:b}=await n(async()=>{const{createPublicClient:o,http:c}=await import("./web3-DJUNf-KT-*************-mmao4qk4a.js").then(m=>m.x);return{createPublicClient:o,http:c}},__vite__mapDeps([2,1])),{bscTestnet:f}=await n(async()=>{const{bscTestnet:o}=await import("./web3-DJUNf-KT-*************-mmao4qk4a.js").then(c=>c.A);return{bscTestnet:o}},__vite__mapDeps([2,1])),T=s(w,"QPTLocker"),h=s(w,"QPTToken"),R=p({chain:f,transport:b()}),E=Number(_),i=BigInt(E*1e6),A=(await R.readContract({address:T,abi:g.QPTLocker,functionName:"amountMappings",args:[i]}))[1],C=Number(A)/10**18,y=await R.readContract({address:h,abi:g.QPTToken,functionName:"balanceOf",args:[e]}),l=BigInt(y),a=Number(l)/1e18;if(l<A)throw new Error(`QPT余额不足。需要: ${C} QPT，当前: ${a} QPT`);const r=await u.writeContract({address:h,abi:g.QPTToken,functionName:"approve",args:[T,A]});return{success:!0,receipt:await R.waitForTransactionReceipt({hash:r,timeout:6e4}),txHash:r,approvedAmount:C,userBalance:a,message:`第2步完成：授权 ${C} QPT`}}catch(e){throw e}}async function j({chainId:w,tier:_,roomId:u,signer:e}){try{if(!e)throw new Error("未检测到钱包签名者");const s=e.account?.address;if(!s)throw new Error("无法获取用户地址");const{getContractAddress:g}=await n(async()=>{const{getContractAddress:c}=await import("./index-DmsQauDE-*************-rjh9jemju.js").then(m=>m.j);return{getContractAddress:c}},__vite__mapDeps([0,1,2,3])),{ABIS:p}=await n(async()=>{const{ABIS:c}=await import("./index-DmsQauDE-*************-rjh9jemju.js").then(m=>m.k);return{ABIS:c}},__vite__mapDeps([0,1,2,3])),{createPublicClient:b,http:f}=await n(async()=>{const{createPublicClient:c,http:m}=await import("./web3-DJUNf-KT-*************-mmao4qk4a.js").then(P=>P.x);return{createPublicClient:c,http:m}},__vite__mapDeps([2,1])),{bscTestnet:T}=await n(async()=>{const{bscTestnet:c}=await import("./web3-DJUNf-KT-*************-mmao4qk4a.js").then(m=>m.A);return{bscTestnet:c}},__vite__mapDeps([2,1])),h=g(w,"QPTLocker"),R=g(w,"QPTToken"),E=b({chain:T,transport:f()}),i=Number(_),d=BigInt(i*1e6),C=(await E.readContract({address:h,abi:p.QPTLocker,functionName:"amountMappings",args:[d]}))[1],y=Number(C)/10**18,l=await E.readContract({address:R,abi:p.QPTToken,functionName:"allowance",args:[s,h]}),a=BigInt(l),r=Number(a)/1e18;if(a<C)throw new Error(`QPT授权不足。需要: ${y} QPT，当前授权: ${r} QPT。请先完成第2步授权操作。`);const t=await e.writeContract({address:h,abi:p.QPTLocker,functionName:"lockForRoom",args:[BigInt(u),d]});return{success:!0,receipt:await E.waitForTransactionReceipt({hash:t,timeout:6e4}),txHash:t,roomId:u,lockedAmount:y,message:`第3步完成：为房间 #${u} 锁仓 ${y} QPT`}}catch(s){throw s}}async function F({chainId:w,tier:_,signer:u}){try{if(console.log("🚀 [createRoomWithQPTVerification] 发起人第1步：创建拼团房间:",{chainId:w,tier:_,signerAddress:u?.account?.address}),!u)throw new Error("未检测到钱包签名者，请安装 MetaMask 或其他以太坊兼容钱包");const{getContractAddress:e}=await n(async()=>{const{getContractAddress:t}=await import("./index-DmsQauDE-*************-rjh9jemju.js").then(o=>o.j);return{getContractAddress:t}},__vite__mapDeps([0,1,2,3])),s=e(w,"GroupBuyRoom"),{ABIS:g}=await n(async()=>{const{ABIS:t}=await import("./index-DmsQauDE-*************-rjh9jemju.js").then(o=>o.k);return{ABIS:t}},__vite__mapDeps([0,1,2,3])),p=g.GroupBuyRoom;if(console.log("📋 [apiCreateRoom] 合约信息:",{contractAddress:s,hasABI:!!p,abiLength:p?.length,chainId:w,tier:_.toString()}),!s||s==="0x0000000000000000000000000000000000000000")throw new Error(`无效的合约地址: ${s}，链ID: ${w}`);if(!p||p.length===0)throw new Error("GroupBuyRoom ABI 未找到或为空");const b=p.find(t=>t.type==="function"&&t.name==="createRoom");if(!b)throw new Error("createRoom 函数在 ABI 中不存在");const f=p.find(t=>t.type==="event"&&t.name==="RoomCreated");if(!f)throw new Error("RoomCreated 事件在 ABI 中不存在");console.log("✅ [apiCreateRoom] ABI 验证通过:",{hasCreateRoomFunction:!!b,hasRoomCreatedEvent:!!f});const T=u.account.address;console.log("👤 [createRoomWithQPTVerification] 签名者地址:",T);const{createPublicClient:h,http:R}=await n(async()=>{const{createPublicClient:t,http:o}=await import("./web3-DJUNf-KT-*************-mmao4qk4a.js").then(c=>c.x);return{createPublicClient:t,http:o}},__vite__mapDeps([2,1])),{bscTestnet:E}=await n(async()=>{const{bscTestnet:t}=await import("./web3-DJUNf-KT-*************-mmao4qk4a.js").then(o=>o.A);return{bscTestnet:t}},__vite__mapDeps([2,1])),i=h({chain:E,transport:R()}),d=Number(_);console.log("🔢 [createRoomWithQPTVerification] 档位信息:",{originalTier:_.toString(),tierNum:d,tierType:typeof _}),console.log("🏠 [createRoomWithQPTVerification] 第1步：创建拼团房间（不验证QPT锁仓）");const A=[30,50,100,200,500,1e3];if(!A.includes(d))throw new Error(`不支持的档位: ${d}。支持的档位: ${A.join(", ")}`);let C;try{const t=`TIER_${d}`;console.log("📞 [apiCreateRoom] 调用合约函数:",t),C=await i.readContract({address:s,abi:p,functionName:t}),console.log("✅ [apiCreateRoom] 获取合约档位值成功:",{tierFunctionName:t,contractTierValue:C.toString()})}catch(t){throw console.error("❌ [apiCreateRoom] 获取合约档位值失败:",t.message),new Error(`不支持的档位: ${d}。支持的档位: 30, 50, 100, 200, 500, 1000。错误详情: ${t.message}`)}let y;try{y=await i.estimateContractGas({address:s,abi:p,functionName:"createRoom",args:[C],account:T}),console.log("⛽ [apiCreateRoom] Gas 估算成功:",y.toString())}catch(t){throw console.error("❌ [apiCreateRoom] Gas 估算失败:",t.message),console.error("可能的原因："),console.error("1. 用户 USDT 余额不足"),console.error("2. 用户未授权合约使用 USDT"),console.error("3. 合约参数错误"),console.error("4. 合约逻辑错误"),new Error(`Gas 估算失败，交易可能会失败。请检查：1) USDT余额 2) USDT授权 3) 网络连接。详细错误: ${t.message}`)}console.log("📤 [apiCreateRoom] 发送交易:",{contractAddress:s,functionName:"createRoom",args:[C.toString()],estimatedGas:y.toString(),signerAddress:T,originalTier:_.toString(),tierNum:d});const l=await u.writeContract({address:s,abi:p,functionName:"createRoom",args:[C],gas:y*120n/100n});console.log("✅ [apiCreateRoom] 交易已发送:",l);const a=await i.waitForTransactionReceipt({hash:l,timeout:6e4});if(console.log("📄 [apiCreateRoom] 交易收据:",{hash:a.hash,status:a.status,logsCount:a.logs?.length||0,blockNumber:a.blockNumber,gasUsed:a.gasUsed?.toString()}),a.status==="reverted")throw console.error("❌ [apiCreateRoom] 交易被回滚，可能的原因:"),console.error("1. 用户余额不足（USDT 或 Gas）"),console.error("2. 合约函数调用失败（参数错误、权限问题等）"),console.error("3. 合约内部逻辑错误"),console.error("4. 网络拥堵或Gas价格过低"),new Error(`交易被回滚，请检查：1) USDT余额是否足够 2) Gas费用是否足够 3) 网络是否正常。交易哈希: ${a.transactionHash||a.hash}`);let r=null;console.log("🔍 [apiCreateRoom] 开始解析事件日志，日志数量:",a.logs.length),console.log("🔍 [apiCreateRoom] 合约地址:",s);for(let t=0;t<a.logs.length;t++){const o=a.logs[t];if(console.log(`📝 [apiCreateRoom] 解析日志 ${t+1}:`,{address:o.address,contractAddress:s,isFromContract:o.address.toLowerCase()===s.toLowerCase(),topics:o.topics,topicsLength:o.topics?.length||0,dataLength:o.data?.length||0,data:o.data}),o.address.toLowerCase()!==s.toLowerCase()){console.log(`⏭️ [apiCreateRoom] 跳过非目标合约的日志 ${t+1}`);continue}try{const{decodeEventLog:c}=await n(async()=>{const{decodeEventLog:P}=await import("./web3-DJUNf-KT-*************-mmao4qk4a.js").then(D=>D.x);return{decodeEventLog:P}},__vite__mapDeps([2,1])),m=c({abi:p,data:o.data,topics:o.topics});if(console.log(`✅ [apiCreateRoom] 成功解码事件 ${t+1}:`,{eventName:m.eventName,args:m.args,argsKeys:Object.keys(m.args||{}),roomIdRaw:m.args?.roomId,roomIdType:typeof m.args?.roomId}),m.eventName==="RoomCreated"){const P=m.args.roomId;r=Number(P),console.log("🎯 [apiCreateRoom] 找到 RoomCreated 事件，房间ID:",{originalValue:P,originalType:typeof P,convertedValue:r,convertedType:typeof r,isValid:r>=0,isNumber:!isNaN(r)});break}}catch(c){console.warn(`⚠️ [apiCreateRoom] 无法解码日志 ${t+1}:`,{error:c.message,logAddress:o.address,topicsLength:o.topics?.length||0,dataLength:o.data?.length||0});continue}}if(r==null)throw console.error("❌ [apiCreateRoom] 无法提取房间ID，详细信息:",{receiptStatus:a.status,logsCount:a.logs.length,contractAddress:s,txHash:a.transactionHash,blockNumber:a.blockNumber,gasUsed:a.gasUsed?.toString(),roomIdValue:r,roomIdType:typeof r,logs:a.logs.map((t,o)=>({index:o,address:t.address,topicsCount:t.topics?.length||0,dataLength:t.data?.length||0}))}),new Error(`无法从交易收据中提取房间ID。交易哈希: ${a.transactionHash}，日志数量: ${a.logs.length}`);console.log("✅ [createRoomWithQPTVerification] 第1步完成 - 拼团房间创建成功:",{roomId:r,receiptHash:a.hash,receiptStatus:a.status,logsCount:a.logs?.length||0,blockNumber:a.blockNumber,timestamp:new Date().toISOString(),message:`第1步完成：创建拼团房间 #${r}`});try{console.log("🔍 [apiCreateRoom] 验证新创建的房间...");const t=await i.readContract({address:s,abi:p,functionName:"getRoom",args:[BigInt(r)]});console.log("✅ [apiCreateRoom] 房间验证成功:",{roomId:r,creator:t[0],tier:t[1]?.toString(),createTime:t[3]?.toString()})}catch(t){console.warn("⚠️ [apiCreateRoom] 房间验证失败:",t.message)}return{roomId:r,receipt:a}}catch(e){throw console.error("创建房间失败:",e),e}}async function H({chainId:w,roomId:_,signer:u}){try{if(!u)throw new Error("未检测到钱包签名者");const e=u.account?.address;if(!e)throw new Error("无法获取用户地址");const{getContractAddress:s}=await n(async()=>{const{getContractAddress:o}=await import("./index-DmsQauDE-*************-rjh9jemju.js").then(c=>c.j);return{getContractAddress:o}},__vite__mapDeps([0,1,2,3])),{ABIS:g}=await n(async()=>{const{ABIS:o}=await import("./index-DmsQauDE-*************-rjh9jemju.js").then(c=>c.k);return{ABIS:o}},__vite__mapDeps([0,1,2,3])),{createPublicClient:p,http:b}=await n(async()=>{const{createPublicClient:o,http:c}=await import("./web3-DJUNf-KT-*************-mmao4qk4a.js").then(m=>m.x);return{createPublicClient:o,http:c}},__vite__mapDeps([2,1])),{bscTestnet:f}=await n(async()=>{const{bscTestnet:o}=await import("./web3-DJUNf-KT-*************-mmao4qk4a.js").then(c=>c.A);return{bscTestnet:o}},__vite__mapDeps([2,1])),T=s(w,"GroupBuyRoom"),h=s(w,"USDT"),R=p({chain:f,transport:b()}),E=await R.readContract({address:T,abi:g.GroupBuyRoom,functionName:"getRoom",args:[BigInt(_)]}),[i,d]=E,A=Number(d)/1e6,C=BigInt(d),y=await R.readContract({address:h,abi:g.USDT,functionName:"balanceOf",args:[e]}),l=BigInt(y),a=Number(l)/1e6;if(l<C)throw new Error(`USDT余额不足。需要: ${A} USDT，当前: ${a} USDT`);const r=await u.writeContract({address:h,abi:g.USDT,functionName:"approve",args:[T,C]});return{success:!0,receipt:await R.waitForTransactionReceipt({hash:r,timeout:6e4}),txHash:r,approvedAmount:A,userBalance:a,message:`第1步完成：重新授权 ${A} USDT`}}catch(e){throw e}}async function M({chainId:w,roomId:_,signer:u}){try{if(!u)throw new Error("未检测到钱包签名者");const e=u.account?.address;if(!e)throw new Error("无法获取用户地址");const{getContractAddress:s}=await n(async()=>{const{getContractAddress:I}=await import("./index-DmsQauDE-*************-rjh9jemju.js").then(B=>B.j);return{getContractAddress:I}},__vite__mapDeps([0,1,2,3])),{ABIS:g}=await n(async()=>{const{ABIS:I}=await import("./index-DmsQauDE-*************-rjh9jemju.js").then(B=>B.k);return{ABIS:I}},__vite__mapDeps([0,1,2,3])),{createPublicClient:p,http:b}=await n(async()=>{const{createPublicClient:I,http:B}=await import("./web3-DJUNf-KT-*************-mmao4qk4a.js").then(Q=>Q.x);return{createPublicClient:I,http:B}},__vite__mapDeps([2,1])),{bscTestnet:f}=await n(async()=>{const{bscTestnet:I}=await import("./web3-DJUNf-KT-*************-mmao4qk4a.js").then(B=>B.A);return{bscTestnet:I}},__vite__mapDeps([2,1])),T=s(w,"GroupBuyRoom"),h=s(w,"USDT"),R=g.GroupBuyRoom,E=p({chain:f,transport:b()}),i=await E.readContract({address:T,abi:R,functionName:"getRoom",args:[BigInt(_)]}),[d,A,C,y,l,a]=i,r=Number(A)/1e6,t=BigInt(A);if(l)throw new Error("房间已关闭，无法参与");if(C.length>=8)throw new Error("房间已满员，无法参与");if(C.some(I=>I.toLowerCase()===e.toLowerCase()))throw new Error("您已参与此房间");const c=Math.floor(Date.now()/1e3),m=Number(i[12]);if(c>m)throw new Error("房间已过期，无法参与");const[P,D]=await Promise.all([E.readContract({address:h,abi:g.USDT,functionName:"balanceOf",args:[e]}),E.readContract({address:h,abi:g.USDT,functionName:"allowance",args:[e,T]})]),v=BigInt(P),L=BigInt(D),S=Number(v)/1e6,k=Number(L)/1e6;if(v<t)throw new Error(`USDT余额不足。需要: ${r} USDT，当前: ${S} USDT`);if(L<t)throw new Error(`USDT授权不足。需要: ${r} USDT，当前授权: ${k} USDT。请先完成第1步授权操作。`);const N=await u.writeContract({address:T,abi:R,functionName:"joinRoom",args:[BigInt(_)]}),$=await E.waitForTransactionReceipt({hash:N,timeout:6e4}),U=await E.readContract({address:h,abi:g.USDT,functionName:"balanceOf",args:[e]}),V=Number(U)/1e6,O=S-V;return{receipt:$,txHash:N,roomId:_,paymentVerification:{expectedAmount:r,actualPaid:O,remainingBalance:V,paymentVerified:Math.abs(O-r)<1e-6}}}catch(e){throw e}}async function G({chainId:w,roomId:_}){try{const{getContractAddress:u}=await n(async()=>{const{getContractAddress:a}=await import("./index-DmsQauDE-*************-rjh9jemju.js").then(r=>r.j);return{getContractAddress:a}},__vite__mapDeps([0,1,2,3])),{ABIS:e}=await n(async()=>{const{ABIS:a}=await import("./index-DmsQauDE-*************-rjh9jemju.js").then(r=>r.k);return{ABIS:a}},__vite__mapDeps([0,1,2,3])),{createPublicClient:s,http:g}=await n(async()=>{const{createPublicClient:a,http:r}=await import("./web3-DJUNf-KT-*************-mmao4qk4a.js").then(t=>t.x);return{createPublicClient:a,http:r}},__vite__mapDeps([2,1])),{bscTestnet:p}=await n(async()=>{const{bscTestnet:a}=await import("./web3-DJUNf-KT-*************-mmao4qk4a.js").then(r=>r.A);return{bscTestnet:a}},__vite__mapDeps([2,1])),b=u(w,"GroupBuyRoom"),f=u(w,"QPTLocker"),T=s({chain:p,transport:g()}),h=await T.readContract({address:b,abi:e.GroupBuyRoom,functionName:"getRoom",args:[BigInt(_)]}),[R]=h,E=await T.readContract({address:f,abi:e.QPTLocker,functionName:"getRoomInfo",args:[BigInt(_)]}),[i,d,A,C,y]=E,l=i.toLowerCase()===R.toLowerCase();return{isValid:l,creator:R,lockerCreator:i,amount:Number(d)/1e18,unlockTime:Number(A),isSuccess:C,isClaimed:y,message:l?"QPT锁仓验证通过":"发起人未锁仓QPT"}}catch(u){return{isValid:!1,message:`QPT锁仓验证失败: ${u.message}`}}}async function x({chainId:w,roomId:_,userAddress:u}){try{const{getContractAddress:e}=await n(async()=>{const{getContractAddress:t}=await import("./index-DmsQauDE-*************-rjh9jemju.js").then(o=>o.j);return{getContractAddress:t}},__vite__mapDeps([0,1,2,3])),{ABIS:s}=await n(async()=>{const{ABIS:t}=await import("./index-DmsQauDE-*************-rjh9jemju.js").then(o=>o.k);return{ABIS:t}},__vite__mapDeps([0,1,2,3])),{createPublicClient:g,http:p}=await n(async()=>{const{createPublicClient:t,http:o}=await import("./web3-DJUNf-KT-*************-mmao4qk4a.js").then(c=>c.x);return{createPublicClient:t,http:o}},__vite__mapDeps([2,1])),{bscTestnet:b}=await n(async()=>{const{bscTestnet:t}=await import("./web3-DJUNf-KT-*************-mmao4qk4a.js").then(o=>o.A);return{bscTestnet:t}},__vite__mapDeps([2,1])),f=e(w,"GroupBuyRoom"),T=e(w,"USDT"),h=g({chain:b,transport:p()}),R=await h.readContract({address:f,abi:s.GroupBuyRoom,functionName:"getRoom",args:[BigInt(_)]}),[E,i]=R,d=Number(i)/1e6,A=BigInt(i),[C,y]=await Promise.all([h.readContract({address:T,abi:s.USDT,functionName:"balanceOf",args:[u]}),h.readContract({address:T,abi:s.USDT,functionName:"allowance",args:[u,f]})]),l=BigInt(C),a=BigInt(y),r={tierAmount:d,balance:Number(l)/1e6,allowance:Number(a)/1e6,hasEnoughBalance:l>=A,hasEnoughAllowance:a>=A,needsApproval:a<A,errors:[]};return r.hasEnoughBalance||r.errors.push(`USDT余额不足 (需要: ${d}, 当前: ${r.balance})`),r.hasEnoughAllowance||r.errors.push(`需要授权USDT (需要: ${d}, 当前授权: ${r.allowance})`),r.canProceed=r.hasEnoughBalance&&r.hasEnoughAllowance,r}catch(e){return{canProceed:!1,errors:[`USDT验证失败: ${e.message}`]}}}async function W({chainId:w,roomId:_,userAddress:u}){try{const{getContractAddress:e}=await n(async()=>{const{getContractAddress:m}=await import("./index-DmsQauDE-*************-rjh9jemju.js").then(P=>P.j);return{getContractAddress:m}},__vite__mapDeps([0,1,2,3])),{ABIS:s}=await n(async()=>{const{ABIS:m}=await import("./index-DmsQauDE-*************-rjh9jemju.js").then(P=>P.k);return{ABIS:m}},__vite__mapDeps([0,1,2,3])),{createPublicClient:g,http:p}=await n(async()=>{const{createPublicClient:m,http:P}=await import("./web3-DJUNf-KT-*************-mmao4qk4a.js").then(D=>D.x);return{createPublicClient:m,http:P}},__vite__mapDeps([2,1])),{bscTestnet:b}=await n(async()=>{const{bscTestnet:m}=await import("./web3-DJUNf-KT-*************-mmao4qk4a.js").then(P=>P.A);return{bscTestnet:m}},__vite__mapDeps([2,1])),f=e(w,"GroupBuyRoom"),h=await g({chain:b,transport:p()}).readContract({address:f,abi:s.GroupBuyRoom,functionName:"getRoom",args:[BigInt(_)]}),[R,E,i,d,A,C]=h,y=Number(E)/1e6,l={canJoin:!0,errors:[],warnings:[],roomInfo:{creator:R,tierAmount:y,participantsCount:i.length,isClosed:A,isSuccessful:C},steps:{needsApproval:!1,canJoinRoom:!1}};A&&(l.canJoin=!1,l.errors.push("房间已关闭")),i.length>=8&&(l.canJoin=!1,l.errors.push("房间已满员")),i.some(m=>m.toLowerCase()===u.toLowerCase())&&(l.canJoin=!1,l.errors.push("您已参与此房间"));const r=Math.floor(Date.now()/1e3),t=Number(h[12]);r>t&&(l.canJoin=!1,l.errors.push("房间已过期")),(await G({chainId:w,roomId:_})).isValid||(l.canJoin=!1,l.errors.push("发起人未锁仓QPT"));const c=await x({chainId:w,roomId:_,userAddress:u});return l.usdtInfo=c,c.hasEnoughBalance||(l.canJoin=!1,l.errors.push(...c.errors.filter(m=>m.includes("余额不足")))),l.steps.needsApproval=c.needsApproval,l.steps.canJoinRoom=l.canJoin&&!c.needsApproval,l}catch(e){return{canJoin:!1,errors:[`验证过程出错: ${e.message}`],warnings:[],steps:{needsApproval:!1,canJoinRoom:!1}}}}async function K({chainId:w,roomId:_,signer:u}){try{if(!u)throw new Error("签名者未定义，请确保钱包已连接");const{getContractAddress:e}=await n(async()=>{const{getContractAddress:i}=await import("./index-DmsQauDE-*************-rjh9jemju.js").then(d=>d.j);return{getContractAddress:i}},__vite__mapDeps([0,1,2,3])),s=e(w,"GroupBuyRoom"),{ABIS:g}=await n(async()=>{const{ABIS:i}=await import("./index-DmsQauDE-*************-rjh9jemju.js").then(d=>d.k);return{ABIS:i}},__vite__mapDeps([0,1,2,3])),p=g.GroupBuyRoom,b=await u.writeContract({address:s,abi:p,functionName:"closeRoom",args:[BigInt(_)]}),{createPublicClient:f,http:T}=await n(async()=>{const{createPublicClient:i,http:d}=await import("./web3-DJUNf-KT-*************-mmao4qk4a.js").then(A=>A.x);return{createPublicClient:i,http:d}},__vite__mapDeps([2,1])),{bscTestnet:h}=await n(async()=>{const{bscTestnet:i}=await import("./web3-DJUNf-KT-*************-mmao4qk4a.js").then(d=>d.A);return{bscTestnet:i}},__vite__mapDeps([2,1]));return{receipt:await f({chain:h,transport:T()}).waitForTransactionReceipt({hash:b,timeout:6e4}),txHash:b}}catch(e){throw console.error("关闭房间失败:",e),e}}async function z({chainId:w,roomId:_,signer:u}){try{if(!u)throw new Error("签名者未定义，请确保钱包已连接");const{getContractAddress:e}=await n(async()=>{const{getContractAddress:i}=await import("./index-DmsQauDE-*************-rjh9jemju.js").then(d=>d.j);return{getContractAddress:i}},__vite__mapDeps([0,1,2,3])),s=e(w,"GroupBuyRoom"),{ABIS:g}=await n(async()=>{const{ABIS:i}=await import("./index-DmsQauDE-*************-rjh9jemju.js").then(d=>d.k);return{ABIS:i}},__vite__mapDeps([0,1,2,3])),p=g.GroupBuyRoom,b=await u.writeContract({address:s,abi:p,functionName:"expireRoom",args:[BigInt(_)]}),{createPublicClient:f,http:T}=await n(async()=>{const{createPublicClient:i,http:d}=await import("./web3-DJUNf-KT-*************-mmao4qk4a.js").then(A=>A.x);return{createPublicClient:i,http:d}},__vite__mapDeps([2,1])),{bscTestnet:h}=await n(async()=>{const{bscTestnet:i}=await import("./web3-DJUNf-KT-*************-mmao4qk4a.js").then(d=>d.A);return{bscTestnet:i}},__vite__mapDeps([2,1]));return{receipt:await f({chain:h,transport:T()}).waitForTransactionReceipt({hash:b,timeout:6e4}),txHash:b}}catch(e){throw console.error("过期房间处理失败:",e),e}}export{q as approveQPTForCreate,H as approveUSDTForJoin,K as closeRoom,F as createRoom,F as createRoomWithQPTVerification,z as expireRoom,M as joinRoom,M as joinRoomWithPaymentVerification,j as lockQPTForCreate,G as validateCreatorQPTLock,W as validateJoinRoomConditions,x as validateParticipantUSDT};
