// qupintuan/hardhat/scripts/upgrade/agentsystem-redeploy-implementation.js
// 重新部署AgentSystem实现合约

const { ethers, upgrades } = require("hardhat");

async function main() {
  console.log("🏗️ 重新部署AgentSystem实现合约...\n");

  const AGENT_SYSTEM_PROXY = "******************************************";

  try {
    const [deployer] = await ethers.getSigners();
    console.log(`🔑 部署账户: ${deployer.address}`);
    
    const balance = await ethers.provider.getBalance(deployer.address);
    console.log(`💰 账户余额: ${ethers.formatEther(balance)} BNB\n`);

    // 1. 检查当前实现
    console.log("1️⃣ 检查当前实现...");
    
    const provider = ethers.provider;
    const implementationSlot = "0x360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc";
    const implStorage = await provider.send("eth_getStorageAt", [AGENT_SYSTEM_PROXY, implementationSlot, "latest"]);
    const currentImplAddress = "0x" + implStorage.slice(-40);
    
    console.log(`   当前实现: ${currentImplAddress}`);

    // 2. 编译合约
    console.log("\n2️⃣ 编译合约...");
    
    const AgentSystemFactory = await ethers.getContractFactory("AgentSystem");
    console.log("   ✅ 合约编译成功");

    // 3. 验证存储布局兼容性
    console.log("\n3️⃣ 验证存储布局兼容性...");
    
    try {
      await upgrades.validateUpgrade(AGENT_SYSTEM_PROXY, AgentSystemFactory);
      console.log("   ✅ 存储布局兼容性检查通过");
    } catch (error) {
      console.log("   ⚠️ 存储布局警告:", error.message);
      console.log("   继续部署...");
    }

    // 4. 部署新实现合约
    console.log("\n4️⃣ 部署新实现合约...");
    
    // 使用upgrades.prepareUpgrade确保正确的部署方式
    const newImplementationAddress = await upgrades.prepareUpgrade(
      AGENT_SYSTEM_PROXY,
      AgentSystemFactory,
      {
        kind: 'uups'
      }
    );
    
    console.log(`   ✅ 新实现合约部署成功: ${newImplementationAddress}`);

    // 5. 验证新实现合约
    console.log("\n5️⃣ 验证新实现合约...");
    
    try {
      const newImpl = await ethers.getContractAt("AgentSystem", newImplementationAddress);
      
      // 检查UUPS UUID
      const proxiableUUID = await newImpl.proxiableUUID();
      console.log(`   UUPS UUID: ${proxiableUUID}`);
      
      // 检查合约代码
      const code = await ethers.provider.getCode(newImplementationAddress);
      console.log(`   合约代码长度: ${code.length} 字符`);
      
      // 确保新实现未初始化
      try {
        await newImpl.systemAdmin();
        console.log(`   ⚠️ 警告: 新实现可能已初始化`);
      } catch (error) {
        console.log(`   ✅ 新实现未初始化（正常）`);
      }
      
    } catch (error) {
      console.log(`   ❌ 验证新实现失败: ${error.message}`);
    }

    // 6. 检查当前账户权限
    console.log("\n6️⃣ 检查当前账户权限...");
    
    try {
      const agentSystem = await ethers.getContractAt("AgentSystem", AGENT_SYSTEM_PROXY);
      
      const ADMIN_ROLE = await agentSystem.ADMIN_ROLE();
      const DEFAULT_ADMIN_ROLE = "0x0000000000000000000000000000000000000000000000000000000000000000";
      
      const hasAdmin = await agentSystem.hasRole(ADMIN_ROLE, deployer.address);
      const hasDefaultAdmin = await agentSystem.hasRole(DEFAULT_ADMIN_ROLE, deployer.address);
      
      console.log(`   有管理员权限: ${hasAdmin ? '✅' : '❌'}`);
      console.log(`   有默认管理员权限: ${hasDefaultAdmin ? '✅' : '❌'}`);
      
      if (hasAdmin || hasDefaultAdmin) {
        console.log(`   ✅ 可以直接升级`);
        
        // 7. 执行直接升级
        console.log("\n7️⃣ 执行直接升级...");
        
        try {
          console.log("   正在执行升级...");
          const upgradeTx = await agentSystem.upgradeToAndCall(newImplementationAddress, "0x");
          
          console.log(`   ⏳ 升级交易已提交: ${upgradeTx.hash}`);
          const receipt = await upgradeTx.wait();
          console.log(`   ✅ 升级交易确认成功! Gas使用: ${receipt.gasUsed.toString()}`);
          
          // 验证升级结果
          const newImplStorage = await provider.send("eth_getStorageAt", [AGENT_SYSTEM_PROXY, implementationSlot, "latest"]);
          const newCurrentImplAddress = "0x" + newImplStorage.slice(-40);
          
          if (newCurrentImplAddress.toLowerCase() === newImplementationAddress.toLowerCase()) {
            console.log(`   🎉 升级成功！新实现地址: ${newCurrentImplAddress}`);
            
            // 8. 验证升级后的功能
            console.log("\n8️⃣ 验证升级后的功能...");
            
            try {
              // 测试新函数
              const testUser = "******************************************";
              const validation = await agentSystem.validateUserLevel(testUser);
              
              console.log(`   ✅ validateUserLevel函数正常工作`);
              console.log(`   测试用户等级有效: ${validation[0]}`);
              console.log(`   应有等级: Level ${validation[2]}`);
              
            } catch (error) {
              console.log(`   ❌ 新函数测试失败: ${error.message}`);
            }
            
          } else {
            console.log(`   ❌ 升级失败，实现地址未更新`);
          }
          
        } catch (error) {
          console.log(`   ❌ 升级失败: ${error.message}`);
          
          if (error.message.includes("AccessControl")) {
            console.log(`   原因: 权限不足`);
          } else if (error.message.includes("Address: low-level delegate call failed")) {
            console.log(`   原因: 委托调用失败`);
          }
        }
        
      } else {
        console.log(`   ❌ 无法直接升级，需要通过多签`);
        
        // 生成新的升级提案
        console.log("\n7️⃣ 生成新的升级提案...");
        
        const upgradeInterface = new ethers.Interface([
          "function upgradeToAndCall(address newImplementation, bytes calldata data)"
        ]);
        
        const upgradeCalldata = upgradeInterface.encodeFunctionData("upgradeToAndCall", [
          newImplementationAddress,
          "0x"
        ]);
        
        console.log(`   新升级调用数据: ${upgradeCalldata}`);
        console.log(`   目标合约: ${AGENT_SYSTEM_PROXY}`);
        console.log(`   新实现地址: ${newImplementationAddress}`);
      }
      
    } catch (error) {
      console.log(`   ❌ 权限检查失败: ${error.message}`);
    }

    // 9. 保存部署信息
    console.log("\n9️⃣ 保存部署信息...");
    
    const deploymentInfo = {
      timestamp: new Date().toISOString(),
      network: "bscTestnet",
      proxyAddress: AGENT_SYSTEM_PROXY,
      oldImplementation: currentImplAddress,
      newImplementation: newImplementationAddress,
      deployer: deployer.address,
      gasUsed: "待更新"
    };
    
    const fs = require('fs');
    const filename = `agentsystem-redeploy-${Date.now()}.json`;
    fs.writeFileSync(filename, JSON.stringify(deploymentInfo, null, 2));
    console.log(`   ✅ 部署信息已保存到: ${filename}`);

    // 10. 总结
    console.log("\n🎉 重新部署完成！");
    console.log("=".repeat(50));
    
    console.log(`\n📋 部署结果:`);
    console.log(`   代理地址: ${AGENT_SYSTEM_PROXY}`);
    console.log(`   旧实现: ${currentImplAddress}`);
    console.log(`   新实现: ${newImplementationAddress}`);
    
    // 检查最终状态
    const finalImplStorage = await provider.send("eth_getStorageAt", [AGENT_SYSTEM_PROXY, implementationSlot, "latest"]);
    const finalImplAddress = "0x" + finalImplStorage.slice(-40);
    
    if (finalImplAddress.toLowerCase() === newImplementationAddress.toLowerCase()) {
      console.log(`   状态: ✅ 升级已完成`);
      console.log(`\n📋 下一步:`);
      console.log(`   1. 验证升级结果:`);
      console.log(`      npx hardhat run scripts/verify-agentsystem-fix.js --network bscTestnet`);
      console.log(`   2. 修复问题用户:`);
      console.log(`      npx hardhat run scripts/fix-all-invalid-users.js --network bscTestnet`);
    } else {
      console.log(`   状态: ❌ 升级未完成`);
      console.log(`\n📋 下一步:`);
      console.log(`   1. 通过多签提交新的升级提案`);
      console.log(`   2. 使用新实现地址: ${newImplementationAddress}`);
    }

  } catch (error) {
    console.error("❌ 重新部署失败:", error.message);
    console.error("详细错误:", error);
    process.exit(1);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
