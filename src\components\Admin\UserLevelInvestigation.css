/* src/components/Admin/UserLevelInvestigation.css */

.user-level-investigation {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.investigation-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
}

.investigation-header h3 {
  margin: 0 0 10px 0;
  font-size: 24px;
  font-weight: 600;
}

.investigation-header p {
  margin: 0;
  opacity: 0.9;
  font-size: 14px;
}

.investigation-input {
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.input-group {
  display: flex;
  align-items: center;
  gap: 15px;
  flex-wrap: wrap;
}

.input-group label {
  font-weight: 500;
  color: #495057;
  min-width: 80px;
}

.input-group input {
  flex: 1;
  min-width: 300px;
  padding: 10px 15px;
  border: 1px solid #ced4da;
  border-radius: 6px;
  font-size: 14px;
  font-family: 'Monaco', 'Menlo', monospace;
}

.input-group input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.investigate-btn {
  padding: 10px 20px;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.investigate-btn:hover:not(:disabled) {
  background: #5a6fd8;
  transform: translateY(-1px);
}

.investigate-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.investigation-results {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.error-result {
  padding: 30px;
  text-align: center;
  color: #dc3545;
}

.error-result h4 {
  margin: 0 0 15px 0;
  font-size: 20px;
}

.success-result h4 {
  margin: 0;
  padding: 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  font-size: 20px;
  color: #495057;
}

.result-section {
  padding: 20px;
  border-bottom: 1px solid #e9ecef;
}

.result-section:last-child {
  border-bottom: none;
}

.result-section h5 {
  margin: 0 0 15px 0;
  font-size: 16px;
  color: #495057;
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #667eea;
}

.info-item .label {
  font-weight: 500;
  color: #6c757d;
  font-size: 14px;
}

.info-item .value {
  font-weight: 600;
  color: #495057;
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 14px;
}

.upgrade-type-0 { color: #6c757d; }
.upgrade-type-1 { color: #28a745; }
.upgrade-type-2 { color: #fd7e14; }

.value.valid { color: #28a745; }
.value.invalid { color: #dc3545; }

.referrals-table {
  overflow-x: auto;
  margin-top: 10px;
}

.referrals-table table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.referrals-table th,
.referrals-table td {
  padding: 12px 8px;
  text-align: left;
  border-bottom: 1px solid #e9ecef;
}

.referrals-table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #495057;
  position: sticky;
  top: 0;
}

.referrals-table td {
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 13px;
}

.excluded {
  color: #dc3545;
  font-weight: 500;
}

.included {
  color: #28a745;
  font-weight: 500;
}

.diagnosis {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.diagnosis-item {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  padding: 15px;
  border-radius: 8px;
  border-left: 4px solid;
}

.diagnosis-item.success {
  background: #d4edda;
  border-left-color: #28a745;
}

.diagnosis-item.warning {
  background: #fff3cd;
  border-left-color: #ffc107;
}

.diagnosis-item.error {
  background: #f8d7da;
  border-left-color: #dc3545;
}

.diagnosis-item.info {
  background: #d1ecf1;
  border-left-color: #17a2b8;
}

.diagnosis-item .icon {
  font-size: 20px;
  line-height: 1;
  margin-top: 2px;
}

.diagnosis-item .content {
  flex: 1;
}

.diagnosis-item .content strong {
  display: block;
  margin-bottom: 5px;
  font-size: 16px;
  color: #495057;
}

.diagnosis-item .content p {
  margin: 0;
  color: #6c757d;
  line-height: 1.5;
}

@media (max-width: 768px) {
  .user-level-investigation {
    padding: 15px;
  }
  
  .input-group {
    flex-direction: column;
    align-items: stretch;
  }
  
  .input-group input {
    min-width: auto;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
  
  .referrals-table {
    font-size: 12px;
  }
  
  .referrals-table th,
  .referrals-table td {
    padding: 8px 4px;
  }
}
