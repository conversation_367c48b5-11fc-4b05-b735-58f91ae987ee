const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index-DmsQauDE-1754738192351-rjh9jemju.js","assets/vendor-Caz4khA--1754738192351-mmao4qk4a.js","assets/web3-DJUNf-KT-1754738192351-mmao4qk4a.js","assets/index-Dcug_PHz-1754738192351-gaytee33w.css"])))=>i.map(i=>d[i]);
import{_ as i}from"./vendor-Caz4khA--1754738192351-mmao4qk4a.js";const O=new Map,x=new Map,U=15e3,j=8e3;async function J({chainId:h}){try{const{getContractAddress:s}=await i(async()=>{const{getContractAddress:r}=await import("./index-DmsQauDE-1754738192351-rjh9jemju.js").then(n=>n.j);return{getContractAddress:r}},__vite__mapDeps([0,1,2,3])),l=s(h,"GroupBuyRoom"),{ABIS:c}=await i(async()=>{const{ABIS:r}=await import("./index-DmsQauDE-1754738192351-rjh9jemju.js").then(n=>n.k);return{ABIS:r}},__vite__mapDeps([0,1,2,3])),d=c.GroupBuyRoom,{createPublicClient:u,http:I}=await i(async()=>{const{createPublicClient:r,http:n}=await import("./web3-DJUNf-KT-1754738192351-mmao4qk4a.js").then(R=>R.x);return{createPublicClient:r,http:n}},__vite__mapDeps([2,1])),{bscTestnet:m}=await i(async()=>{const{bscTestnet:r}=await import("./web3-DJUNf-KT-1754738192351-mmao4qk4a.js").then(n=>n.A);return{bscTestnet:r}},__vite__mapDeps([2,1]));return await u({chain:m,transport:I()}).readContract({address:l,abi:d,functionName:"totalRooms"})}catch(s){return console.error("获取总房间数量失败:",s),0n}}async function K({chainId:h,roomId:s}){const l=BigInt(s),c=`room_${h}_${s}`;try{const d=O.get(c);if(d&&Date.now()-d.timestamp<U)return d.data;if(x.has(c))return await x.get(c);if(l<0)throw new Error(`无效的房间ID: ${s}，房间ID应该大于等于0`);const u=(async()=>{try{const{getContractAddress:I}=await i(async()=>{const{getContractAddress:t}=await import("./index-DmsQauDE-1754738192351-rjh9jemju.js").then(e=>e.j);return{getContractAddress:t}},__vite__mapDeps([0,1,2,3])),m=I(h,"GroupBuyRoom"),{ABIS:b}=await i(async()=>{const{ABIS:t}=await import("./index-DmsQauDE-1754738192351-rjh9jemju.js").then(e=>e.k);return{ABIS:t}},__vite__mapDeps([0,1,2,3])),T=b.GroupBuyRoom,{createPublicClient:r,http:n}=await i(async()=>{const{createPublicClient:t,http:e}=await import("./web3-DJUNf-KT-1754738192351-mmao4qk4a.js").then(p=>p.x);return{createPublicClient:t,http:e}},__vite__mapDeps([2,1])),{bscTestnet:R}=await i(async()=>{const{bscTestnet:t}=await import("./web3-DJUNf-KT-1754738192351-mmao4qk4a.js").then(e=>e.A);return{bscTestnet:t}},__vite__mapDeps([2,1])),C=r({chain:R,transport:n()}),H=new Promise((t,e)=>{setTimeout(()=>e(new Error("请求超时")),j)}),M=await Promise.race([C.readContract({address:m,abi:T,functionName:"getRoom",args:[l]}),H]),[$,v,k,V,A,o,N,S,W]=M;let f=null;try{A&&(f=await C.readContract({address:m,abi:T,functionName:"getLotteryInfo",args:[l]}))}catch(t){t.message?.includes("limit exceeded")}let G=!1;try{A&&(G=await C.readContract({address:m,abi:T,functionName:"roomReadyForWinner",args:[l]}))}catch(t){t.message?.includes("limit exceeded")}const y=k.map(t=>t.toLowerCase());let _=null;try{const t=localStorage.getItem(`lottery_${l}`);t&&(_=JSON.parse(t))}catch{}let a=0,w=!1,P=Number(V),D=null;try{D=await C.readContract({address:m,abi:T,functionName:"getRoomTimeInfo",args:[l]})}catch(t){t.message?.includes("limit exceeded")}let g=!1;if(D){const[t,e,p,B,F]=D;P=Number(t),a=Number(F),g=B}else{const t=Math.floor(Date.now()/1e3),e=1440*60,p=Number(V);if(p>1e9){const B=p+e;a=Math.max(0,B-t),g=a===0,P=p}else g=!1,a=0}A?o!=="0x0000000000000000000000000000000000000000"?(w=!1,a=0):_&&_.txHash||y.length>=8?(w=!1,a=a>0?a:3600):(w=!0,a=0):y.length>=8?w=!1:g?(w=!0,a=0):w=!1;const E={id:Number(l),creator:$.toLowerCase(),tier:Number(v),tierAmount:Number(v),participants:y,participantsCount:y.length,maxParticipants:8,createTime:P,timeLeft:a,isExpired:w,isClosed:A,isSuccessful:o,winnerIndex:Number(N),lotteryTxHash:f&&f[2]?f[2]:o!=="0x0000000000000000000000000000000000000000"&&S?S:_?.txHash||null,lotteryTimestamp:f&&f[3]?Number(f[3]):o!=="0x0000000000000000000000000000000000000000"&&W?Number(W):_?.timestamp||null,winner:(()=>{if(o!=="0x0000000000000000000000000000000000000000"){const t=y.findIndex(e=>e===o.toLowerCase());return{index:t>=0?t+1:Number(N)+1,address:o}}return null})(),winnerAddress:o!=="0x0000000000000000000000000000000000000000"?o:null,calculatedWinner:_?.winner||null,winnerInfo:(()=>{if(o!=="0x0000000000000000000000000000000000000000"){const t=y.findIndex(e=>e===o.toLowerCase());return{address:o,winnerAddress:o,index:t>=0?t+1:Number(N)+1,source:"contract"}}if(f&&f[1]!=="0x0000000000000000000000000000000000000000"){const t=f[1],e=y.findIndex(p=>p===t.toLowerCase());return{address:t,winnerAddress:t,index:e>=0?e+1:Number(f[4])+1,source:"contract-lottery"}}return _?.winner?{..._.winner,source:"local"}:null})(),readyForWinner:G||_&&_.txHash,status:A&&o?"completed":A&&!o?"failed":w?"expired":y.length>=8?"full":"active"};let L=E;try{if(E.isClosed||E.participants&&E.participants.length>=8){const{enhanceRoomWithLotteryInfo:e}=await i(async()=>{const{enhanceRoomWithLotteryInfo:p}=await import("./lotteryInfoPersistence-BBzWo8Bu-1754738192351-mmao4qk4a.js");return{enhanceRoomWithLotteryInfo:p}},[]);L=e(E)}else L={...E,readyForWinner:!1,lotteryTxHash:null,lotteryTimestamp:null,calculatedWinner:null,lotteryInfo:null}}catch{}return O.set(c,{data:L,timestamp:Date.now()}),L}finally{x.delete(c)}})();return x.set(c,u),await u}catch(d){x.delete(c);const u=d.message||"";if(u.includes("limit exceeded")||u.includes("Failed to fetch")||u.includes("CORS")||u.includes("403")||u.includes("请求超时")){const m=O.get(c);return m?m.data:null}throw console.error(`获取房间 ${s} 信息失败:`,d),d}}async function Q(h){try{const{createPublicClient:s,http:l}=await i(async()=>{const{createPublicClient:r,http:n}=await import("./web3-DJUNf-KT-1754738192351-mmao4qk4a.js").then(R=>R.x);return{createPublicClient:r,http:n}},__vite__mapDeps([2,1])),{bscTestnet:c}=await i(async()=>{const{bscTestnet:r}=await import("./web3-DJUNf-KT-1754738192351-mmao4qk4a.js").then(n=>n.A);return{bscTestnet:r}},__vite__mapDeps([2,1])),{getContractAddress:d}=await i(async()=>{const{getContractAddress:r}=await import("./index-DmsQauDE-1754738192351-rjh9jemju.js").then(n=>n.j);return{getContractAddress:r}},__vite__mapDeps([0,1,2,3])),{ABIS:u}=await i(async()=>{const{ABIS:r}=await import("./index-DmsQauDE-1754738192351-rjh9jemju.js").then(n=>n.k);return{ABIS:r}},__vite__mapDeps([0,1,2,3])),I=s({chain:c,transport:l()}),m=d(97,"GroupBuyRoom"),b=u.GroupBuyRoom;return{lotteryInfo:await I.readContract({address:m,abi:b,functionName:"getLotteryInfo",args:[BigInt(h)]})}}catch(s){throw console.error(`验证房间#${h} 合约状态失败:`,s),s}}export{Q as debugRoomContractState,K as fetchRoom,J as fetchTotalRooms};
