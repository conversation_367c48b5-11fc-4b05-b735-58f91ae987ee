function c(){try{const e=[];for(let r=0;r<localStorage.length;r++){const t=localStorage.key(r);t&&(t.startsWith("claimStatus_")||t.startsWith("groupbuy-")||t.startsWith("qptlock-")||t.startsWith("qptbuyback"))&&e.push(t)}return e.forEach(r=>{localStorage.removeItem(r)}),{success:!0,removedCount:e.length}}catch(e){return{success:!1,error:e.message}}}function s(){try{const e=[];for(let r=0;r<localStorage.length;r++){const t=localStorage.key(r);if(t&&t.startsWith("claimStatus_"))try{const o=localStorage.getItem(t),a=JSON.parse(o);(typeof a!="object"||typeof a.hasClaimed!="boolean"||!a.timestamp)&&e.push(t)}catch{e.push(t)}}return{hasSuspiciousCache:e.length>0,suspiciousKeys:e,count:e.length}}catch(e){return{hasSuspiciousCache:!1,error:e.message}}}function u(){try{const e=s();return e.hasSuspiciousCache?(e.suspiciousKeys.forEach(r=>{localStorage.removeItem(r)}),{fixed:!0,removedCount:e.count}):{fixed:!1,removedCount:0}}catch(e){return{fixed:!1,error:e.message}}}typeof window<"u"&&(window.clearClaimStatusCache=c,window.checkClaimStatusCache=s,window.fixClaimStatusCache=u);export{u as autoFixClaimStatusCache,s as checkForCorruptedCache,c as clearAllClaimStatusCache};
