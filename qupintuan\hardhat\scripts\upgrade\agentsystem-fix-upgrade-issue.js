// qupintuan/hardhat/scripts/upgrade/agentsystem-fix-upgrade-issue.js
// 修复AgentSystem升级问题

const { ethers } = require("hardhat");

async function main() {
  console.log("🔧 修复AgentSystem升级问题...\n");

  const AGENT_SYSTEM_PROXY = "******************************************";
  const TIMELOCK_ADDRESS = "******************************************";
  const NEW_IMPLEMENTATION = "******************************************";

  try {
    const [deployer] = await ethers.getSigners();
    console.log(`🔑 操作账户: ${deployer.address}\n`);

    // 连接到合约
    const agentSystem = await ethers.getContractAt("AgentSystem", AGENT_SYSTEM_PROXY);
    
    console.log("📋 合约信息:");
    console.log(`   代理地址: ${AGENT_SYSTEM_PROXY}`);
    console.log(`   Timelock: ${TIMELOCK_ADDRESS}`);
    console.log(`   新实现: ${NEW_IMPLEMENTATION}`);
    console.log("");

    // 1. 检查当前权限状态
    console.log("1️⃣ 检查当前权限状态...");
    
    const ADMIN_ROLE = await agentSystem.ADMIN_ROLE();
    const DEFAULT_ADMIN_ROLE = "0x0000000000000000000000000000000000000000000000000000000000000000";
    
    console.log(`   管理员角色: ${ADMIN_ROLE}`);
    console.log(`   默认管理员角色: ${DEFAULT_ADMIN_ROLE}`);
    
    // 检查各种权限
    const timelockHasAdmin = await agentSystem.hasRole(ADMIN_ROLE, TIMELOCK_ADDRESS);
    const timelockHasDefaultAdmin = await agentSystem.hasRole(DEFAULT_ADMIN_ROLE, TIMELOCK_ADDRESS);
    const deployerHasAdmin = await agentSystem.hasRole(ADMIN_ROLE, deployer.address);
    const deployerHasDefaultAdmin = await agentSystem.hasRole(DEFAULT_ADMIN_ROLE, deployer.address);
    
    console.log(`   Timelock有管理员权限: ${timelockHasAdmin ? '✅' : '❌'}`);
    console.log(`   Timelock有默认管理员权限: ${timelockHasDefaultAdmin ? '✅' : '❌'}`);
    console.log(`   部署者有管理员权限: ${deployerHasAdmin ? '✅' : '❌'}`);
    console.log(`   部署者有默认管理员权限: ${deployerHasDefaultAdmin ? '✅' : '❌'}`);

    // 2. 检查当前实现
    console.log("\n2️⃣ 检查当前实现...");
    
    const provider = ethers.provider;
    const implementationSlot = "0x360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc";
    const implStorage = await provider.send("eth_getStorageAt", [AGENT_SYSTEM_PROXY, implementationSlot, "latest"]);
    const currentImplAddress = "0x" + implStorage.slice(-40);
    
    console.log(`   当前实现: ${currentImplAddress}`);
    console.log(`   目标实现: ${NEW_IMPLEMENTATION}`);
    console.log(`   已升级: ${currentImplAddress.toLowerCase() === NEW_IMPLEMENTATION.toLowerCase() ? '✅' : '❌'}`);

    // 3. 尝试直接升级（如果有权限）
    console.log("\n3️⃣ 尝试直接升级...");
    
    if (deployerHasAdmin || deployerHasDefaultAdmin) {
      console.log("   ✅ 当前账户有管理员权限，尝试直接升级...");
      
      try {
        // 估算gas
        const gasEstimate = await agentSystem.upgradeToAndCall.estimateGas(NEW_IMPLEMENTATION, "0x");
        console.log(`   预估Gas: ${gasEstimate.toString()}`);
        
        // 执行升级
        console.log("   正在执行升级...");
        const upgradeTx = await agentSystem.upgradeToAndCall(NEW_IMPLEMENTATION, "0x", {
          gasLimit: gasEstimate * 120n / 100n // 增加20%的gas余量
        });
        
        console.log(`   ⏳ 升级交易已提交: ${upgradeTx.hash}`);
        const receipt = await upgradeTx.wait();
        console.log(`   ✅ 升级交易确认成功! Gas使用: ${receipt.gasUsed.toString()}`);
        
        // 验证升级结果
        const newImplStorage = await provider.send("eth_getStorageAt", [AGENT_SYSTEM_PROXY, implementationSlot, "latest"]);
        const newCurrentImplAddress = "0x" + newImplStorage.slice(-40);
        
        if (newCurrentImplAddress.toLowerCase() === NEW_IMPLEMENTATION.toLowerCase()) {
          console.log(`   🎉 升级成功！新实现地址: ${newCurrentImplAddress}`);
        } else {
          console.log(`   ❌ 升级失败，实现地址未更新`);
        }
        
      } catch (error) {
        console.log(`   ❌ 直接升级失败: ${error.message}`);
        
        // 分析错误原因
        if (error.message.includes("AccessControl")) {
          console.log(`   原因: 权限不足`);
        } else if (error.message.includes("Address: low-level delegate call failed")) {
          console.log(`   原因: 委托调用失败，可能是新实现合约有问题`);
        } else {
          console.log(`   原因: 未知错误`);
        }
      }
      
    } else {
      console.log("   ❌ 当前账户没有管理员权限，无法直接升级");
      
      // 4. 尝试通过Timelock升级
      console.log("\n4️⃣ 准备通过Timelock升级...");
      
      if (timelockHasAdmin || timelockHasDefaultAdmin) {
        console.log("   ✅ Timelock有管理员权限");
        
        // 检查新实现合约是否有问题
        console.log("   检查新实现合约...");
        
        try {
          const newImpl = await ethers.getContractAt("AgentSystem", NEW_IMPLEMENTATION);
          const newProxiableUUID = await newImpl.proxiableUUID();
          console.log(`   新实现UUPS UUID: ${newProxiableUUID}`);
          
          // 检查新实现是否已初始化
          try {
            await newImpl.systemAdmin();
            console.log(`   ⚠️ 新实现可能已初始化，这会导致升级失败`);
            console.log(`   建议重新部署新实现合约`);
          } catch (error) {
            console.log(`   ✅ 新实现未初始化（正常）`);
          }
          
        } catch (error) {
          console.log(`   ❌ 检查新实现失败: ${error.message}`);
        }
        
      } else {
        console.log("   ❌ Timelock也没有管理员权限");
      }
    }

    // 5. 建议的解决方案
    console.log("\n5️⃣ 建议的解决方案...");
    console.log("=".repeat(50));
    
    if (currentImplAddress.toLowerCase() === NEW_IMPLEMENTATION.toLowerCase()) {
      console.log("🎉 升级已完成！");
      console.log("运行验证脚本: npx hardhat run scripts/verify-agentsystem-fix.js --network bscTestnet");
    } else {
      console.log("🔧 升级尚未完成，建议的解决方案:");
      
      if (deployerHasAdmin || deployerHasDefaultAdmin) {
        console.log("1. 当前账户有权限，重新部署新实现合约:");
        console.log("   npx hardhat run scripts/upgrade/agentsystem-redeploy-and-upgrade.js --network bscTestnet");
      } else {
        console.log("1. 重新部署新实现合约（确保未初始化）:");
        console.log("   npx hardhat run scripts/upgrade/agentsystem-redeploy-implementation.js --network bscTestnet");
        console.log("");
        console.log("2. 重新提交升级提案:");
        console.log("   npx hardhat run scripts/upgrade/agentsystem-resubmit-proposal.js --network bscTestnet");
      }
    }

    // 6. 检查问题用户状态
    console.log("\n6️⃣ 检查问题用户状态...");
    
    const problemUsers = [
      "0xDf98905098CB4e5D261578f600337eeeFd4082b3",
      "0x96549975B0704d528E3034f81036e8D728F68084"
    ];
    
    for (const userAddress of problemUsers) {
      try {
        const userInfo = await agentSystem.getUserInfo(userAddress);
        const currentLevel = Number(userInfo[1]);
        
        console.log(`   用户 ${userAddress}:`);
        console.log(`     当前等级: Level ${currentLevel}`);
        
        // 尝试调用新函数（如果升级完成）
        try {
          const validation = await agentSystem.validateUserLevel(userAddress);
          const isValid = validation[0];
          const expectedLevel = Number(validation[2]);
          
          console.log(`     等级有效: ${isValid ? '✅' : '❌'}`);
          console.log(`     应有等级: Level ${expectedLevel}`);
          
          if (!isValid) {
            console.log(`     🚨 升级完成后需要修复: Level ${currentLevel} → Level ${expectedLevel}`);
          }
          
        } catch (error) {
          console.log(`     ⚠️ 新函数不可用（升级未完成）`);
        }
        
      } catch (error) {
        console.log(`   ❌ 检查用户 ${userAddress} 失败: ${error.message}`);
      }
    }

  } catch (error) {
    console.error("❌ 修复失败:", error.message);
    console.error("详细错误:", error);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
