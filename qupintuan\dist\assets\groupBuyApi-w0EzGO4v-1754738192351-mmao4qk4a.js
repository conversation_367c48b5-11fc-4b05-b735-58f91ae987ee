const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index-DmsQauDE-1754738192351-rjh9jemju.js","assets/vendor-Caz4khA--1754738192351-mmao4qk4a.js","assets/web3-DJUNf-KT-1754738192351-mmao4qk4a.js","assets/index-Dcug_PHz-1754738192351-gaytee33w.css","assets/enhancedGroupBuyService-DI9Z_-rf-1754738192351-mmao4qk4a.js"])))=>i.map(i=>d[i]);
import{fetchRoom as D,fetchTotalRooms as v}from"./basicOperations-CmXjWg1V-1754738192351-mmao4qk4a.js";import{approveQPTForCreate as N,approveUSDTForJoin as O,closeRoom as Q,createRoomWithQPTVerification as S,createRoomWithQPTVerification as x,expireRoom as W,joinRoomWithPaymentVerification as j,joinRoomWithPaymentVerification as q,lockQPTForCreate as U}from"./roomManagement-CRZ0InUC-1754738192351-mmao4qk4a.js";import{_ as n}from"./vendor-Caz4khA--1754738192351-mmao4qk4a.js";import{c as M,d as F,e as H,b as $,a as J,g as z,f as K}from"./rewardOperations-2IHLUMdh-1754738192351-mmao4qk4a.js";async function E({chainId:_,roomId:d,winner:m,lotteryTxHash:e,lotteryTimestamp:c,signer:w}){try{if(!w)throw new Error("签名者未定义，请确保钱包已连接");if(!m||!e||!c)throw new Error("缺少必要参数：winner, lotteryTxHash, lotteryTimestamp");const{getContractAddress:i}=await n(async()=>{const{getContractAddress:t}=await import("./index-DmsQauDE-1754738192351-rjh9jemju.js").then(r=>r.j);return{getContractAddress:t}},__vite__mapDeps([0,1,2,3])),p=i(_,"GroupBuyRoom"),{ABIS:h}=await n(async()=>{const{ABIS:t}=await import("./index-DmsQauDE-1754738192351-rjh9jemju.js").then(r=>r.k);return{ABIS:t}},__vite__mapDeps([0,1,2,3])),P=h.GroupBuyRoom,{createPublicClient:f,http:C}=await n(async()=>{const{createPublicClient:t,http:r}=await import("./web3-DJUNf-KT-1754738192351-mmao4qk4a.js").then(a=>a.x);return{createPublicClient:t,http:r}},__vite__mapDeps([2,1])),{bscTestnet:l}=await n(async()=>{const{bscTestnet:t}=await import("./web3-DJUNf-KT-1754738192351-mmao4qk4a.js").then(r=>r.A);return{bscTestnet:t}},__vite__mapDeps([2,1])),o=f({chain:l,transport:C()});try{const t=await o.readContract({address:p,abi:P,functionName:"roomLastActionTime",args:[BigInt(d)]});c<Number(t)&&(c=Number(t))}catch{const r=await o.getBlock();c=Number(r.timestamp)}const s=await w.writeContract({address:p,abi:P,functionName:"setWinner",args:[BigInt(d),m,e,BigInt(c)]}),u=await o.waitForTransactionReceipt({hash:s,timeout:6e4});if(u.status==="reverted")throw new Error("交易被区块链网络拒绝，可能是合约执行失败");return{receipt:u,txHash:s}}catch(i){throw i.message?.includes("User rejected")?new Error("用户取消了交易"):i.message?.includes("insufficient funds")?new Error("账户余额不足，无法支付交易费用"):i.message?.includes("execution reverted")?new Error("合约执行失败，请检查交易参数"):i}}async function g({chainId:_,roomId:d}){try{const{getContractAddress:m}=await n(async()=>{const{getContractAddress:a}=await import("./index-DmsQauDE-1754738192351-rjh9jemju.js").then(A=>A.j);return{getContractAddress:a}},__vite__mapDeps([0,1,2,3])),{ABIS:e}=await n(async()=>{const{ABIS:a}=await import("./index-DmsQauDE-1754738192351-rjh9jemju.js").then(A=>A.k);return{ABIS:a}},__vite__mapDeps([0,1,2,3])),c=m(_,"QPTLocker"),w=e.QPTLocker,{createPublicClient:i,http:p}=await n(async()=>{const{createPublicClient:a,http:A}=await import("./web3-DJUNf-KT-1754738192351-mmao4qk4a.js").then(T=>T.x);return{createPublicClient:a,http:A}},__vite__mapDeps([2,1])),{bscTestnet:h}=await n(async()=>{const{bscTestnet:a}=await import("./web3-DJUNf-KT-1754738192351-mmao4qk4a.js").then(A=>A.A);return{bscTestnet:a}},__vite__mapDeps([2,1])),f=await i({chain:h,transport:p()}).readContract({address:c,abi:w,functionName:"getRoomInfo",args:[BigInt(d)]}),[C,l,o,s,u]=f,t=Math.floor(Date.now()/1e3),r=Math.max(0,Number(o)-t);return{creator:C,amount:Number(l),unlockTime:Number(o),isSuccess:s,isClaimed:u,remainingTime:r,isUnlocked:r===0}}catch(m){return console.error("获取QPT锁仓信息失败:",m),{creator:"0x0000000000000000000000000000000000000000",amount:0,unlockTime:0,isSuccess:!1,isClaimed:!1,remainingTime:0,isUnlocked:!1}}}async function I({chainId:_,roomId:d,signer:m}){try{if(!m)throw new Error("签名者未定义，请确保钱包已连接");const{getContractAddress:e}=await n(async()=>{const{getContractAddress:o}=await import("./index-DmsQauDE-1754738192351-rjh9jemju.js").then(s=>s.j);return{getContractAddress:o}},__vite__mapDeps([0,1,2,3])),{ABIS:c}=await n(async()=>{const{ABIS:o}=await import("./index-DmsQauDE-1754738192351-rjh9jemju.js").then(s=>s.k);return{ABIS:o}},__vite__mapDeps([0,1,2,3])),w=e(_,"QPTLocker"),i=c.QPTLocker,p=await m.writeContract({address:w,abi:i,functionName:"claimLockedQPT",args:[BigInt(d)]}),{createPublicClient:h,http:P}=await n(async()=>{const{createPublicClient:o,http:s}=await import("./web3-DJUNf-KT-1754738192351-mmao4qk4a.js").then(u=>u.x);return{createPublicClient:o,http:s}},__vite__mapDeps([2,1])),{bscTestnet:f}=await n(async()=>{const{bscTestnet:o}=await import("./web3-DJUNf-KT-1754738192351-mmao4qk4a.js").then(s=>s.A);return{bscTestnet:o}},__vite__mapDeps([2,1]));return{receipt:await h({chain:f,transport:P()}).waitForTransactionReceipt({hash:p,timeout:6e4}),txHash:p}}catch(e){throw console.error("领取锁定QPT失败:",e),e}}async function R({chainId:_,roomId:d,userAddress:m}){try{const{getUserPaidAmount:e}=await n(async()=>{const{getUserPaidAmount:w}=await import("./enhancedGroupBuyService-DI9Z_-rf-1754738192351-mmao4qk4a.js");return{getUserPaidAmount:w}},__vite__mapDeps([4,1,0,2,3]));return await e(d,m)}catch(e){return console.error("获取用户支付金额失败:",e),"0"}}async function y({chainId:_,roomId:d,userAddress:m,role:e}){try{const{getContractAddress:c}=await n(async()=>{const{getContractAddress:r}=await import("./index-DmsQauDE-1754738192351-rjh9jemju.js").then(a=>a.j);return{getContractAddress:r}},__vite__mapDeps([0,1,2,3])),{ABIS:w}=await n(async()=>{const{ABIS:r}=await import("./index-DmsQauDE-1754738192351-rjh9jemju.js").then(a=>a.k);return{ABIS:r}},__vite__mapDeps([0,1,2,3])),i=c(_,"GroupBuyRoom"),p=w.GroupBuyRoom,{createPublicClient:h,http:P}=await n(async()=>{const{createPublicClient:r,http:a}=await import("./web3-DJUNf-KT-1754738192351-mmao4qk4a.js").then(A=>A.x);return{createPublicClient:r,http:a}},__vite__mapDeps([2,1])),{bscTestnet:f}=await n(async()=>{const{bscTestnet:r}=await import("./web3-DJUNf-KT-1754738192351-mmao4qk4a.js").then(a=>a.A);return{bscTestnet:r}},__vite__mapDeps([2,1])),C=h({chain:f,transport:P()}),[l,o,s,u]=await C.readContract({address:i,abi:p,functionName:"getUserClaimStatus",args:[BigInt(d),m]});let t;switch(e){case"creator":t=l;break;case"winner":try{const[r,a]=await C.readContract({address:i,abi:p,functionName:"getWinnerRewardStatus",args:[BigInt(d),m]});t=r||a}catch{t=!1}break;case"participant":t=o;break;default:throw new Error(`不支持的角色类型: ${e}`)}return t}catch(c){return c.message?.includes("not found on ABI"),!1}}async function B({chainId:_,roomId:d,winnerAddress:m}){try{if(_!==97)throw new Error(`不支持的链ID: ${_}`);const{getContractAddress:e}=await n(async()=>{const{getContractAddress:u}=await import("./index-DmsQauDE-1754738192351-rjh9jemju.js").then(t=>t.j);return{getContractAddress:u}},__vite__mapDeps([0,1,2,3])),{ABIS:c}=await n(async()=>{const{ABIS:u}=await import("./index-DmsQauDE-1754738192351-rjh9jemju.js").then(t=>t.k);return{ABIS:u}},__vite__mapDeps([0,1,2,3])),w=e(_,"QPTLocker"),i=e(_,"GroupBuyRoom"),p=c.QPTLocker,h=c.GroupBuyRoom,{createPublicClient:P,http:f}=await n(async()=>{const{createPublicClient:u,http:t}=await import("./web3-DJUNf-KT-1754738192351-mmao4qk4a.js").then(r=>r.x);return{createPublicClient:u,http:t}},__vite__mapDeps([2,1])),{bscTestnet:C}=await n(async()=>{const{bscTestnet:u}=await import("./web3-DJUNf-KT-1754738192351-mmao4qk4a.js").then(t=>t.A);return{bscTestnet:u}},__vite__mapDeps([2,1])),l=P({chain:C,transport:f()});let o=0,s=0;try{const t=(await l.readContract({address:i,abi:h,functionName:"rooms",args:[BigInt(d)]}))[1],r=await l.readContract({address:w,abi:p,functionName:"amountMappings",args:[t]});o=Number(r[2])/1e18;const a=await l.readContract({address:i,abi:h,functionName:"tierPoints",args:[t]}).catch(()=>0n);s=Number(a)/1e6}catch{try{const r=(await l.readContract({address:i,abi:h,functionName:"rooms",args:[BigInt(d)]}))[1],a=await l.readContract({address:w,abi:p,functionName:"amountMappings",args:[r]}).catch(()=>null);a&&(o=Number(a[2])/1e18);const A=await l.readContract({address:i,abi:h,functionName:"tierPoints",args:[r]}).catch(()=>0n);s=Number(A)/1e6}catch(t){console.warn("备用方法查询赢家奖励失败:",t)}}return{qptAmount:o,pointsAmount:s}}catch(e){return console.error("获取赢家奖励数据失败:",e),{qptAmount:0,pointsAmount:0}}}export{N as approveQPTForCreate,O as approveUSDTForJoin,y as checkRoleClaimStatus,M as claimCreatorCommission,I as claimLockedQPT,F as claimParticipantRefund,H as claimReward,$ as claimWinnerPoints,J as claimWinnerQPT,Q as closeRoom,S as createRoom,x as createRoomWithQPTVerification,W as expireRoom,D as fetchRoom,v as fetchTotalRooms,z as getCreatorCommissionAmount,K as getParticipantRefundAmount,g as getQPTLockInfo,R as getUserPaidAmount,B as getWinnerRewards,j as joinRoom,q as joinRoomWithPaymentVerification,U as lockQPTForCreate,E as setWinner};
