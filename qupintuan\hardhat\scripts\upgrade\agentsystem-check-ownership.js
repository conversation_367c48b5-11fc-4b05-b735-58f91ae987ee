// qupintuan/hardhat/scripts/upgrade/agentsystem-check-ownership.js
// 检查AgentSystem合约的所有权和实现状态

const { ethers } = require("hardhat");

async function main() {
  console.log("🔍 检查AgentSystem合约的所有权和实现状态...\n");

  const AGENT_SYSTEM_PROXY = "******************************************";
  const TIMELOCK_ADDRESS = "******************************************";
  const NEW_IMPLEMENTATION = "******************************************";

  try {
    // 连接到代理合约
    const agentSystem = await ethers.getContractAt("AgentSystem", AGENT_SYSTEM_PROXY);
    
    console.log("📋 合约信息:");
    console.log(`   代理地址: ${AGENT_SYSTEM_PROXY}`);
    console.log(`   Timelock: ${TIMELOCK_ADDRESS}`);
    console.log(`   新实现: ${NEW_IMPLEMENTATION}`);
    console.log("");

    // 1. 检查当前实现地址
    console.log("1️⃣ 检查当前实现地址...");
    
    try {
      // 使用web3方式获取存储
      const provider = ethers.provider;
      const implementationSlot = "0x360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc";
      const implStorage = await provider.send("eth_getStorageAt", [AGENT_SYSTEM_PROXY, implementationSlot, "latest"]);
      const currentImplAddress = "0x" + implStorage.slice(-40);
      
      console.log(`   当前实现: ${currentImplAddress}`);
      console.log(`   目标实现: ${NEW_IMPLEMENTATION}`);
      console.log(`   已升级: ${currentImplAddress.toLowerCase() === NEW_IMPLEMENTATION.toLowerCase() ? '✅' : '❌'}`);
      
    } catch (error) {
      console.log(`   ❌ 获取实现地址失败: ${error.message}`);
    }

    // 2. 检查合约所有者
    console.log("\n2️⃣ 检查合约所有者...");
    
    try {
      const owner = await agentSystem.owner();
      console.log(`   当前所有者: ${owner}`);
      console.log(`   Timelock地址: ${TIMELOCK_ADDRESS}`);
      console.log(`   所有者是Timelock: ${owner.toLowerCase() === TIMELOCK_ADDRESS.toLowerCase() ? '✅' : '❌'}`);
      
      if (owner.toLowerCase() !== TIMELOCK_ADDRESS.toLowerCase()) {
        console.log(`   🚨 问题: 所有者不是Timelock，无法通过Timelock升级`);
      }
      
    } catch (error) {
      console.log(`   ❌ 获取所有者失败: ${error.message}`);
    }

    // 3. 检查UUPS相关
    console.log("\n3️⃣ 检查UUPS相关...");
    
    try {
      const proxiableUUID = await agentSystem.proxiableUUID();
      console.log(`   UUPS UUID: ${proxiableUUID}`);
      
      // 检查_authorizeUpgrade函数
      const [deployer] = await ethers.getSigners();
      try {
        // 尝试调用_authorizeUpgrade（应该失败，因为不是所有者）
        await agentSystem.connect(deployer).callStatic.upgradeToAndCall(NEW_IMPLEMENTATION, "0x");
        console.log(`   ⚠️ 意外：非所有者可以升级`);
      } catch (error) {
        if (error.message.includes("Ownable: caller is not the owner")) {
          console.log(`   ✅ 升级权限检查正常`);
        } else {
          console.log(`   ❌ 升级检查失败: ${error.message}`);
        }
      }
      
    } catch (error) {
      console.log(`   ❌ UUPS检查失败: ${error.message}`);
    }

    // 4. 检查新实现合约
    console.log("\n4️⃣ 检查新实现合约...");
    
    try {
      const newImpl = await ethers.getContractAt("AgentSystem", NEW_IMPLEMENTATION);
      
      // 检查新实现的基本信息
      const newProxiableUUID = await newImpl.proxiableUUID();
      console.log(`   新实现UUPS UUID: ${newProxiableUUID}`);
      
      // 检查新实现是否已初始化
      try {
        const newOwner = await newImpl.owner();
        console.log(`   新实现所有者: ${newOwner}`);
        console.log(`   ⚠️ 新实现可能已初始化，这可能导致升级失败`);
      } catch (error) {
        console.log(`   ✅ 新实现未初始化（正常）`);
      }
      
    } catch (error) {
      console.log(`   ❌ 检查新实现失败: ${error.message}`);
    }

    // 5. 尝试直接升级（如果当前账户是所有者）
    console.log("\n5️⃣ 尝试直接升级测试...");
    
    try {
      const [deployer] = await ethers.getSigners();
      console.log(`   测试账户: ${deployer.address}`);
      
      // 检查当前账户是否有权限
      const owner = await agentSystem.owner();
      if (owner.toLowerCase() === deployer.address.toLowerCase()) {
        console.log(`   ✅ 当前账户是所有者，可以直接升级`);
        
        // 询问是否执行直接升级
        console.log(`   🔧 可以运行直接升级脚本:`);
        console.log(`   npx hardhat run scripts/upgrade/agentsystem-direct-upgrade.js --network bscTestnet`);
        
      } else {
        console.log(`   ❌ 当前账户不是所有者，无法直接升级`);
        console.log(`   需要通过Timelock升级或转移所有权`);
      }
      
    } catch (error) {
      console.log(`   ❌ 权限检查失败: ${error.message}`);
    }

    // 6. 检查Timelock权限
    console.log("\n6️⃣ 检查Timelock权限...");
    
    try {
      // 检查Timelock是否有管理员权限
      const ADMIN_ROLE = await agentSystem.ADMIN_ROLE();
      const hasAdminRole = await agentSystem.hasRole(ADMIN_ROLE, TIMELOCK_ADDRESS);
      
      console.log(`   管理员角色: ${ADMIN_ROLE}`);
      console.log(`   Timelock有管理员权限: ${hasAdminRole ? '✅' : '❌'}`);
      
      // 检查默认管理员权限
      const DEFAULT_ADMIN_ROLE = "0x0000000000000000000000000000000000000000000000000000000000000000";
      const hasDefaultAdminRole = await agentSystem.hasRole(DEFAULT_ADMIN_ROLE, TIMELOCK_ADDRESS);
      console.log(`   Timelock有默认管理员权限: ${hasDefaultAdminRole ? '✅' : '❌'}`);
      
    } catch (error) {
      console.log(`   ❌ 检查Timelock权限失败: ${error.message}`);
    }

    // 7. 问题诊断和建议
    console.log("\n7️⃣ 问题诊断和建议...");
    console.log("=".repeat(50));
    
    console.log("\n🔍 委托调用失败的可能原因:");
    console.log("1. 新实现合约已经初始化");
    console.log("2. 新实现合约的构造函数有问题");
    console.log("3. 存储布局不兼容");
    console.log("4. 新实现合约的代码有bug");
    
    console.log("\n🔧 建议的解决方案:");
    console.log("1. 重新部署新实现合约（确保未初始化）");
    console.log("2. 检查新实现合约的构造函数");
    console.log("3. 使用直接升级方法（如果有权限）");
    console.log("4. 转移所有权给当前账户进行升级");
    
    console.log("\n📋 下一步操作:");
    console.log("1. 重新部署新实现合约:");
    console.log("   npx hardhat run scripts/upgrade/agentsystem-redeploy-implementation.js --network bscTestnet");
    console.log("");
    console.log("2. 或者尝试直接升级（如果有权限）:");
    console.log("   npx hardhat run scripts/upgrade/agentsystem-direct-upgrade.js --network bscTestnet");

  } catch (error) {
    console.error("❌ 检查失败:", error.message);
    console.error("详细错误:", error);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
