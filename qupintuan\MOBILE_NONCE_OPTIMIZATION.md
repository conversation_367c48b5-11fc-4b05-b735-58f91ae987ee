# 移动端Nonce优化系统

## 概述

本系统专门解决移动端钱包（特别是TP钱包）的Nonce冲突问题，通过智能检测和优化的交易处理，确保所有业务流程在移动端都能稳定执行。

## 核心特性

### 1. 自动移动端检测
系统会自动检测以下移动端环境：
- TP钱包 (TokenPocket)
- Trust钱包
- imToken钱包
- MathWallet
- 其他移动端浏览器

### 2. 智能Nonce管理
- 账户级别的Nonce缓存和管理
- 实时从区块链获取最新Nonce（包含pending交易）
- 交易队列控制，防止并发冲突
- 自动重试和错误恢复

### 3. 高优先级Gas策略
- 第一笔交易使用1.5倍Gas费
- 钱包特定的Gas优化配置
- 网络拥堵状态感知

### 4. 业务流程优化
支持以下业务流程的优化：
- 创建拼团房间（3次弹窗）
- 参与拼团房间（2次弹窗）
- 质押激活节点（2次弹窗）
- QPT回购房间（2次弹窗）

## 使用方法

### 自动启用
系统会自动检测移动端环境，无需手动配置。当检测到移动端钱包时，会自动使用优化的交易处理。

### 手动测试
可以通过以下方式测试优化系统：

```javascript
// 检查是否启用了优化
console.log('钱包类型:', window.ethereum?.isTokenPocket ? 'TP' : 'Unknown');
console.log('移动端检测:', /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent));
```

## 错误处理

### 常见错误及解决方案

1. **"创建失败：无法发送交易"**
   - 检查网络连接
   - 重启钱包应用
   - 确保钱包已解锁

2. **"Timed out while waiting for transaction"**
   - 网络拥堵，等待片刻重试
   - 检查Gas费设置
   - 确保交易未被取消

3. **"Nonce too low"**
   - 系统会自动处理
   - 如果持续出现，清理浏览器缓存

## 调试信息

### 控制台日志
系统会输出详细的调试信息，包括：
- 🔧 初始化信息
- 🔍 Nonce获取过程
- ⛽ Gas配置优化
- 📱 移动端检测结果
- 🚀 业务流程执行状态

### 关键日志标识
- `[UniversalNonceManager]` - Nonce管理相关
- `[TransactionQueue]` - 交易队列相关
- `[OptimizedBusinessFlows]` - 优化业务流程相关
- `[executeTransaction]` - 交易执行相关

## 性能监控

### 成功指标
- 交易成功率 > 95%
- Nonce冲突率 < 5%
- 平均交易确认时间 < 2分钟

### 监控方法
```javascript
// 获取系统状态
import { universalNonceManager } from '@/utils/universalNonceManager.js';

const status = universalNonceManager.getAccountStatus(userAddress);
console.log('账户状态:', status);
```

## 故障排除

### 步骤1：检查基础环境
1. 确认钱包应用已打开
2. 检查网络连接
3. 确认钱包已连接到正确网络

### 步骤2：清理缓存
```javascript
// 清理Nonce缓存
import { universalNonceManager } from '@/utils/universalNonceManager.js';
universalNonceManager.clearAccountCache(userAddress);
```

### 步骤3：重启钱包
如果问题持续，建议：
1. 关闭钱包应用
2. 清理浏览器缓存
3. 重新打开钱包应用
4. 重新连接网站

## 技术细节

### 文件结构
```
qupintuan/src/
├── utils/
│   ├── universalNonceManager.js    # 通用Nonce管理器
│   ├── transactionQueue.js         # 交易队列管理器
│   └── transactionHandler.js       # 优化的交易处理器
├── services/
│   ├── optimizedBusinessFlows.js   # 优化的业务流程
│   └── groupBuy/
│       ├── core.js                 # 核心业务逻辑（已集成优化）
│       └── transaction.js          # 交易处理（已集成优化）
└── apis/groupBuy/
    └── roomManagement.js           # API函数（已支持Nonce和Gas参数）
```

### 集成点
1. **创建拼团房间**: `core.js` -> `optimizedCreateGroupBuy`
2. **参与拼团房间**: `useGroupBuyActions.js` -> `optimizedJoinGroupBuy`
3. **交易执行**: `transaction.js` -> `createOptimizedSendTx`

## 更新日志

### v1.0.0 (当前版本)
- ✅ 通用Nonce管理器
- ✅ 交易队列管理器
- ✅ 高优先级Gas策略
- ✅ 移动端自动检测
- ✅ 业务流程优化
- ✅ 错误处理和重试机制

### 已知限制
1. 仅支持BSC测试网
2. 需要钱包支持EIP-1559（可选）
3. 依赖viem库版本 >= 2.0

## 支持

如果遇到问题，请：
1. 检查控制台日志
2. 记录错误信息
3. 提供钱包类型和操作步骤
4. 联系技术支持
