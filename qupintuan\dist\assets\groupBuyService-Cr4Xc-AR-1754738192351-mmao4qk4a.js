import{c as t}from"./index-DmsQauDE-1754738192351-rjh9jemju.js";import{E as j,R as k,d as E,h as L,e as U,f as g,r as v}from"./index-DmsQauDE-1754738192351-rjh9jemju.js";import{createAndLock as r,fetchTotalRooms as e,fetchRoom as c,joinRoom as n,claimReward as m}from"./core-sFjn417F-1754738192351-mmao4qk4a.js";import"./vendor-Caz4khA--1754738192351-mmao4qk4a.js";import"./web3-DJUNf-KT-1754738192351-mmao4qk4a.js";import"./basicOperations-CmXjWg1V-1754738192351-mmao4qk4a.js";import"./roomManagement-CRZ0InUC-1754738192351-mmao4qk4a.js";import"./rewardOperations-2IHLUMdh-1754738192351-mmao4qk4a.js";import"./transaction-D--126Ts-1754738192351-mmao4qk4a.js";async function h(o,a){return r(o,a)}async function y(o){return e(o)}async function S(o,a){return c(o,a)}async function T(o){return n(o)}async function w(o){return m(o)}async function $(o){return t.fetchRooms(o)}export{j as ERROR_CODES,k as ROOM_STATUS,E as calculateRemainingTime,L as canUserClaimReward,U as canUserJoinRoom,w as claimReward,h as createAndLock,t as default,S as fetchRoom,$ as fetchRooms,y as fetchTotalRooms,g as formatRoomStatus,T as joinRoom,t as newGroupBuyService,v as recalculateRoomStatus};
