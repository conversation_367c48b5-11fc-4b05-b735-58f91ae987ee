const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/web3-DJUNf-KT-1754738192351-mmao4qk4a.js","assets/vendor-Caz4khA--1754738192351-mmao4qk4a.js","assets/index-DmsQauDE-1754738192351-rjh9jemju.js","assets/index-Dcug_PHz-1754738192351-gaytee33w.css"])))=>i.map(i=>d[i]);
import{_ as i}from"./vendor-Caz4khA--1754738192351-mmao4qk4a.js";async function T(e,r=null){const t={environment:{},storage:{},contract:{},recommendations:[],severity:"info"};return t.environment=await f(),t.storage=await d(e),t.contract=await h(e),t.recommendations=w(t),t.severity=p(t),t}async function f(){const e={userAgent:navigator.userAgent,isMobile:!1,walletType:"unknown",browserType:"unknown",storageSupport:{},networkInfo:{}};return e.isMobile=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent),window.ethereum&&(window.ethereum.isTokenPocket?e.walletType="TokenPocket":window.ethereum.isTrust?e.walletType="TrustWallet":window.ethereum.isImToken?e.walletType="ImToken":window.ethereum.isMathWallet?e.walletType="MathWallet":window.ethereum.isMetaMask?e.walletType="MetaMask":e.walletType="Other"),navigator.userAgent.includes("Chrome")?e.browserType="Chrome":navigator.userAgent.includes("Safari")?e.browserType="Safari":navigator.userAgent.includes("Firefox")?e.browserType="Firefox":e.browserType="Other",e.storageSupport={localStorage:v(),sessionStorage:S(),indexedDB:_(),cookies:I()},navigator.connection&&(e.networkInfo={effectiveType:navigator.connection.effectiveType,downlink:navigator.connection.downlink,rtt:navigator.connection.rtt}),e}async function d(e){const r={localStorage:{available:!1,hasData:!1,data:null},sessionStorage:{available:!1,hasData:!1,data:null},memoryHash:{available:!1,hasData:!1,data:null},persistentStorage:{available:!1,hasData:!1,data:null},multiStorage:{available:!1,hasData:!1,data:null}};try{const t=localStorage.getItem(`lottery_${e}`);r.localStorage.available=!0,r.localStorage.hasData=!!t,r.localStorage.data=t?JSON.parse(t):null}catch(t){r.localStorage.error=t.message}try{const t=sessionStorage.getItem(`lottery_session_${e}`);r.sessionStorage.available=!0,r.sessionStorage.hasData=!!t,r.sessionStorage.data=t?JSON.parse(t):null}catch(t){r.sessionStorage.error=t.message}try{const t=window._lotteryHashData?.[e];r.memoryHash.available=!0,r.memoryHash.hasData=!!t,r.memoryHash.data=t}catch(t){r.memoryHash.error=t.message}try{const{getLotteryInfo:t}=await i(async()=>{const{getLotteryInfo:n}=await import("./lotteryInfoPersistence-BBzWo8Bu-1754738192351-mmao4qk4a.js");return{getLotteryInfo:n}},[]),o=t(e);r.persistentStorage.available=!0,r.persistentStorage.hasData=!!o,r.persistentStorage.data=o}catch(t){r.persistentStorage.error=t.message}try{const{loadLotteryInfo:t}=await i(async()=>{const{loadLotteryInfo:n}=await import("./lotteryStorage-DXlHLTdr-1754738192351-mmao4qk4a.js");return{loadLotteryInfo:n}},[]),o=await t(e);r.multiStorage.available=!0,r.multiStorage.hasData=!!o,r.multiStorage.data=o}catch(t){r.multiStorage.error=t.message}return r}async function h(e,r){const t={roomExists:!1,readyForWinner:!1,hasLotteryInfo:!1,lotteryInfo:null,roomData:null};try{const{createPublicClient:o,http:n}=await i(async()=>{const{createPublicClient:a,http:s}=await import("./web3-DJUNf-KT-1754738192351-mmao4qk4a.js").then(y=>y.x);return{createPublicClient:a,http:s}},__vite__mapDeps([0,1])),{bscTestnet:g}=await i(async()=>{const{bscTestnet:a}=await import("./web3-DJUNf-KT-1754738192351-mmao4qk4a.js").then(s=>s.A);return{bscTestnet:a}},__vite__mapDeps([0,1])),{getContractAddress:m}=await i(async()=>{const{getContractAddress:a}=await import("./index-DmsQauDE-1754738192351-rjh9jemju.js").then(s=>s.j);return{getContractAddress:a}},__vite__mapDeps([2,1,0,3])),{ABIS:l}=await i(async()=>{const{ABIS:a}=await import("./index-DmsQauDE-1754738192351-rjh9jemju.js").then(s=>s.k);return{ABIS:a}},__vite__mapDeps([2,1,0,3])),c=o({chain:g,transport:n()}),u=m(97,"GroupBuyRoom");try{const a=await c.readContract({address:u,abi:l.GroupBuyRoom,functionName:"rooms",args:[BigInt(e)]});t.roomExists=!0,t.roomData=a}catch(a){t.roomError=a.message}try{const a=await c.readContract({address:u,abi:l.GroupBuyRoom,functionName:"roomReadyForWinner",args:[BigInt(e)]});t.readyForWinner=a}catch(a){t.readyForWinnerError=a.message}try{const a=await c.readContract({address:u,abi:l.GroupBuyRoom,functionName:"getLotteryInfo",args:[BigInt(e)]});t.hasLotteryInfo=!0,t.lotteryInfo=a}catch(a){t.lotteryInfoError=a.message}}catch(o){t.generalError=o.message}return t}function w(e){const r=[];if(e.environment.isMobile&&e.environment.walletType==="TokenPocket"&&r.push({type:"environment",priority:"high",message:"检测到TP钱包移动端环境，建议使用PC端浏览器进行开奖操作"}),!Object.values(e.storage).some(o=>o.hasData)&&e.contract.readyForWinner&&r.push({type:"storage",priority:"critical",message:"第1步开奖数据丢失，但合约显示已准备验证赢家。建议从合约恢复数据"}),e.contract.hasLotteryInfo&&e.contract.lotteryInfo){const[,o,n]=e.contract.lotteryInfo;o!=="******************************************"&&n!=="******************************************000000000000000000000000"&&r.push({type:"contract",priority:"info",message:"合约中已有完整开奖信息，可以直接使用"})}return(e.environment.networkInfo?.effectiveType==="2g"||e.environment.networkInfo?.effectiveType==="slow-2g")&&r.push({type:"network",priority:"medium",message:"网络连接较慢，可能影响数据同步，建议在网络良好时重试"}),r}function p(e){const r=e.recommendations.filter(o=>o.priority==="critical"),t=e.recommendations.filter(o=>o.priority==="high");return r.length>0?"error":t.length>0?"warning":"info"}function v(){try{const e="test";return localStorage.setItem(e,e),localStorage.removeItem(e),!0}catch{return!1}}function S(){try{const e="test";return sessionStorage.setItem(e,e),sessionStorage.removeItem(e),!0}catch{return!1}}function _(){return"indexedDB"in window}function I(){return navigator.cookieEnabled}export{T as diagnoseMobileLotteryIssue};
