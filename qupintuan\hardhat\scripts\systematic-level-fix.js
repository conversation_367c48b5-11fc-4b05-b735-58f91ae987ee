// qupintuan/hardhat/scripts/systematic-level-fix.js
// 系统性修复所有用户等级问题

const { ethers } = require("hardhat");

async function main() {
  console.log("🔧 系统性修复用户等级问题...\n");

  // 合约地址（BSC测试网）
  const AGENT_SYSTEM_PROXY = "******************************************";

  try {
    // 获取签名者
    const [deployer] = await ethers.getSigners();
    console.log(`🔑 使用账户: ${deployer.address}`);
    
    // 连接到合约
    const agentSystem = await ethers.getContractAt("AgentSystem", AGENT_SYSTEM_PROXY);
    
    console.log(`📋 合约地址: ${AGENT_SYSTEM_PROXY}\n`);

    // 1. 检查管理员权限
    console.log("1️⃣ 检查管理员权限...");
    try {
      const ADMIN_ROLE = await agentSystem.ADMIN_ROLE();
      const hasAdminRole = await agentSystem.hasRole(ADMIN_ROLE, deployer.address);
      
      console.log(`   管理员角色: ${ADMIN_ROLE}`);
      console.log(`   当前账户权限: ${hasAdminRole ? '✅ 有管理员权限' : '❌ 无管理员权限'}`);
      
      if (!hasAdminRole) {
        console.log(`\n❌ 错误: 当前账户没有管理员权限，无法修复用户等级`);
        console.log(`   需要使用有管理员权限的账户来执行修复操作`);
        return;
      }
    } catch (error) {
      console.log(`❌ 权限检查失败: ${error.message}`);
      return;
    }

    // 2. 已知问题用户列表
    console.log("\n2️⃣ 处理已知问题用户...");
    const knownProblemUsers = [
      "0xDf98905098CB4e5D261578f600337eeeFd4082b3"
    ];

    const fixActions = [];

    for (const userAddress of knownProblemUsers) {
      console.log(`\n   📊 分析用户: ${userAddress}`);
      
      try {
        // 验证用户等级
        const validation = await agentSystem.validateUserLevel(userAddress);
        const isValid = validation[0];
        const upgradeType = Number(validation[1]);
        const expectedLevel = Number(validation[2]);
        const smallTeamsPerformance = Number(validation[3]) / 1000000;
        const validTeamsCount = Number(validation[4]);
        
        // 获取当前等级
        const userInfo = await agentSystem.getUserInfo(userAddress);
        const currentLevel = Number(userInfo[1]);
        const totalPerformance = Number(userInfo[2]) / 1000000;
        
        const upgradeTypeNames = ['未升级', '自动升级', '管理员设置'];
        
        console.log(`      - 当前等级: Level ${currentLevel}`);
        console.log(`      - 应有等级: Level ${expectedLevel}`);
        console.log(`      - 等级有效: ${isValid ? '✅' : '❌'}`);
        console.log(`      - 升级类型: ${upgradeTypeNames[upgradeType]}`);
        console.log(`      - 团队业绩: ${totalPerformance.toFixed(2)} USDT`);
        console.log(`      - 小团队业绩: ${smallTeamsPerformance.toFixed(2)} USDT`);
        console.log(`      - 有效团队数: ${validTeamsCount}`);
        
        if (!isValid && upgradeType !== 2) { // 不是管理员设置且等级无效
          if (currentLevel > expectedLevel) {
            console.log(`      🔧 需要降级: Level ${currentLevel} → Level ${expectedLevel}`);
            fixActions.push({
              user: userAddress,
              action: 'setLevel',
              from: currentLevel,
              to: expectedLevel,
              reason: `等级过高，小团队业绩 ${smallTeamsPerformance.toFixed(2)} USDT 不足`
            });
          } else if (currentLevel < expectedLevel) {
            console.log(`      🔧 需要升级: Level ${currentLevel} → Level ${expectedLevel}`);
            fixActions.push({
              user: userAddress,
              action: 'tryUpgrade',
              from: currentLevel,
              to: expectedLevel,
              reason: `等级过低，小团队业绩 ${smallTeamsPerformance.toFixed(2)} USDT 达标`
            });
          }
        } else if (isValid) {
          console.log(`      ✅ 等级正常，无需修复`);
        } else {
          console.log(`      ℹ️ 管理员设置的等级，无需验证业绩`);
        }
        
      } catch (error) {
        console.log(`      ❌ 分析用户失败: ${error.message}`);
      }
    }

    // 3. 执行修复操作
    console.log("\n3️⃣ 执行修复操作...");
    
    if (fixActions.length === 0) {
      console.log("   ✅ 没有需要修复的用户");
    } else {
      console.log(`   发现 ${fixActions.length} 个需要修复的用户\n`);
      
      for (let i = 0; i < fixActions.length; i++) {
        const action = fixActions[i];
        console.log(`   [${i + 1}/${fixActions.length}] 修复用户: ${action.user}`);
        console.log(`      操作: ${action.action} (Level ${action.from} → Level ${action.to})`);
        console.log(`      原因: ${action.reason}`);
        
        try {
          let tx;
          
          if (action.action === 'setLevel') {
            // 手动设置等级（通常用于降级）
            console.log(`      正在设置等级为 Level ${action.to}...`);
            tx = await agentSystem.setUserLevel(action.user, action.to);
          } else if (action.action === 'tryUpgrade') {
            // 尝试自动升级
            console.log(`      正在尝试自动升级...`);
            tx = await agentSystem.tryUpgrade(action.user);
          }
          
          if (tx) {
            console.log(`      ⏳ 交易已提交: ${tx.hash}`);
            const receipt = await tx.wait();
            console.log(`      ✅ 交易确认成功! Gas: ${receipt.gasUsed.toString()}`);
            
            // 验证修复结果
            const newUserInfo = await agentSystem.getUserInfo(action.user);
            const newLevel = Number(newUserInfo[1]);
            const newValidation = await agentSystem.validateUserLevel(action.user);
            const newIsValid = newValidation[0];
            const newUpgradeType = Number(newValidation[1]);
            
            console.log(`      验证结果:`);
            console.log(`        - 新等级: Level ${newLevel}`);
            console.log(`        - 等级有效: ${newIsValid ? '✅' : '❌'}`);
            console.log(`        - 升级类型: ${['未升级', '自动升级', '管理员设置'][newUpgradeType]}`);
            
            if (newIsValid) {
              console.log(`      🎉 修复成功！`);
            } else {
              console.log(`      ⚠️ 修复后仍有问题，需要进一步检查`);
            }
          }
          
        } catch (error) {
          console.log(`      ❌ 修复失败: ${error.message}`);
          
          if (error.message.includes('AccessControl')) {
            console.log(`         原因: 没有管理员权限`);
          } else if (error.message.includes('Invalid level')) {
            console.log(`         原因: 无效的等级值`);
          } else {
            console.log(`         详细错误: ${error}`);
          }
        }
        
        console.log(""); // 空行分隔
      }
    }

    // 4. 最终验证
    console.log("4️⃣ 最终验证所有用户...");
    
    for (const userAddress of knownProblemUsers) {
      console.log(`\n   📊 最终验证: ${userAddress}`);
      
      try {
        const validation = await agentSystem.validateUserLevel(userAddress);
        const isValid = validation[0];
        const upgradeType = Number(validation[1]);
        const expectedLevel = Number(validation[2]);
        
        const userInfo = await agentSystem.getUserInfo(userAddress);
        const currentLevel = Number(userInfo[1]);
        
        console.log(`      - 当前等级: Level ${currentLevel}`);
        console.log(`      - 应有等级: Level ${expectedLevel}`);
        console.log(`      - 等级有效: ${isValid ? '✅' : '❌'}`);
        console.log(`      - 升级类型: ${['未升级', '自动升级', '管理员设置'][upgradeType]}`);
        
        if (isValid) {
          console.log(`      ✅ 修复成功！用户等级现在正常`);
        } else {
          console.log(`      ❌ 仍有问题，可能需要手动处理`);
        }
        
      } catch (error) {
        console.log(`      ❌ 最终验证失败: ${error.message}`);
      }
    }

    // 5. 生成修复报告
    console.log("\n5️⃣ 生成修复报告...");
    console.log("=".repeat(60));
    
    const reportData = {
      timestamp: new Date().toISOString(),
      fixedUsers: fixActions.length,
      actions: fixActions,
      summary: {
        totalChecked: knownProblemUsers.length,
        totalFixed: fixActions.length,
        success: fixActions.filter(a => a.success !== false).length
      }
    };
    
    console.log(`\n📊 修复总结:`);
    console.log(`   检查用户数: ${knownProblemUsers.length}`);
    console.log(`   修复操作数: ${fixActions.length}`);
    console.log(`   修复成功数: ${reportData.summary.success}`);
    
    // 保存报告
    const fs = require('fs');
    const reportPath = `level-fix-report-${Date.now()}.json`;
    fs.writeFileSync(reportPath, JSON.stringify(reportData, null, 2));
    console.log(`\n📄 修复报告已保存到: ${reportPath}`);

    console.log("\n🎉 系统性修复完成！");
    console.log("\n📋 后续建议:");
    console.log("1. 定期运行批量验证脚本检查新问题");
    console.log("2. 监控等级变更事件确保系统正常");
    console.log("3. 完善前端显示升级类型信息");
    console.log("4. 建立自动化监控机制");

  } catch (error) {
    console.error("❌ 系统性修复失败:", error.message);
    console.error("详细错误:", error);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
