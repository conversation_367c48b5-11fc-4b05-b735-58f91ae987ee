// src/services/groupBuy/core.js
// 拼团核心业务逻辑

import { logDebug, logError } from '@/services/logger';
import { scheduleDataRefresh } from '@/utils/dataRefreshManager';
import { startTimer, endTimer, recordNetworkRequest } from '@/utils/performanceMonitor';
import { deduplicateRequest } from '@/utils/requestDeduplicator';
import { sendTx } from '@/utils/transactionHandler';

import {
  fetchTotalRooms as apiFetchTotalRooms,
  fetchRoom as apiFetchRoom,
  joinRoom as apiJoinRoom,
  claimReward as apiClaimReward,
  approveUSDT as apiApproveUSDT,
  closeRoom as apiCloseRoom
} from '@/apis/groupBuyApi';

// 导入新的三步操作函数
import {
  approveQPTForCreate,
  lockQPTForCreate,
  createRoomWithQPTVerification
} from '@/apis/groupBuy/roomManagement';

import { validateSigner, validateChainId, parseTierAmount, throwError } from './validation';
import { executeTransaction } from './transaction';
import { ERROR_CODES, ROOM_STATUS } from './constants';

/**
 * 创建拼团房间并锁定 QPT
 * @param {ethers.Signer} signer - 钱包签名者
 * @param {string} tierAmountStr - 拼团金额字符串（已格式化为 USDT 精度）
 * @returns {Promise<{receipt}>} - 返回交易收据
 */
export async function createAndLock(signer, tierAmountStr) {
  console.log('📞 [groupBuyService] 调用 createAndLock，参数:', { tierAmountStr });

  try {
    // 简化的移动端检测（减少加载时间）
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
                     window.ethereum?.isTokenPocket ||
                     window.ethereum?.isTrust ||
                     window.ethereum?.isImToken ||
                     window.ethereum?.isMathWallet;

    if (isMobile) {

      // 移动端预检查
      if (!window.ethereum) {
        throw new Error('未检测到钱包，请确保钱包应用已打开');
      }

      // 确保钱包连接正常
      try {
        await window.ethereum.request({ method: 'eth_requestAccounts' });
        // 移动端额外等待时间，确保钱包完全初始化
        await new Promise(resolve => setTimeout(resolve, 500)); // 减少等待时间
      } catch (error) {
        throw new Error('钱包连接失败，请检查钱包状态');
      }

      // 获取用户地址
      const userAddress = signer.account?.address;

      if (userAddress) {
        try {
          // 动态导入优化的业务流程
          const { optimizedCreateGroupBuy } = await import('@/services/optimizedBusinessFlows.js');

          return await optimizedCreateGroupBuy(signer, tierAmountStr);
        } catch (error) {
          // 如果优化流程失败，继续使用标准流程
        }
      }
    }

    // 验证参数
    validateSigner(signer);
    const tierAmountBigInt = parseTierAmount(tierAmountStr);

    // 获取链ID
    const chainId = await signer.getChainId();
    validateChainId(chainId);

    // 转换为数字，处理 USDT 精度格式
    const tierNum = Number(tierAmountBigInt) / 1000000; // USDT 有 6 位小数精度

    // 支持的档位：根据合约 ABI 中的 TIER_ 常量
    const supportedTiers = [30, 50, 100, 200, 500, 1000];
    if (!supportedTiers.includes(tierNum)) {
      throwError(ERROR_CODES.INVALID_TIER_AMOUNT, `不支持的拼团金额: ${tierNum}，支持的档位: ${supportedTiers.join(', ')} (原始值: ${tierAmountStr})`);
    }

    logDebug({
      component: 'groupBuyService',
      function: 'createAndLock',
      message: `开始创建拼团房间，金额: ${tierNum}`,
      tierNum,
      chainId
    });

    // 发起人固定三步操作流程（按正确的业务顺序）
    console.log('🚀 [createAndLock] 开始发起人固定三步操作流程');

    // 导入toast用于用户反馈
    const { toast } = await import('react-hot-toast');

    // 步骤 1/3: 创建拼团房间
    console.log('📞 [createAndLock] 步骤 1/3: 创建拼团房间');
    toast.loading('第1步：正在创建拼团房间...', { id: 'step1' });

    const createResult = await executeTransaction(
      () => createRoomWithQPTVerification({ chainId: 97, tier: tierNum, signer }),
      'createRoomWithQPTVerification',
      { isFirstTransaction: true } // 第一笔交易使用高优先级
    );

    console.log('✅ [createAndLock] 步骤 1/3: 拼团房间创建成功:', `房间ID: ${createResult.roomId}`);
    toast.success(`✅ 第1步完成：拼团房间 #${createResult.roomId} 创建成功！`, { id: 'step1' });

    // 步骤 2/3: 授权QPT给QPTLocker合约
    console.log('📞 [createAndLock] 步骤 2/3: 授权QPT给QPTLocker合约');
    toast.loading('第2步：正在授权QPT给锁仓合约...', { id: 'step2' });

    const approveResult = await executeTransaction(
      () => approveQPTForCreate({ chainId: 97, tier: tierNum, signer }),
      'approveQPTForCreate',
      { isFirstTransaction: false } // 第二笔交易
    );

    console.log('✅ [createAndLock] 步骤 2/3: QPT授权成功:', approveResult.message);
    toast.success(`✅ 第2步完成：QPT授权成功！`, { id: 'step2' });

    // 步骤 3/3: 锁仓QPT
    console.log('📞 [createAndLock] 步骤 3/3: 锁仓QPT');
    toast.loading('第3步：正在锁仓QPT...', { id: 'step3' });

    const lockResult = await executeTransaction(
      () => lockQPTForCreate({ chainId: 97, tier: tierNum, roomId: createResult.roomId, signer }),
      'lockQPTForCreate',
      { isFirstTransaction: false } // 第三笔交易
    );

    console.log('✅ [createAndLock] 步骤 3/3: QPT锁仓成功:', lockResult.message);
    toast.success(`✅ 第3步完成：QPT锁仓成功！`, { id: 'step3' });

    // 调试信息：检查 createResult 的结构
    console.log('🔍 [createAndLock] createResult 调试信息:', {
      createResult,
      hasRoomId: createResult?.roomId !== undefined,
      hasReceipt: createResult?.receipt !== undefined,
      roomIdValue: createResult?.roomId,
      receiptHash: createResult?.receipt?.hash
    });

    // 房间创建成功验证 - 修复 roomId 为 0 的问题
    if (!createResult || createResult.roomId === undefined || createResult.roomId === null || !createResult.receipt) {
      console.error('❌ [createAndLock] 创建结果验证失败:', {
        createResult,
        hasCreateResult: !!createResult,
        hasRoomId: createResult?.roomId !== undefined && createResult?.roomId !== null,
        hasReceipt: !!(createResult?.receipt),
        roomId: createResult?.roomId,
        roomIdType: typeof createResult?.roomId,
        receipt: createResult?.receipt
      });
      throwError(ERROR_CODES.CREATE_AND_LOCK_FAILED, '创建房间失败，未获取到有效的 roomId 或 receipt');
    }

    const { roomId, receipt } = createResult;

    // 验证QPT锁仓状态
    console.log('🔍 [createAndLock] 验证QPT锁仓状态...');
    try {
      const { validateCreatorQPTLock } = await import('@/apis/groupBuy/roomManagement.js');
      const qptValidation = await validateCreatorQPTLock({ chainId: 97, roomId });

      if (!qptValidation.isValid) {
        console.error('❌ [createAndLock] QPT锁仓验证失败:', qptValidation);
        throw new Error(`QPT锁仓验证失败: ${qptValidation.message}`);
      }

      console.log('✅ [createAndLock] QPT锁仓验证通过:', {
        roomId,
        lockedAmount: qptValidation.amount,
        creator: qptValidation.creator
      });
    } catch (validationError) {
      console.error('❌ [createAndLock] QPT锁仓验证异常:', validationError);
      // 验证失败不阻止流程，但记录错误
      toast.error(`⚠️ QPT锁仓验证失败: ${validationError.message}`, {
        duration: 8000,
        position: 'top-center',
      });
    }

    console.log('✅ [createAndLock] 发起人三步操作完成');

    // 清理可能的错误开奖信息（防止新房间显示开奖信息）
    try {
      const roomId = createResult.roomId;
      const lotteryKey = `lottery_${roomId}`;
      if (localStorage.getItem(lotteryKey)) {
        localStorage.removeItem(lotteryKey);
      }
    } catch (error) {
      // 静默处理清理错误
    }

    logDebug({
      component: 'groupBuyService',
      function: 'createAndLock',
      message: `发起人三步操作完成，房间ID: ${roomId}，交易哈希: ${receipt.hash}`,
      roomId,
      receipt,
      approveResult: approveResult.message,
      lockResult: lockResult.message
    });
    console.log('🎉 [createAndLock] 发起人固定三步操作完成，房间创建成功');

    // 显示最终成功提示
    toast.success(`🎉 拼团房间创建完成！房间ID: ${createResult.roomId}`, {
      duration: 6000,
      position: 'top-center',
      style: {
        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
        color: '#fff',
        fontWeight: 'bold',
        fontSize: '16px',
        padding: '16px 24px',
        borderRadius: '12px',
        boxShadow: '0 8px 32px rgba(0,0,0,0.3)',
      },
    });

    // 触发数据刷新
    scheduleDataRefresh('groupBuy', 5000);

    return {
      receipt,
      roomId,
      createResult: `房间ID: ${createResult.roomId}`,
      approveResult: approveResult.message,
      lockResult: lockResult.message,
      message: '发起人三步操作完成：创建房间 → 授权QPT → 锁仓QPT'
    };
  } catch (err) {
    logError({
      component: 'groupBuyService',
      function: 'createAndLock',
      message: `创建拼团房间失败: ${err.message}`,
      error: err,
      tierAmountStr
    });

    // 显示错误提示
    const { toast } = await import('react-hot-toast');

    // 清除所有步骤的loading状态
    toast.dismiss('step1');
    toast.dismiss('step2');
    toast.dismiss('step3');

    // 显示错误信息 - 移动端优化
    let errorMessage = '创建拼团房间失败';

    // 简化的移动端检测
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
                     window.ethereum?.isTokenPocket ||
                     window.ethereum?.isTrust ||
                     window.ethereum?.isImToken ||
                     window.ethereum?.isMathWallet;

    if (err.message) {
      if (err.message.includes('用户拒绝') || err.message.includes('User rejected')) {
        errorMessage = '用户取消了交易';
      } else if (err.message.includes('余额不足') || err.message.includes('insufficient funds')) {
        errorMessage = '余额不足，请检查BNB和QPT余额';
      } else if (err.message.includes('nonce too low')) {
        errorMessage = isMobile
          ? 'Nonce太低，请重启钱包应用后重试'
          : 'Nonce太低，请稍后重试';
      } else if (err.message.includes('网络') || err.message.includes('network') || err.message.includes('连接')) {
        errorMessage = isMobile
          ? '网络连接失败，请检查网络设置'
          : '网络连接失败，请检查网络设置';
      } else if (err.message.includes('钱包') || err.message.includes('Cannot read properties of undefined')) {
        errorMessage = isMobile
          ? '钱包连接问题，请重新连接钱包后重试'
          : '钱包连接问题，请重新连接钱包后重试';
      } else {
        errorMessage = `创建失败: ${err.message}`;
      }
    }

    toast.error(errorMessage, {
      duration: 6000,
      position: 'top-center',
    });

    throw err;
  }
}

/**
 * 获取房间总数
 * @param {number} chainId - 链ID
 * @returns {Promise<number>} - 房间总数
 */
export async function fetchTotalRooms(chainId) {
  const timerId = startTimer('fetchTotalRooms');
  
  try {
    validateChainId(chainId);
    
    const result = await deduplicateRequest(
      `fetchTotalRooms_${chainId}`,
      () => apiFetchTotalRooms({ chainId }),
      5000 // 5秒缓存
    );
    
    recordNetworkRequest('fetchTotalRooms', true);
    return result;
  } catch (error) {
    recordNetworkRequest('fetchTotalRooms', false);
    logError({
      component: 'groupBuyService',
      function: 'fetchTotalRooms',
      message: `获取房间总数失败: ${error.message}`,
      error,
      chainId
    });
    throw error;
  } finally {
    endTimer(timerId);
  }
}

/**
 * 获取房间信息
 * @param {number} chainId - 链ID
 * @param {number} roomId - 房间ID
 * @returns {Promise<Object>} - 房间信息
 */
export async function fetchRoom(chainId, roomId) {
  const timerId = startTimer('fetchRoom');
  
  try {
    validateChainId(chainId);
    
    if (!roomId || roomId <= 0) {
      throwError(ERROR_CODES.INVALID_ROOM_DATA, '无效的房间ID');
    }
    
    const result = await deduplicateRequest(
      `fetchRoom_${chainId}_${roomId}`,
      () => apiFetchRoom({ chainId, roomId }),
      3000 // 3秒缓存
    );
    
    recordNetworkRequest('fetchRoom', true);
    return result;
  } catch (error) {
    recordNetworkRequest('fetchRoom', false);
    logError({
      component: 'groupBuyService',
      function: 'fetchRoom',
      message: `获取房间信息失败: ${error.message}`,
      error,
      chainId,
      roomId
    });
    throw error;
  } finally {
    endTimer(timerId);
  }
}

/**
 * 加入房间
 * @param {Object} params - 参数对象
 * @param {ethers.Signer} params.signer - 钱包签名者
 * @param {number} params.chainId - 链ID
 * @param {number} params.roomId - 房间ID
 * @returns {Promise<Object>} - 交易结果
 */
export async function joinRoom({ signer, chainId, roomId }) {
  const timerId = startTimer('joinRoom');
  
  try {
    validateSigner(signer);
    validateChainId(chainId);
    
    if (!roomId || roomId <= 0) {
      throwError(ERROR_CODES.INVALID_ROOM_DATA, '无效的房间ID');
    }

    logDebug({
      component: 'groupBuyService',
      function: 'joinRoom',
      message: `开始加入房间: ${roomId}`,
      chainId,
      roomId
    });

    const result = await executeTransaction(
      () => apiJoinRoom({ signer, chainId, roomId }),
      'joinRoom'
    );

    recordNetworkRequest('joinRoom', true);
    
    // 触发数据刷新
    scheduleDataRefresh('groupBuy', 3000);
    
    return result;
  } catch (error) {
    recordNetworkRequest('joinRoom', false);
    logError({
      component: 'groupBuyService',
      function: 'joinRoom',
      message: `加入房间失败: ${error.message}`,
      error,
      chainId,
      roomId
    });
    throw error;
  } finally {
    endTimer(timerId);
  }
}

/**
 * 领取奖励
 * @param {Object} params - 参数对象
 * @param {ethers.Signer} params.signer - 钱包签名者
 * @param {number} params.chainId - 链ID
 * @param {number} params.roomId - 房间ID
 * @returns {Promise<Object>} - 交易结果
 */
export async function claimReward({ signer, chainId, roomId }) {
  const timerId = startTimer('claimReward');
  
  try {
    validateSigner(signer);
    validateChainId(chainId);
    
    if (!roomId || roomId <= 0) {
      throwError(ERROR_CODES.INVALID_ROOM_DATA, '无效的房间ID');
    }

    logDebug({
      component: 'groupBuyService',
      function: 'claimReward',
      message: `开始领取奖励，房间: ${roomId}`,
      chainId,
      roomId
    });

    const result = await executeTransaction(
      () => apiClaimReward({ signer, chainId, roomId }),
      'claimReward'
    );

    recordNetworkRequest('claimReward', true);
    
    // 触发数据刷新
    scheduleDataRefresh('groupBuy', 3000);
    
    return result;
  } catch (error) {
    recordNetworkRequest('claimReward', false);
    logError({
      component: 'groupBuyService',
      function: 'claimReward',
      message: `领取奖励失败: ${error.message}`,
      error,
      chainId,
      roomId
    });
    throw error;
  } finally {
    endTimer(timerId);
  }
}
