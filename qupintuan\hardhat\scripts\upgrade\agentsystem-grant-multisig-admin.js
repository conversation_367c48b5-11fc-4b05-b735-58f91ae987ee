// qupintuan/hardhat/scripts/upgrade/agentsystem-grant-multisig-admin.js
// 给多签钱包授予ADMIN_ROLE权限

const { ethers } = require("hardhat");

async function main() {
  console.log("🔑 给多签钱包授予ADMIN_ROLE权限...\n");

  const MULTISIG_ADDRESS = "******************************************";
  const AGENT_SYSTEM_PROXY = "******************************************";
  const SYSTEM_ADMIN = "******************************************";

  try {
    const [deployer] = await ethers.getSigners();
    console.log(`🔑 操作账户: ${deployer.address}\n`);

    console.log("📋 授权信息:");
    console.log(`   多签钱包: ${MULTISIG_ADDRESS}`);
    console.log(`   代理合约: ${AGENT_SYSTEM_PROXY}`);
    console.log(`   系统管理员: ${SYSTEM_ADMIN}`);
    console.log("");

    // 连接合约
    const agentSystem = await ethers.getContractAt("AgentSystem", AGENT_SYSTEM_PROXY);

    // 1. 检查当前权限状态
    console.log("1️⃣ 检查当前权限状态...");
    
    const ADMIN_ROLE = await agentSystem.ADMIN_ROLE();
    const DEFAULT_ADMIN_ROLE = "0x0000000000000000000000000000000000000000000000000000000000000000";
    
    console.log(`   管理员角色: ${ADMIN_ROLE}`);
    console.log(`   默认管理员角色: ${DEFAULT_ADMIN_ROLE}`);
    
    const multisigHasAdmin = await agentSystem.hasRole(ADMIN_ROLE, MULTISIG_ADDRESS);
    const deployerHasAdmin = await agentSystem.hasRole(ADMIN_ROLE, deployer.address);
    const deployerHasDefaultAdmin = await agentSystem.hasRole(DEFAULT_ADMIN_ROLE, deployer.address);
    const systemAdminHasAdmin = await agentSystem.hasRole(ADMIN_ROLE, SYSTEM_ADMIN);
    
    console.log(`   多签有管理员权限: ${multisigHasAdmin ? '✅' : '❌'}`);
    console.log(`   部署者有管理员权限: ${deployerHasAdmin ? '✅' : '❌'}`);
    console.log(`   部署者有默认管理员权限: ${deployerHasDefaultAdmin ? '✅' : '❌'}`);
    console.log(`   系统管理员有管理员权限: ${systemAdminHasAdmin ? '✅' : '❌'}`);

    if (multisigHasAdmin) {
      console.log("\n✅ 多签钱包已经有管理员权限，无需授权");
      return;
    }

    // 2. 检查当前账户是否有授权权限
    console.log("\n2️⃣ 检查授权权限...");
    
    if (!deployerHasAdmin && !deployerHasDefaultAdmin) {
      console.log("❌ 当前账户没有授权权限");
      console.log("需要使用有DEFAULT_ADMIN_ROLE权限的账户来执行授权");
      console.log("建议使用系统管理员账户或Timelock来执行");
      return;
    }

    // 3. 执行授权
    console.log("\n3️⃣ 执行授权...");
    
    try {
      console.log(`   正在给多签钱包授予ADMIN_ROLE权限...`);
      
      const grantTx = await agentSystem.grantRole(ADMIN_ROLE, MULTISIG_ADDRESS);
      console.log(`   ⏳ 授权交易已提交: ${grantTx.hash}`);
      
      const receipt = await grantTx.wait();
      console.log(`   ✅ 授权交易确认成功! Gas使用: ${receipt.gasUsed.toString()}`);
      
      // 验证授权结果
      const newMultisigHasAdmin = await agentSystem.hasRole(ADMIN_ROLE, MULTISIG_ADDRESS);
      
      if (newMultisigHasAdmin) {
        console.log(`   🎉 授权成功！多签钱包现在有ADMIN_ROLE权限`);
      } else {
        console.log(`   ❌ 授权失败，多签钱包仍然没有权限`);
      }
      
    } catch (error) {
      console.log(`   ❌ 授权失败: ${error.message}`);
      
      if (error.message.includes("AccessControl")) {
        console.log(`   原因: 当前账户没有授权权限`);
      } else {
        console.log(`   原因: 未知错误`);
      }
      throw error;
    }

    // 4. 现在尝试升级
    console.log("\n4️⃣ 现在尝试通过多签升级...");
    
    const NEW_IMPLEMENTATION = "******************************************";
    
    // 准备升级调用数据
    const upgradeInterface = new ethers.Interface([
      "function upgradeTo(address newImplementation)"
    ]);
    
    const upgradeCalldata = upgradeInterface.encodeFunctionData("upgradeTo", [
      NEW_IMPLEMENTATION
    ]);
    
    console.log(`   新实现地址: ${NEW_IMPLEMENTATION}`);
    console.log(`   升级调用数据: ${upgradeCalldata}`);
    
    // 连接多签钱包
    const multisig = await ethers.getContractAt("MultiSigWallet", MULTISIG_ADDRESS);
    
    try {
      const submitTx = await multisig.submitTransaction(
        AGENT_SYSTEM_PROXY,
        0,
        upgradeCalldata
      );
      
      console.log(`   ⏳ 新升级提案已提交: ${submitTx.hash}`);
      const receipt = await submitTx.wait();
      console.log(`   ✅ 新升级提案确认成功!`);
      
      // 获取交易ID
      const submitEvents = receipt.logs.filter(log => {
        try {
          const parsed = multisig.interface.parseLog(log);
          return parsed.name === 'SubmitTransaction';
        } catch {
          return false;
        }
      });
      
      if (submitEvents.length > 0) {
        const parsed = multisig.interface.parseLog(submitEvents[0]);
        const transactionId = parsed.args.txIndex;
        console.log(`   📝 新升级提案ID: ${transactionId}`);
        
        console.log(`\n🔐 下一步操作:`);
        console.log(`   1. 登录多签钱包管理界面`);
        console.log(`   2. 确认并执行提案ID: ${transactionId}`);
        console.log(`   3. 这次应该会成功，因为多签钱包现在有权限了`);
        
      }
      
    } catch (error) {
      console.log(`   ❌ 提交新升级提案失败: ${error.message}`);
    }

    // 5. 保存授权信息
    console.log("\n5️⃣ 保存授权信息...");
    
    const authInfo = {
      timestamp: new Date().toISOString(),
      network: "bscTestnet",
      multisigAddress: MULTISIG_ADDRESS,
      proxyAddress: AGENT_SYSTEM_PROXY,
      adminRole: ADMIN_ROLE,
      authorizer: deployer.address,
      success: true
    };
    
    const fs = require('fs');
    const filename = `agentsystem-multisig-authorization-${Date.now()}.json`;
    fs.writeFileSync(filename, JSON.stringify(authInfo, null, 2));
    console.log(`   ✅ 授权信息已保存到: ${filename}`);

    console.log("\n🎉 多签钱包授权完成！");
    console.log("现在可以通过多签钱包成功执行升级了");

  } catch (error) {
    console.error("❌ 授权失败:", error.message);
    console.error("详细错误:", error);
    process.exit(1);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
