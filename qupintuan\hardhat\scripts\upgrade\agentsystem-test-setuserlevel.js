// qupintuan/hardhat/scripts/upgrade/agentsystem-test-setuserlevel.js
// 测试setUserLevel函数是否可用

const { ethers } = require("hardhat");

async function main() {
  console.log("🧪 测试setUserLevel函数是否可用...\n");

  const AGENT_SYSTEM_PROXY = "******************************************";
  const TEST_USER = "******************************************";

  try {
    console.log("📋 测试信息:");
    console.log(`   代理合约: ${AGENT_SYSTEM_PROXY}`);
    console.log(`   测试用户: ${TEST_USER}`);
    console.log("");

    // 1. 尝试直接调用setUserLevel函数
    console.log("1️⃣ 尝试直接调用setUserLevel函数...");
    
    try {
      // 构建setUserLevel调用数据
      const setUserLevelInterface = new ethers.Interface([
        "function setUserLevel(address user, uint8 level)"
      ]);
      
      const calldata = setUserLevelInterface.encodeFunctionData("setUserLevel", [
        TEST_USER,
        0
      ]);
      
      console.log(`   函数选择器: ${calldata.slice(0, 10)}`);
      console.log(`   调用数据: ${calldata}`);
      
      // 尝试静态调用
      const [deployer] = await ethers.getSigners();
      
      try {
        const result = await ethers.provider.call({
          to: AGENT_SYSTEM_PROXY,
          data: calldata,
          from: deployer.address
        });
        console.log(`   ✅ 静态调用成功: ${result}`);
      } catch (error) {
        console.log(`   ❌ 静态调用失败: ${error.message}`);
        
        if (error.message.includes("AccessControl")) {
          console.log("   原因: 权限不足（函数存在但无权限）");
        } else if (error.message.includes("function selector was not recognized")) {
          console.log("   原因: 函数不存在");
        }
      }
      
    } catch (error) {
      console.log(`   ❌ 调用数据构建失败: ${error.message}`);
    }

    // 2. 检查当前用户状态
    console.log("\n2️⃣ 检查当前用户状态...");
    
    try {
      // 使用基本的call方法
      const getUserInfoInterface = new ethers.Interface([
        "function getUserInfo(address user) view returns (address inviter, uint8 level, uint256 totalPerformance, uint256 referralsCount, bool isRegistered, uint256 personalPerformance, uint8 upgradeType, uint256 upgradeTimestamp)"
      ]);
      
      const getUserInfoCalldata = getUserInfoInterface.encodeFunctionData("getUserInfo", [TEST_USER]);
      
      const userInfoResult = await ethers.provider.call({
        to: AGENT_SYSTEM_PROXY,
        data: getUserInfoCalldata
      });
      
      const decodedUserInfo = getUserInfoInterface.decodeFunctionResult("getUserInfo", userInfoResult);
      
      console.log(`   当前等级: Level ${decodedUserInfo[1]}`);
      console.log(`   团队业绩: ${ethers.formatUnits(decodedUserInfo[2], 6)} USDT`);
      console.log(`   升级类型: ${['未升级', '自动升级', '管理员设置'][Number(decodedUserInfo[6])]}`);
      
    } catch (error) {
      console.log(`   ❌ 获取用户信息失败: ${error.message}`);
    }

    // 3. 检查validateUserLevel函数
    console.log("\n3️⃣ 检查validateUserLevel函数...");
    
    try {
      const validateInterface = new ethers.Interface([
        "function validateUserLevel(address user) view returns (bool isValid, uint8 upgradeType, uint8 expectedLevel, uint256 smallTeamsPerformance, uint256 validTeamsCount)"
      ]);
      
      const validateCalldata = validateInterface.encodeFunctionData("validateUserLevel", [TEST_USER]);
      
      const validateResult = await ethers.provider.call({
        to: AGENT_SYSTEM_PROXY,
        data: validateCalldata
      });
      
      const decodedValidation = validateInterface.decodeFunctionResult("validateUserLevel", validateResult);
      
      console.log(`   等级有效: ${decodedValidation[0] ? '✅' : '❌'}`);
      console.log(`   应有等级: Level ${decodedValidation[2]}`);
      console.log(`   小团队业绩: ${ethers.formatUnits(decodedValidation[3], 6)} USDT`);
      
    } catch (error) {
      console.log(`   ❌ 验证用户等级失败: ${error.message}`);
    }

    // 4. 检查权限
    console.log("\n4️⃣ 检查权限...");
    
    try {
      const [deployer] = await ethers.getSigners();
      
      // 检查ADMIN_ROLE
      const hasRoleInterface = new ethers.Interface([
        "function hasRole(bytes32 role, address account) view returns (bool)",
        "function ADMIN_ROLE() view returns (bytes32)"
      ]);
      
      // 获取ADMIN_ROLE
      const adminRoleCalldata = hasRoleInterface.encodeFunctionData("ADMIN_ROLE", []);
      const adminRoleResult = await ethers.provider.call({
        to: AGENT_SYSTEM_PROXY,
        data: adminRoleCalldata
      });
      const adminRole = hasRoleInterface.decodeFunctionResult("ADMIN_ROLE", adminRoleResult)[0];
      
      console.log(`   ADMIN_ROLE: ${adminRole}`);
      
      // 检查当前账户权限
      const hasRoleCalldata = hasRoleInterface.encodeFunctionData("hasRole", [adminRole, deployer.address]);
      const hasRoleResult = await ethers.provider.call({
        to: AGENT_SYSTEM_PROXY,
        data: hasRoleCalldata
      });
      const hasRole = hasRoleInterface.decodeFunctionResult("hasRole", hasRoleResult)[0];
      
      console.log(`   当前账户 ${deployer.address} 有ADMIN权限: ${hasRole ? '✅' : '❌'}`);
      
      // 检查Timelock权限
      const TIMELOCK_ADDRESS = "******************************************";
      const timelockHasRoleCalldata = hasRoleInterface.encodeFunctionData("hasRole", [adminRole, TIMELOCK_ADDRESS]);
      const timelockHasRoleResult = await ethers.provider.call({
        to: AGENT_SYSTEM_PROXY,
        data: timelockHasRoleCalldata
      });
      const timelockHasRole = hasRoleInterface.decodeFunctionResult("hasRole", timelockHasRoleResult)[0];
      
      console.log(`   Timelock ${TIMELOCK_ADDRESS} 有ADMIN权限: ${timelockHasRole ? '✅' : '❌'}`);
      
    } catch (error) {
      console.log(`   ❌ 权限检查失败: ${error.message}`);
    }

    // 5. 尝试通过Timelock修复
    console.log("\n5️⃣ 准备通过Timelock修复用户...");
    
    const TIMELOCK_ADDRESS = "******************************************";
    const MULTISIG_ADDRESS = "******************************************";
    
    console.log("   由于Timelock有ADMIN权限，我们可以通过Timelock来修复用户");
    console.log("   需要通过多签钱包提交Timelock操作");
    
    // 准备修复两个用户的调用数据
    const problemUsers = [
      { address: "******************************************", targetLevel: 0 },
      { address: "******************************************", targetLevel: 0 }
    ];
    
    const setUserLevelInterface = new ethers.Interface([
      "function setUserLevel(address user, uint8 level)"
    ]);
    
    console.log("\n   修复用户的调用数据:");
    
    for (let i = 0; i < problemUsers.length; i++) {
      const user = problemUsers[i];
      const calldata = setUserLevelInterface.encodeFunctionData("setUserLevel", [
        user.address,
        user.targetLevel
      ]);
      
      console.log(`\n   用户 ${i + 1}: ${user.address}`);
      console.log(`     目标等级: Level ${user.targetLevel}`);
      console.log(`     调用数据: ${calldata}`);
      console.log(`     目标合约: ${AGENT_SYSTEM_PROXY}`);
      console.log(`     执行者: ${TIMELOCK_ADDRESS} (通过多签)`);
    }

    // 6. 解决方案
    console.log("\n6️⃣ 解决方案...");
    console.log("=".repeat(50));
    
    console.log("\n🔧 推荐方案: 通过Timelock修复用户");
    console.log("1. setUserLevel函数存在且可用");
    console.log("2. Timelock有ADMIN_ROLE权限");
    console.log("3. 需要通过多签钱包提交Timelock操作");
    
    console.log("\n📋 具体步骤:");
    console.log("1. 为每个用户创建Timelock schedule和execute提案");
    console.log("2. 通过多签钱包执行这些提案");
    console.log("3. 等待延迟期后执行修复");
    
    console.log("\n🚀 下一步:");
    console.log("运行Timelock修复脚本:");
    console.log("npx hardhat run scripts/upgrade/agentsystem-timelock-fix-users.js --network bscTestnet");

  } catch (error) {
    console.error("❌ 测试失败:", error.message);
    console.error("详细错误:", error);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
