// qupintuan/hardhat/scripts/batch-validate-user-levels.js
// 批量验证用户等级，查找所有可能受影响的用户

const { ethers } = require("hardhat");

async function main() {
  console.log("🔍 批量验证用户等级，查找问题用户...\n");

  // 合约地址（BSC测试网）
  const AGENT_SYSTEM_PROXY = "******************************************";

  try {
    // 连接到合约
    const agentSystem = await ethers.getContractAt("AgentSystem", AGENT_SYSTEM_PROXY);
    
    console.log(`📋 合约地址: ${AGENT_SYSTEM_PROXY}\n`);

    // 1. 获取所有注册用户的地址
    console.log("1️⃣ 收集用户地址...");
    
    // 由于没有直接获取所有用户的方法，我们需要通过事件来收集
    // 这里我们先检查一些已知的用户地址
    const knownUsers = [
      "******************************************", // 已知问题用户
      "******************************************", // 系统管理员
      "0xA2dD...F9EF", // 示例用户1
      "0xB3d9...3a1D", // 示例用户2
      "0x9654...8084", // 示例用户3
    ];

    // 从事件中获取更多用户地址
    console.log("   正在从事件中收集用户地址...");
    
    let allUsers = new Set(knownUsers);
    
    try {
      // 获取最近的性能添加事件
      const performanceEvents = await agentSystem.queryFilter(
        agentSystem.filters.PerformanceAdded(),
        -10000 // 最近10000个区块
      );
      
      performanceEvents.forEach(event => {
        if (event.args && event.args.user) {
          allUsers.add(event.args.user);
        }
      });
      
      console.log(`   从性能事件中发现 ${performanceEvents.length} 个事件`);
      
    } catch (error) {
      console.log(`   ⚠️ 获取事件失败: ${error.message}`);
    }

    try {
      // 获取等级更新事件
      const levelEvents = await agentSystem.queryFilter(
        agentSystem.filters.LevelUpdated(),
        -10000 // 最近10000个区块
      );
      
      levelEvents.forEach(event => {
        if (event.args && event.args.user) {
          allUsers.add(event.args.user);
        }
      });
      
      console.log(`   从等级事件中发现 ${levelEvents.length} 个事件`);
      
    } catch (error) {
      console.log(`   ⚠️ 获取等级事件失败: ${error.message}`);
    }

    const userList = Array.from(allUsers).filter(addr => 
      addr && addr !== ethers.ZeroAddress && ethers.isAddress(addr)
    );
    
    console.log(`   总共收集到 ${userList.length} 个用户地址\n`);

    // 2. 批量验证用户等级
    console.log("2️⃣ 批量验证用户等级...");
    
    const problemUsers = [];
    const validUsers = [];
    const adminSetUsers = [];
    
    for (let i = 0; i < userList.length; i++) {
      const userAddress = userList[i];
      
      try {
        console.log(`   [${i + 1}/${userList.length}] 验证用户: ${userAddress}`);
        
        // 获取用户基本信息
        const userInfo = await agentSystem.getUserInfo(userAddress);
        const currentLevel = Number(userInfo[1]);
        const totalPerformance = Number(userInfo[2]) / 1000000; // 转换为USDT
        const isRegistered = userInfo[4];
        
        // 跳过未注册用户
        if (!isRegistered) {
          console.log(`      ⏭️ 跳过未注册用户`);
          continue;
        }
        
        // 跳过0级用户（除非有异常）
        if (currentLevel === 0) {
          console.log(`      ✅ Level 0 用户，跳过`);
          continue;
        }
        
        // 验证等级
        const validation = await agentSystem.validateUserLevel(userAddress);
        const isValid = validation[0];
        const upgradeType = Number(validation[1]);
        const expectedLevel = Number(validation[2]);
        const smallTeamsPerformance = Number(validation[3]) / 1000000;
        
        const upgradeTypeNames = ['未升级', '自动升级', '管理员设置'];
        
        console.log(`      - 当前等级: Level ${currentLevel}`);
        console.log(`      - 应有等级: Level ${expectedLevel}`);
        console.log(`      - 等级有效: ${isValid ? '✅' : '❌'}`);
        console.log(`      - 升级类型: ${upgradeTypeNames[upgradeType]}`);
        console.log(`      - 团队业绩: ${totalPerformance.toFixed(2)} USDT`);
        console.log(`      - 小团队业绩: ${smallTeamsPerformance.toFixed(2)} USDT`);
        
        // 分类用户
        if (upgradeType === 2) { // 管理员设置
          adminSetUsers.push({
            address: userAddress,
            currentLevel,
            expectedLevel,
            upgradeType,
            totalPerformance,
            smallTeamsPerformance,
            isValid
          });
          console.log(`      📝 管理员设置的等级`);
        } else if (!isValid) { // 等级无效
          problemUsers.push({
            address: userAddress,
            currentLevel,
            expectedLevel,
            upgradeType,
            totalPerformance,
            smallTeamsPerformance,
            isValid,
            problem: currentLevel > expectedLevel ? 'OVER_LEVEL' : 'UNDER_LEVEL'
          });
          console.log(`      🚨 发现问题用户！`);
        } else { // 等级正常
          validUsers.push({
            address: userAddress,
            currentLevel,
            expectedLevel,
            upgradeType,
            totalPerformance,
            smallTeamsPerformance,
            isValid
          });
          console.log(`      ✅ 等级正常`);
        }
        
      } catch (error) {
        console.log(`      ❌ 验证失败: ${error.message}`);
      }
      
      console.log(""); // 空行分隔
    }

    // 3. 生成报告
    console.log("3️⃣ 生成验证报告...");
    console.log("=".repeat(60));
    
    console.log(`\n📊 验证统计:`);
    console.log(`   总验证用户数: ${userList.length}`);
    console.log(`   等级正常用户: ${validUsers.length}`);
    console.log(`   问题用户数量: ${problemUsers.length}`);
    console.log(`   管理员设置用户: ${adminSetUsers.length}`);
    
    // 4. 详细问题用户报告
    if (problemUsers.length > 0) {
      console.log(`\n🚨 问题用户详细报告:`);
      console.log("-".repeat(60));
      
      problemUsers.forEach((user, index) => {
        console.log(`\n${index + 1}. ${user.address}`);
        console.log(`   当前等级: Level ${user.currentLevel}`);
        console.log(`   应有等级: Level ${user.expectedLevel}`);
        console.log(`   问题类型: ${user.problem === 'OVER_LEVEL' ? '等级过高' : '等级过低'}`);
        console.log(`   升级类型: ${['未升级', '自动升级', '管理员设置'][user.upgradeType]}`);
        console.log(`   团队业绩: ${user.totalPerformance.toFixed(2)} USDT`);
        console.log(`   小团队业绩: ${user.smallTeamsPerformance.toFixed(2)} USDT`);
        
        if (user.problem === 'OVER_LEVEL') {
          const requiredPerformance = [0, 5000, 30000, 100000, 500000][user.currentLevel];
          const deficit = requiredPerformance - user.smallTeamsPerformance;
          console.log(`   业绩缺口: ${deficit.toFixed(2)} USDT`);
        }
      });
    }
    
    // 5. 管理员设置用户报告
    if (adminSetUsers.length > 0) {
      console.log(`\n📝 管理员设置用户报告:`);
      console.log("-".repeat(60));
      
      adminSetUsers.forEach((user, index) => {
        console.log(`\n${index + 1}. ${user.address}`);
        console.log(`   当前等级: Level ${user.currentLevel}`);
        console.log(`   团队业绩: ${user.totalPerformance.toFixed(2)} USDT`);
        console.log(`   小团队业绩: ${user.smallTeamsPerformance.toFixed(2)} USDT`);
        console.log(`   状态: 管理员手动设置，无需验证业绩`);
      });
    }
    
    // 6. 修复建议
    console.log(`\n🔧 修复建议:`);
    console.log("-".repeat(60));
    
    if (problemUsers.length === 0) {
      console.log("✅ 未发现问题用户，系统状态正常");
    } else {
      console.log(`发现 ${problemUsers.length} 个问题用户，建议执行以下修复操作:`);
      console.log("");
      
      const overLevelUsers = problemUsers.filter(u => u.problem === 'OVER_LEVEL');
      const underLevelUsers = problemUsers.filter(u => u.problem === 'UNDER_LEVEL');
      
      if (overLevelUsers.length > 0) {
        console.log(`1. 降级用户 (${overLevelUsers.length} 个):`);
        overLevelUsers.forEach(user => {
          console.log(`   ${user.address}: Level ${user.currentLevel} → Level ${user.expectedLevel}`);
        });
        console.log(`   执行命令: npx hardhat run scripts/fix-invalid-user-level.js --network bscTestnet`);
        console.log("");
      }
      
      if (underLevelUsers.length > 0) {
        console.log(`2. 升级用户 (${underLevelUsers.length} 个):`);
        underLevelUsers.forEach(user => {
          console.log(`   ${user.address}: Level ${user.currentLevel} → Level ${user.expectedLevel}`);
        });
        console.log(`   执行命令: npx hardhat run scripts/upgrade-eligible-users.js --network bscTestnet`);
        console.log("");
      }
    }
    
    // 7. 保存报告到文件
    const reportData = {
      timestamp: new Date().toISOString(),
      totalUsers: userList.length,
      validUsers: validUsers.length,
      problemUsers: problemUsers.length,
      adminSetUsers: adminSetUsers.length,
      problemUserDetails: problemUsers,
      adminSetUserDetails: adminSetUsers
    };
    
    const fs = require('fs');
    const reportPath = `user-level-validation-report-${Date.now()}.json`;
    fs.writeFileSync(reportPath, JSON.stringify(reportData, null, 2));
    console.log(`📄 详细报告已保存到: ${reportPath}`);

  } catch (error) {
    console.error("❌ 批量验证失败:", error.message);
    console.error("详细错误:", error);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
