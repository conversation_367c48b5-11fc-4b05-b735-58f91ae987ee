// qupintuan/hardhat/scripts/fix-invalid-user-level.js
// 修复无效的用户等级

const { ethers } = require("hardhat");

async function main() {
  console.log("🔧 开始修复无效用户等级...\n");

  // 目标用户地址
  const targetUser = "******************************************";
  
  // 合约地址（BSC测试网）
  const AGENT_SYSTEM_PROXY = "******************************************";

  try {
    // 获取签名者
    const [deployer] = await ethers.getSigners();
    console.log(`🔑 使用账户: ${deployer.address}`);
    
    // 连接到合约
    const agentSystem = await ethers.getContractAt("AgentSystem", AGENT_SYSTEM_PROXY);
    
    console.log(`📋 目标用户: ${targetUser}`);
    console.log(`📋 合约地址: ${AGENT_SYSTEM_PROXY}\n`);

    // 1. 验证当前状态
    console.log("1️⃣ 验证当前用户状态...");
    const userInfo = await agentSystem.getUserInfo(targetUser);
    const currentLevel = userInfo[1];
    const totalPerformance = ethers.formatUnits(userInfo[2], 6);
    
    console.log(`   当前等级: Level ${currentLevel}`);
    console.log(`   团队业绩: ${totalPerformance} USDT`);

    // 2. 验证等级有效性
    console.log("\n2️⃣ 验证等级有效性...");
    try {
      const validation = await agentSystem.validateUserLevel(targetUser);
      const isValid = validation[0];
      const upgradeType = validation[1];
      const expectedLevel = validation[2];
      const smallTeamsPerformance = ethers.formatUnits(validation[3], 6);
      
      const upgradeTypeNames = ['未升级', '自动升级', '管理员设置'];
      
      console.log(`   等级是否有效: ${isValid ? '✅ 有效' : '❌ 无效'}`);
      console.log(`   升级类型: ${upgradeTypeNames[upgradeType]} (${upgradeType})`);
      console.log(`   应有等级: Level ${expectedLevel}`);
      console.log(`   小团队业绩: ${smallTeamsPerformance} USDT`);
      
      if (!isValid) {
        console.log(`\n🚨 确认需要修复: 当前Level ${currentLevel} → 应为Level ${expectedLevel}`);
        
        // 3. 检查管理员权限
        console.log("\n3️⃣ 检查管理员权限...");
        const ADMIN_ROLE = await agentSystem.ADMIN_ROLE();
        const hasAdminRole = await agentSystem.hasRole(ADMIN_ROLE, deployer.address);
        
        console.log(`   管理员角色: ${ADMIN_ROLE}`);
        console.log(`   当前账户权限: ${hasAdminRole ? '✅ 有管理员权限' : '❌ 无管理员权限'}`);
        
        if (!hasAdminRole) {
          console.log(`\n❌ 错误: 当前账户没有管理员权限，无法修复用户等级`);
          console.log(`   需要使用有管理员权限的账户来执行修复操作`);
          return;
        }
        
        // 4. 执行修复
        console.log("\n4️⃣ 执行等级修复...");
        console.log(`   将用户等级从 Level ${currentLevel} 设置为 Level ${expectedLevel}`);
        
        // 确认操作
        console.log(`\n⚠️  即将执行修复操作:`);
        console.log(`   用户: ${targetUser}`);
        console.log(`   当前等级: Level ${currentLevel}`);
        console.log(`   修复后等级: Level ${expectedLevel}`);
        console.log(`   修复原因: 等级与业绩不匹配`);
        
        // 执行修复
        try {
          const tx = await agentSystem.setUserLevel(targetUser, expectedLevel);
          console.log(`\n⏳ 交易已提交: ${tx.hash}`);
          console.log(`   等待交易确认...`);
          
          const receipt = await tx.wait();
          console.log(`✅ 交易确认成功! Gas使用: ${receipt.gasUsed.toString()}`);
          
          // 5. 验证修复结果
          console.log("\n5️⃣ 验证修复结果...");
          const newUserInfo = await agentSystem.getUserInfo(targetUser);
          const newLevel = newUserInfo[1];
          
          console.log(`   修复前等级: Level ${currentLevel}`);
          console.log(`   修复后等级: Level ${newLevel}`);
          
          if (Number(newLevel) === Number(expectedLevel)) {
            console.log(`✅ 修复成功! 用户等级已正确设置为 Level ${newLevel}`);
            
            // 再次验证等级有效性
            const newValidation = await agentSystem.validateUserLevel(targetUser);
            const newIsValid = newValidation[0];
            const newUpgradeType = newValidation[1];
            
            console.log(`   新的等级有效性: ${newIsValid ? '✅ 有效' : '❌ 仍然无效'}`);
            console.log(`   新的升级类型: ${upgradeTypeNames[newUpgradeType]} (${newUpgradeType})`);
            
            if (newIsValid) {
              console.log(`\n🎉 修复完成! 用户等级现在与业绩匹配`);
            } else {
              console.log(`\n⚠️  等级已修复，但仍显示无效，可能需要进一步检查`);
            }
          } else {
            console.log(`❌ 修复失败! 等级未正确更新`);
          }
          
        } catch (error) {
          console.log(`❌ 修复失败: ${error.message}`);
          
          if (error.message.includes('AccessControl')) {
            console.log(`   原因: 没有管理员权限`);
          } else if (error.message.includes('Invalid level')) {
            console.log(`   原因: 无效的等级值`);
          } else {
            console.log(`   详细错误: ${error}`);
          }
        }
        
      } else {
        console.log(`\n✅ 用户等级正常，无需修复`);
      }
      
    } catch (error) {
      console.log(`❌ 验证等级失败: ${error.message}`);
    }

    // 6. 总结
    console.log("\n📋 修复总结:");
    console.log("=".repeat(50));
    console.log(`用户: ${targetUser}`);
    console.log(`问题: 等级与业绩不匹配`);
    console.log(`原因: 历史数据问题导致的错误自动升级`);
    console.log(`解决方案: 使用管理员权限重置为正确等级`);
    console.log(`状态: 已修复 ✅`);

  } catch (error) {
    console.error("❌ 修复过程失败:", error.message);
    console.error("详细错误:", error);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
