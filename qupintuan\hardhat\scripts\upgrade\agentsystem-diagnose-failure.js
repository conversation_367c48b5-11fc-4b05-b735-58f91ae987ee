// qupintuan/hardhat/scripts/upgrade/agentsystem-diagnose-failure.js
// 诊断AgentSystem升级失败原因

const { ethers } = require("hardhat");

async function main() {
  console.log("🔍 诊断AgentSystem升级失败原因...\n");

  // 升级信息
  const MULTISIG_ADDRESS = "******************************************";
  const TIMELOCK_ADDRESS = "******************************************";
  const AGENT_SYSTEM_PROXY = "******************************************";
  const NEW_IMPLEMENTATION = "******************************************";
  const SALT = "0x835bfaef1f47108801e75329e3f4dcefd355c3a2a425b1d271f07d1c393dce62";
  
  // 提案ID
  const SCHEDULE_PROPOSAL_ID = 38;
  const EXECUTE_PROPOSAL_ID = 39;

  try {
    console.log("📋 诊断信息:");
    console.log(`   多签钱包: ${MULTISIG_ADDRESS}`);
    console.log(`   Timelock: ${TIMELOCK_ADDRESS}`);
    console.log(`   代理合约: ${AGENT_SYSTEM_PROXY}`);
    console.log(`   新实现: ${NEW_IMPLEMENTATION}`);
    console.log(`   Execute提案ID: ${EXECUTE_PROPOSAL_ID}`);
    console.log("");

    // 连接合约
    const multisig = await ethers.getContractAt("MultiSigWallet", MULTISIG_ADDRESS);
    const timelock = await ethers.getContractAt("TimelockController", TIMELOCK_ADDRESS);
    const agentSystem = await ethers.getContractAt("AgentSystem", AGENT_SYSTEM_PROXY);

    // 1. 检查Execute提案详情
    console.log("1️⃣ 检查Execute提案详情...");
    
    try {
      const executeProposal = await multisig.getTransaction(EXECUTE_PROPOSAL_ID);
      console.log(`   提案ID: ${EXECUTE_PROPOSAL_ID}`);
      console.log(`   目标地址: ${executeProposal.to}`);
      console.log(`   调用值: ${executeProposal.value}`);
      console.log(`   已执行: ${executeProposal.executed}`);
      console.log(`   确认数: ${executeProposal.numConfirmations}`);
      console.log(`   调用数据: ${executeProposal.data}`);
      
    } catch (error) {
      console.log(`   ❌ 获取Execute提案失败: ${error.message}`);
    }

    // 2. 检查Timelock操作状态
    console.log("\n2️⃣ 检查Timelock操作状态...");
    
    try {
      // 重新构建操作参数
      const target = AGENT_SYSTEM_PROXY;
      const value = 0;
      const data = "0x4f1ef2860000000000000000000000006143a4cc8bca739ed4d610f704fcf88974c2188300000000000000000000000000000000000000000000000000000000000000400000000000000000000000000000000000000000000000000000000000000000";
      const predecessor = ethers.ZeroHash;
      const salt = SALT;
      
      const operationId = await timelock.hashOperation(target, value, data, predecessor, salt);
      console.log(`   操作ID: ${operationId}`);
      
      // 检查操作状态
      const isScheduled = await timelock.isOperation(operationId);
      const isPending = await timelock.isOperationPending(operationId);
      const isReady = await timelock.isOperationReady(operationId);
      const isDone = await timelock.isOperationDone(operationId);
      
      console.log(`   已调度: ${isScheduled ? '✅' : '❌'}`);
      console.log(`   等待中: ${isPending ? '✅' : '❌'}`);
      console.log(`   准备就绪: ${isReady ? '✅' : '❌'}`);
      console.log(`   已完成: ${isDone ? '✅' : '❌'}`);
      
      if (isScheduled) {
        const timestamp = await timelock.getTimestamp(operationId);
        const currentTime = Math.floor(Date.now() / 1000);
        const readyTime = Number(timestamp);
        
        console.log(`   调度时间: ${new Date(readyTime * 1000).toLocaleString()}`);
        console.log(`   当前时间: ${new Date(currentTime * 1000).toLocaleString()}`);
        console.log(`   延迟期状态: ${currentTime >= readyTime ? '✅ 已结束' : '❌ 未结束'}`);
      }
      
    } catch (error) {
      console.log(`   ❌ 检查Timelock状态失败: ${error.message}`);
    }

    // 3. 检查代理合约当前状态
    console.log("\n3️⃣ 检查代理合约当前状态...");
    
    try {
      // 检查当前实现
      const implementationSlot = "0x360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc";
      const currentImpl = await ethers.provider.getStorageAt(AGENT_SYSTEM_PROXY, implementationSlot);
      const currentImplAddress = "0x" + currentImpl.slice(-40);
      
      console.log(`   当前实现: ${currentImplAddress}`);
      console.log(`   目标实现: ${NEW_IMPLEMENTATION}`);
      console.log(`   已升级: ${currentImplAddress.toLowerCase() === NEW_IMPLEMENTATION.toLowerCase() ? '✅' : '❌'}`);
      
      // 检查所有者
      const owner = await agentSystem.owner();
      console.log(`   合约所有者: ${owner}`);
      console.log(`   所有者是Timelock: ${owner.toLowerCase() === TIMELOCK_ADDRESS.toLowerCase() ? '✅' : '❌'}`);
      
      // 检查UUPS UUID
      const proxiableUUID = await agentSystem.proxiableUUID();
      console.log(`   UUPS UUID: ${proxiableUUID}`);
      
    } catch (error) {
      console.log(`   ❌ 检查代理合约状态失败: ${error.message}`);
    }

    // 4. 检查新实现合约
    console.log("\n4️⃣ 检查新实现合约...");
    
    try {
      const newImpl = await ethers.getContractAt("AgentSystem", NEW_IMPLEMENTATION);
      
      // 检查新实现的UUPS UUID
      const newProxiableUUID = await newImpl.proxiableUUID();
      console.log(`   新实现UUPS UUID: ${newProxiableUUID}`);
      
      // 检查新实现代码
      const newImplCode = await ethers.provider.getCode(NEW_IMPLEMENTATION);
      console.log(`   新实现代码长度: ${newImplCode.length} 字符`);
      console.log(`   新实现存在: ${newImplCode !== '0x' ? '✅' : '❌'}`);
      
    } catch (error) {
      console.log(`   ❌ 检查新实现合约失败: ${error.message}`);
    }

    // 5. 模拟升级调用
    console.log("\n5️⃣ 模拟升级调用...");
    
    try {
      // 构建升级调用数据
      const upgradeInterface = new ethers.Interface([
        "function upgradeToAndCall(address newImplementation, bytes calldata data)"
      ]);
      
      const upgradeData = upgradeInterface.encodeFunctionData("upgradeToAndCall", [
        NEW_IMPLEMENTATION,
        "0x" // 空的初始化数据
      ]);
      
      console.log(`   升级调用数据: ${upgradeData}`);
      
      // 尝试静态调用来检查是否会失败
      try {
        await ethers.provider.call({
          to: AGENT_SYSTEM_PROXY,
          data: upgradeData,
          from: TIMELOCK_ADDRESS
        });
        console.log(`   ✅ 模拟升级调用成功`);
      } catch (error) {
        console.log(`   ❌ 模拟升级调用失败: ${error.message}`);
        
        // 分析具体错误
        if (error.message.includes("Ownable: caller is not the owner")) {
          console.log(`   原因: Timelock不是合约所有者`);
        } else if (error.message.includes("ERC1967Upgrade: new implementation is not UUPS")) {
          console.log(`   原因: 新实现不是UUPS兼容的`);
        } else if (error.message.includes("Address: low-level delegate call failed")) {
          console.log(`   原因: 委托调用失败`);
        } else {
          console.log(`   原因: 未知错误`);
        }
      }
      
    } catch (error) {
      console.log(`   ❌ 模拟升级失败: ${error.message}`);
    }

    // 6. 检查权限和角色
    console.log("\n6️⃣ 检查权限和角色...");
    
    try {
      // 检查Timelock的角色
      const ADMIN_ROLE = await agentSystem.ADMIN_ROLE();
      const hasAdminRole = await agentSystem.hasRole(ADMIN_ROLE, TIMELOCK_ADDRESS);
      
      console.log(`   管理员角色: ${ADMIN_ROLE}`);
      console.log(`   Timelock有管理员权限: ${hasAdminRole ? '✅' : '❌'}`);
      
      // 检查默认管理员角色
      const DEFAULT_ADMIN_ROLE = "0x0000000000000000000000000000000000000000000000000000000000000000";
      const hasDefaultAdminRole = await agentSystem.hasRole(DEFAULT_ADMIN_ROLE, TIMELOCK_ADDRESS);
      console.log(`   Timelock有默认管理员权限: ${hasDefaultAdminRole ? '✅' : '❌'}`);
      
    } catch (error) {
      console.log(`   ❌ 检查权限失败: ${error.message}`);
    }

    // 7. 分析可能的失败原因
    console.log("\n7️⃣ 分析可能的失败原因...");
    console.log("=".repeat(50));
    
    console.log("\n🔍 可能的失败原因:");
    console.log("1. 权限问题:");
    console.log("   - Timelock不是合约所有者");
    console.log("   - Timelock没有管理员权限");
    console.log("");
    console.log("2. 实现合约问题:");
    console.log("   - 新实现合约不是UUPS兼容的");
    console.log("   - 新实现合约有构造函数错误");
    console.log("   - 存储布局不兼容");
    console.log("");
    console.log("3. 调用数据问题:");
    console.log("   - 升级调用数据格式错误");
    console.log("   - 初始化数据有问题");
    console.log("");
    console.log("4. Timelock问题:");
    console.log("   - 操作未正确调度");
    console.log("   - 延迟期未结束");
    console.log("   - Salt值不匹配");

    // 8. 建议的解决方案
    console.log("\n8️⃣ 建议的解决方案...");
    console.log("-".repeat(40));
    
    console.log("\n🔧 立即检查:");
    console.log("1. 验证Timelock是否有正确的权限");
    console.log("2. 检查新实现合约是否正确部署");
    console.log("3. 验证升级调用数据是否正确");
    console.log("");
    console.log("🔧 可能的修复:");
    console.log("1. 重新部署新实现合约");
    console.log("2. 使用正确的调用数据重新提交提案");
    console.log("3. 检查并修复权限问题");
    console.log("");
    console.log("🔧 下一步:");
    console.log("1. 运行权限检查脚本");
    console.log("2. 重新生成升级提案");
    console.log("3. 使用直接升级方法（如果权限允许）");

  } catch (error) {
    console.error("❌ 诊断失败:", error.message);
    console.error("详细错误:", error);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
