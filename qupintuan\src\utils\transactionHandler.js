// src/utils/transactionHandler.js
/**
 * 优化的交易处理器 - 集成通用Nonce管理和错误处理
 * sendTx: 统一处理链上交易流程，包含结构化错误处理和详细注释
 * @param {Function} txFn - 返回交易Promise的异步函数
 * @param {Object} options - 配置选项
 * @param {Function} options.onTxHash - 交易哈希回调
 * @param {Function} options.onReceipt - 交易收据回调
 * @param {Function} options.onError - 错误处理回调
 * @param {number} [options.timeout=120000] - 交易确认超时时间（毫秒）
 * @param {boolean} [options.useOptimizedNonce=false] - 是否使用优化的Nonce管理
 * @param {string} [options.userAddress] - 用户地址（用于Nonce管理）
 * @param {boolean} [options.isFirstTransaction=false] - 是否是第一笔交易
 * @param {string} [options.operationName='交易'] - 操作名称（用于错误提示）
 * @returns {Promise<Object>} 交易收据
 * @throws {Object} 结构化错误对象 { code: string, message: string, originalError: Error }
 */
export async function sendTx(txFn, options) {
  const DEFAULT_TX_TIMEOUT = 120000; // 默认交易确认超时时间（毫秒）

  // 输入验证
  if (typeof txFn !== 'function') {
    throw new Error('txFn必须是一个返回Promise的函数');
  }

  const {
    onTxHash,
    onReceipt,
    onError,
    timeout = DEFAULT_TX_TIMEOUT,
    useOptimizedNonce = false,
    userAddress,
    isFirstTransaction = false,
    operationName = '交易',
  } = options || {};

  // 如果启用了优化的Nonce管理，使用新的处理方式
  if (useOptimizedNonce && userAddress) {
    return sendTxWithOptimizedNonce(txFn, options);
  }

  // 回调函数类型校验
  if (typeof onTxHash !== 'function' ||
      typeof onReceipt !== 'function' ||
      typeof onError !== 'function') {
    throw new Error('sendTx: onTxHash, onReceipt和onOnError回调都是必需的');
  }

  // 超时时间有效性校验
  if (typeof timeout !== 'number' || timeout <= 0) {
    throw new Error('timeout必须是大于0的数字');
  }

  let tx;
  try {
    tx = await txFn();

    // 处理交易对象

    // 如果 tx 是字符串（交易哈希），直接返回
    if (typeof tx === 'string') {
      onTxHash(tx);

      // 使用 provider 等待交易确认
      const ethers = await import('ethers');
      const { BrowserProvider } = ethers;
      const provider = new BrowserProvider(window.ethereum);

      const receipt = await provider.waitForTransaction(tx, 1, timeout);
      onReceipt(receipt);
      return receipt;
    }

    // 如果 tx 是一个包含 receipt 的结果对象（来自 groupBuyApi）
    if (tx && tx.receipt && !tx.wait) {
      console.log('🔍 [sendTx] 处理包含 receipt 的结果对象:', {
        tx,
        hasRoomId: tx.roomId !== undefined,
        hasReceipt: tx.receipt !== undefined,
        receiptHash: tx.receipt?.hash
      });
      onTxHash(tx.receipt.hash);
      onReceipt(tx.receipt);
      return tx; // 返回完整的结果对象
    }

    // 如果 tx 没有 wait 方法，但有 hash，尝试使用 provider 等待
    if (!tx?.wait && tx?.hash) {
      onTxHash(tx.hash);

      const ethers = await import('ethers');
      const { BrowserProvider } = ethers;
      const provider = new BrowserProvider(window.ethereum);

      const receipt = await provider.waitForTransaction(tx.hash, 1, timeout);
      onReceipt(receipt);
      return receipt;
    }

    // 传统的 ethers 交易对象
    if (typeof tx?.wait !== 'function') {
      throw new Error('交易对象缺少 .wait() 方法，且不是有效的交易哈希');
    }

  } catch (err) {
    console.error('交易发送失败', err);
    onError({
      code: 'TX_SEND_FAILED',
      message: '无法发送交易，请检查网络连接或钱包配置',
      originalError: err
    });
    throw {
      code: 'TX_SEND_FAILED',
      message: '无法发送交易，请检查网络连接或钱包配置',
      originalError: err
    };
  }

  // 传统的 ethers 交易对象处理
  // 开发环境才输出日志
  if (import.meta.env.MODE === 'development') {
    console.info('tx hash:', tx.hash);
  }

  onTxHash(tx.hash);

  let receipt;
  try {
    const abortController = new AbortController();
    const timeoutId = setTimeout(() => abortController.abort(), timeout);

    try {
      receipt = await tx.wait();

      // 创建可变副本，避免修改只读对象
      const receiptCopy = {
        ...receipt,
        logs: receipt.logs || []
      };

      clearTimeout(timeoutId);
      onReceipt(receiptCopy);
      return receiptCopy;
    } catch (err) {
      clearTimeout(timeoutId);
      if (err.name === 'AbortError') {
        const timeoutError = {
          code: 'TX_CONFIRM_TIMEOUT',
          message: `交易确认超时(${timeout}ms)，请稍后手动检查交易状态。交易哈希: ${tx?.hash || '未知'}`,
          originalError: err
        };
        console.error(timeoutError.message, err);
        onError(timeoutError);
        throw timeoutError;
      }
      // 增强错误输出：打印 tx.hash 以便后续查询
      const wrappedError = {
        code: 'TX_EXECUTION_FAILURE',
        message: `交易执行失败，请检查链上状态或 gas 配置。交易哈希: ${tx?.hash || '未知'}`,
        originalError: err
      };
      console.error(wrappedError.message, wrappedError.originalError);
      onError(wrappedError);
      throw wrappedError;
    }
  } catch (err) {
    console.error('交易执行失败', err);
    const executionError = {
      code: 'TX_EXECUTION_FAILED',
      message: '交易执行失败，请检查交易是否被拒绝或gas不足',
      originalError: err
    };
    onError(executionError);
    throw executionError;
  }
}

// 默认等待 120 秒再判定为超时
const DEFAULT_WAIT_TIMEOUT = 120 * 1000; // 120s

import { logDebug, logError } from '@/services/logger';

/**
 * 等待交易确认
 * @param {Object} tx - 交易对象
 * @param {number} [timeout=60000] - 超时时间（毫秒）
 * @returns {Promise<Object>} 交易收据
 * @throws {Error} 如果超时或交易失败
 */
export async function waitForTransaction(tx, timeout = DEFAULT_WAIT_TIMEOUT) {
  const abortController = new AbortController();
  const timeoutId = setTimeout(() => abortController.abort(), timeout);

  try {
    // 使用轮询方式替代原生 .wait()
    let receipt = null;
    const pollInterval = 5000; // 每 5 秒查询一次
    const startTime = Date.now();

    while (!receipt && Date.now() - startTime < timeout) {
      try {
        receipt = await tx.provider.getTransactionReceipt(tx.hash);
        if (receipt) {
          clearTimeout(timeoutId);
          return receipt;
        }
      } catch (err) {
        console.warn('⚠️ getTransactionReceipt error:', err.message);
      }

      await new Promise(resolve => setTimeout(resolve, pollInterval));
    }

    clearTimeout(timeoutId);
    throw new Error(`交易确认超时(${timeout}ms)，请稍后手动检查交易状态。交易哈希: ${tx?.hash || '未知'}`);
  } catch (err) {
    clearTimeout(timeoutId);
    if (err.name === 'AbortError') {
      throw new Error(`交易确认中止(${timeout}ms)，请重试。交易哈希: ${tx?.hash || '未知'}`);
    }

    // 即使 wait 报错，也尝试获取链上最终交易状态
    try {
      const finalReceipt = await tx.provider.getTransactionReceipt(tx.hash);
      if (finalReceipt?.status === 1) {
        logDebug({
          component: 'transactionHandler',
          function: 'waitForTransaction',
          message: '交易最终成功上链，但等待超时',
          receipt: finalReceipt
        });
        return finalReceipt;
      } else {
        logError({
          component: 'transactionHandler',
          function: 'waitForTransaction',
          message: '交易最终失败或未被打包',
          receipt: finalReceipt
        });
        throw err;
      }
    } catch (checkErr) {
      logError({
        component: 'transactionHandler',
        function: 'waitForTransaction',
        message: '无法获取最终交易状态',
        error: checkErr
      });
      throw err;
    }
  }
}

/**
 * 使用优化Nonce管理的交易发送函数
 * @param {Function} txFn - 交易执行函数
 * @param {Object} options - 选项
 * @returns {Promise<Object>} 交易结果
 */
async function sendTxWithOptimizedNonce(txFn, options) {
  const {
    onTxHash,
    onReceipt,
    onError,
    timeout = 120000,
    userAddress,
    isFirstTransaction = false,
    operationName = '交易',
  } = options;

  // 动态导入优化模块
  const { universalNonceManager } = await import('./universalNonceManager.js');



  try {
    // 使用通用Nonce管理器执行交易
    const result = await universalNonceManager.executeTransaction(
      userAddress,
      async ({ nonce }) => {
        // 获取优化的Gas配置
        const gasConfig = universalNonceManager.getOptimizedGasConfig({}, isFirstTransaction);

        // 执行原始交易函数，传入Nonce和Gas配置
        return await txFn({ nonce, gasConfig });
      },
      {
        highPriority: isFirstTransaction,
        maxRetries: 3,
        retryDelay: 3000,
        onProgress: (progress) => {
          console.log('📊 [sendTxWithOptimizedNonce] 交易进度:', progress);
        },
        onNonceConflict: (conflictInfo) => {
          console.warn('⚠️ [sendTxWithOptimizedNonce] Nonce冲突:', conflictInfo);
        }
      }
    );

    // 处理交易结果
    const txHash = result.hash || result.txHash || result;

    if (typeof txHash === 'string') {

      if (onTxHash) {
        onTxHash(txHash);
      }

      // 等待交易确认
      const ethers = await import('ethers');
      const { BrowserProvider } = ethers;
      const provider = new BrowserProvider(window.ethereum);

      const receipt = await provider.waitForTransaction(txHash, 1, timeout);

      if (onReceipt) {
        onReceipt(receipt);
      }

      // 标记交易完成
      universalNonceManager.markTransactionComplete(userAddress, txHash);

      return receipt;
    } else {
      // 处理其他类型的返回结果
      if (result.receipt) {
        if (onTxHash) {
          onTxHash(result.receipt.hash);
        }
        if (onReceipt) {
          onReceipt(result.receipt);
        }
        universalNonceManager.markTransactionComplete(userAddress, result.receipt.hash);
        return result;
      }

      throw new Error('无效的交易结果格式');
    }

  } catch (error) {

    // 调用错误回调
    if (onError) {
      onError({
        code: 'OPTIMIZED_TX_FAILED',
        message: error.message,
        originalError: error
      });
    }

    throw error;
  }
}

/**
 * 创建优化的交易执行函数
 * @param {string} userAddress - 用户地址
 * @param {boolean} isFirstTransaction - 是否是第一笔交易
 * @param {string} operationName - 操作名称
 * @returns {Function} 优化的sendTx函数
 */
export function createOptimizedSendTx(userAddress, isFirstTransaction = false, operationName = '交易') {
  return (txFn, options = {}) => {
    return sendTx(txFn, {
      ...options,
      useOptimizedNonce: true,
      userAddress,
      isFirstTransaction,
      operationName
    });
  };
}
