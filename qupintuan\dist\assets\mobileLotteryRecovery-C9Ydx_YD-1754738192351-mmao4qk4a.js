const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/web3-DJUNf-KT-1754738192351-mmao4qk4a.js","assets/vendor-Caz4khA--1754738192351-mmao4qk4a.js","assets/index-DmsQauDE-1754738192351-rjh9jemju.js","assets/index-Dcug_PHz-1754738192351-gaytee33w.css"])))=>i.map(i=>d[i]);
import{_ as s}from"./vendor-Caz4khA--1754738192351-mmao4qk4a.js";function p(){const t=navigator.userAgent,e=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(t),r=window.ethereum?.isTokenPocket,a=window.ethereum?.isTrust,n=window.ethereum?.isImToken,o=window.ethereum?.isMathWallet;return e||r||a||n||o}async function H(t,e=null){const r=[()=>f(t),()=>d(t),()=>m(t),()=>g(t),()=>v(t),()=>w(t),()=>h(e)];for(const[a,n]of r.entries())try{const o=await n();if(o&&o.txHash&&o.winner)return o}catch{}return null}async function w(t){const{createPublicClient:e,http:r}=await s(async()=>{const{createPublicClient:c,http:u}=await import("./web3-DJUNf-KT-1754738192351-mmao4qk4a.js").then(y=>y.x);return{createPublicClient:c,http:u}},__vite__mapDeps([0,1])),{bscTestnet:a}=await s(async()=>{const{bscTestnet:c}=await import("./web3-DJUNf-KT-1754738192351-mmao4qk4a.js").then(u=>u.A);return{bscTestnet:c}},__vite__mapDeps([0,1])),{getContractAddress:n}=await s(async()=>{const{getContractAddress:c}=await import("./index-DmsQauDE-1754738192351-rjh9jemju.js").then(u=>u.j);return{getContractAddress:c}},__vite__mapDeps([2,1,0,3])),{ABIS:o}=await s(async()=>{const{ABIS:c}=await import("./index-DmsQauDE-1754738192351-rjh9jemju.js").then(u=>u.k);return{ABIS:c}},__vite__mapDeps([2,1,0,3])),l=e({chain:a,transport:r()}),_=n(97,"GroupBuyRoom"),i=await l.readContract({address:_,abi:o.GroupBuyRoom,functionName:"getLotteryInfo",args:[BigInt(t)]});return i&&i[2]!=="0x0000000000000000000000000000000000000000000000000000000000000000"?{txHash:i[2],winner:{address:i[1],winnerAddress:i[1],index:Number(i[4])+1,source:"contract"},timestamp:Number(i[3]),source:"contract"}:null}function h(t){if(!t)return null;const e=t.lotteryTxHash,r=t.calculatedWinner||t.winnerInfo||t.winner,a=t.lotteryTimestamp;return e&&e!=="0x0000000000000000000000000000000000000000000000000000000000000000"&&r?{txHash:e,winner:r,timestamp:a,source:"roomObject"}:null}function f(t){try{const e=localStorage.getItem(`lottery_${t}`);if(e){const r=JSON.parse(e);if(r.txHash&&r.winner)return{...r,source:"localStorage"}}}catch{}return null}function d(t){try{const e=sessionStorage.getItem(`lottery_session_${t}`);if(e){const r=JSON.parse(e);if(r.txHash&&r.winner)return{...r,source:"sessionStorage"}}}catch{}return null}function m(t){try{if(window._lotteryHashData&&window._lotteryHashData[t]){const e=window._lotteryHashData[t];if(e.txHash&&e.winner)return{...e,source:"memoryHash"}}}catch{}return null}async function g(t){try{const{getLotteryInfo:e}=await s(async()=>{const{getLotteryInfo:a}=await import("./lotteryInfoPersistence-BBzWo8Bu-1754738192351-mmao4qk4a.js");return{getLotteryInfo:a}},[]),r=e(t);if(r&&r.txHash&&r.winner)return{...r,source:"persistentStorage"}}catch{}return null}async function v(t){try{const{loadLotteryInfo:e}=await s(async()=>{const{loadLotteryInfo:a}=await import("./lotteryStorage-DXlHLTdr-1754738192351-mmao4qk4a.js");return{loadLotteryInfo:a}},[]),r=await e(t);if(r&&r.txHash&&r.winner)return{...r,source:"multiStorage"}}catch{}return null}async function L(t,e){const r={localStorage:!1,sessionStorage:!1,memoryHash:!1,persistentStorage:!1,multiStorage:!1};try{localStorage.setItem(`lottery_${t}`,JSON.stringify(e)),r.localStorage=!0}catch{}try{sessionStorage.setItem(`lottery_session_${t}`,JSON.stringify(e)),r.sessionStorage=!0}catch{}try{window._lotteryHashData=window._lotteryHashData||{},window._lotteryHashData[t]=e,r.memoryHash=!0}catch{}try{const{saveLotteryInfo:n}=await s(async()=>{const{saveLotteryInfo:o}=await import("./lotteryInfoPersistence-BBzWo8Bu-1754738192351-mmao4qk4a.js");return{saveLotteryInfo:o}},[]);n(t,e),r.persistentStorage=!0}catch{}try{const{saveLotteryInfo:n}=await s(async()=>{const{saveLotteryInfo:o}=await import("./lotteryStorage-DXlHLTdr-1754738192351-mmao4qk4a.js");return{saveLotteryInfo:o}},[]);await n(t,e),r.multiStorage=!0}catch{}return Object.values(r).filter(Boolean).length>0}export{p as isMobileEnvironment,H as recoverLotteryDataMobile,L as saveLotteryDataMobile};
