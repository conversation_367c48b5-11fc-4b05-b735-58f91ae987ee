const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index-DmsQauDE-1754738192351-rjh9jemju.js","assets/vendor-Caz4khA--1754738192351-mmao4qk4a.js","assets/web3-DJUNf-KT-1754738192351-mmao4qk4a.js","assets/index-Dcug_PHz-1754738192351-gaytee33w.css"])))=>i.map(i=>d[i]);
import{_ as u}from"./vendor-Caz4khA--1754738192351-mmao4qk4a.js";import{a as m,b as l}from"./index-DmsQauDE-1754738192351-rjh9jemju.js";import{r as c}from"./web3-DJUNf-KT-1754738192351-mmao4qk4a.js";async function i(){try{const{contractAddress:t,contractABI:s}=await m("AgentSystem"),e=await l(),o=await c(e,{address:t,abi:s,functionName:"systemAdmin"});try{const r=await c(e,{address:t,abi:s,functionName:"getTeamStats",args:[o]}),a=Number(r[1]),n=a+1;if(n>1)return{success:!0,totalUsers:n,method:"getTeamStats",details:{teamMembers:a,systemAdmin:1}}}catch{}try{const r=await c(e,{address:t,abi:s,functionName:"getTeamMemberCount",args:[o,20]}),a=Number(r),n=a+1;if(n>1)return{success:!0,totalUsers:n,method:"getTeamMemberCount",details:{teamMembers:a,systemAdmin:1}}}catch{}try{const r=await c(e,{address:t,abi:s,functionName:"getUserReferrals",args:[o]});return{success:!0,totalUsers:1+r.length+r.length*2,method:"estimation",details:{directReferrals:r.length,systemAdmin:1,estimated:!0}}}catch{}return{success:!1,totalUsers:1,method:"fallback",error:"所有统计方法都失败"}}catch(t){return{success:!1,totalUsers:1,method:"error",error:t.message}}}async function d(){try{const t=await i(),{getTotalUsers:s}=await u(async()=>{const{getTotalUsers:r}=await import("./index-DmsQauDE-1754738192351-rjh9jemju.js").then(a=>a.u);return{getTotalUsers:r}},__vite__mapDeps([0,1,2,3])),e=await s(!1);return{quickCount:t.totalUsers,apiCount:e,difference:Math.abs(t.totalUsers-e),quickMethod:t.method,isAccurate:t.success&&t.totalUsers>e,recommendation:t.success&&t.totalUsers>1?"建议使用快速统计结果":"需要进一步调试",timestamp:new Date().toISOString()}}catch(t){return{error:t.message,timestamp:new Date().toISOString()}}}async function f(){try{const t=await i();if(t.success&&t.totalUsers>1){const s="accurate_user_count",e={count:t.totalUsers,method:t.method,timestamp:Date.now(),expires:Date.now()+3e5};try{localStorage.setItem(s,JSON.stringify(e))}catch{}return t.totalUsers}else return 1}catch{return 1}}function g(){try{const t="accurate_user_count",s=localStorage.getItem(t);if(s){const e=JSON.parse(s);if(e.expires>Date.now())return e.count;localStorage.removeItem(t)}return null}catch{return null}}typeof window<"u"&&(window.getQuickUserCount=i,window.validateUserCount=d,window.fixUserCountAPI=f,window.getCachedUserCount=g);export{f as fixUserCountAPI,g as getCachedUserCount,i as getQuickUserCount,d as validateUserCount};
