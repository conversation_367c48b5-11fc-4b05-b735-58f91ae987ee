{"timestamp": "2025-08-09T14:41:42.691Z", "network": "bscTestnet", "multisigAddress": "0x85D2a947B0dA2c73a4342A2656a142B16CD71CFb", "proxyAddress": "0x9096769B22B53A464D40265420b9Ed5342b6ACb3", "newImplementation": "0x1895Fe4d13dC43b9FFa9b2eE2E020023C16f42c2", "transactionId": 41, "upgradeFunction": "upgradeTo(address)", "functionSelector": "0x3659cfe6", "upgradeCalldata": "0x3659cfe60000000000000000000000001895fe4d13dc43b9ffa9b2ee2e020023c16f42c2", "submitter": "0x012F049B3947C45C400AF5909c5b629BEac0C0b3", "submitTxHash": "0xf75a0d90076692e9f6fa436471919625c27998e7f4f5635ec38f7b0417bd1916", "differences": {"previousAttempt": "使用upgradeToAndCall函数失败", "currentAttempt": "使用upgradeTo函数", "reason": "参考成功的ProductManagement升级脚本"}}