const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index-DmsQauDE-*************-rjh9jemju.js","assets/vendor-Caz4khA--*************-mmao4qk4a.js","assets/web3-DJUNf-KT-*************-mmao4qk4a.js","assets/index-Dcug_PHz-*************-gaytee33w.css"])))=>i.map(i=>d[i]);
import{_ as p,P as i}from"./vendor-Caz4khA--*************-mmao4qk4a.js";import{c as w,h as S,b as u,d as T,e as A}from"./web3-DJUNf-KT-*************-mmao4qk4a.js";const y=async t=>{try{if(!t||typeof t!="string")return console.warn("getUserAgentInfo: 无效的用户地址参数:",t),{level:0,rewardPercent:5};const{CONTRACT_ADDRESSES:a,ABIS:n}=await p(async()=>{const{CONTRACT_ADDRESSES:s,ABIS:l}=await import("./index-DmsQauDE-*************-rjh9jemju.js").then(b=>b.k);return{CONTRACT_ADDRESSES:s,ABIS:l}},__vite__mapDeps([0,1,2,3])),e=a[97].AgentSystem,c=await w({chain:u,transport:S()}).readContract({address:e,abi:n.AgentSystem,functionName:"getLevel",args:[t]}),o=5+Number(c)*5;return{level:Number(c),rewardPercent:o}}catch(a){return console.warn("获取代理信息失败:",a),{level:0,rewardPercent:5}}},f=async(t,a)=>{if(!a)return!1;try{const{CONTRACT_ADDRESSES:n,ABIS:e}=await p(async()=>{const{CONTRACT_ADDRESSES:l,ABIS:b}=await import("./index-DmsQauDE-*************-rjh9jemju.js").then(d=>d.k);return{CONTRACT_ADDRESSES:l,ABIS:b}},__vite__mapDeps([0,1,2,3])),r=T({chain:u,transport:A(window.ethereum)}),c=n[97].QPTBuyback,o=await r.writeContract({address:c,abi:e.QPTBuyback,functionName:"finalizeTimeout",args:[BigInt(t)],account:a});return i.success("处理过期房间交易已提交，等待确认..."),await w({chain:u,transport:S("https://bsc-testnet.public.blastapi.io")}).waitForTransactionReceipt({hash:o}),i.success(`房间 #${t} 过期处理成功！`),!0}catch(n){return console.error("处理过期房间失败:",n),i.error("处理过期房间失败: "+(n.message||"未知错误")),!1}},E=async(t,a,n)=>{try{const{CONTRACT_ADDRESSES:e,ABIS:r}=await p(async()=>{const{CONTRACT_ADDRESSES:d,ABIS:h}=await import("./index-DmsQauDE-*************-rjh9jemju.js").then(C=>C.k);return{CONTRACT_ADDRESSES:d,ABIS:h}},__vite__mapDeps([0,1,2,3])),c=T({chain:u,transport:A(window.ethereum)}),o=e[97].QPTBuyback;let s;switch(n){case"failed":s="refundFailed";break;case"success":s="refundSuccess";break;case"expired":s="refundExpired";break;default:throw new Error("未知的退款类型")}const l=await c.writeContract({address:o,abi:r.QPTBuyback,functionName:s,args:[BigInt(t)],account:a});return i.success("退款交易已提交，等待确认..."),await w({chain:u,transport:S("https://bsc-testnet.public.blastapi.io")}).waitForTransactionReceipt({hash:l}),i.success(`房间 #${t} QPT退款成功！`),!0}catch(e){console.error("QPT退款失败:",e);let r="QPT退款失败";throw e.message.includes("User rejected")?r="用户取消了交易":e.message.includes("Already refunded")?r="已经退款过了":e.message.includes("Not a participant")&&(r="您不是此房间的参与者"),i.error(r),e}},g=f;export{g as finalizeTimeoutRoom,y as getUserAgentInfo,f as handleExpireRoom,E as refundQPTBuyback};
